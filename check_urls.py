#!/usr/bin/env python
"""
فحص جميع URLs المستخدمة في القائمة الجانبية
Check all URLs used in the sidebar
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'field_sales_tracker.settings')
django.setup()

from django.urls import reverse, NoReverseMatch
from django.test import RequestFactory
from django.contrib.auth import get_user_model

User = get_user_model()

def check_urls():
    """فحص جميع URLs المستخدمة في القائمة الجانبية"""
    
    urls_to_check = [
        # Dashboard URLs
        ('dashboard:home', {}),
        
        # Categories URLs
        ('categories:dashboard', {}),
        ('categories:list', {}),
        ('categories:list_by_type', {'category_type': 1}),
        ('categories:list_by_type', {'category_type': 2}),
        ('categories:list_by_type', {'category_type': 3}),
        
        # Visits URLs
        ('visits:visits_list', {}),
        ('visits:pending_visits', {}),
        ('visits:my_visits', {}),
        ('visits:add_visit', {}),
        ('visits:task_management', {}),
        
        # Clients URLs
        ('clients:clients_list', {}),
        ('clients:add_client', {}),
        ('clients:bulk_add_clients', {}),
        
        # Users URLs
        ('users:users_list', {}),
        ('users:sales_representatives', {}),
        ('users:add_user', {}),
        ('users:hierarchy_management', {}),
        ('users:hierarchical_reports', {}),
        ('users:logout', {}),
    ]
    
    print("🔍 فحص URLs المستخدمة في القائمة الجانبية...")
    print("=" * 60)
    
    working_urls = []
    broken_urls = []
    
    for url_name, kwargs in urls_to_check:
        try:
            url = reverse(url_name, kwargs=kwargs)
            working_urls.append((url_name, url))
            print(f"✅ {url_name:<30} -> {url}")
        except NoReverseMatch as e:
            broken_urls.append((url_name, str(e)))
            print(f"❌ {url_name:<30} -> خطأ: {e}")
        except Exception as e:
            broken_urls.append((url_name, str(e)))
            print(f"⚠️  {url_name:<30} -> خطأ غير متوقع: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج:")
    print(f"   ✅ URLs تعمل بشكل صحيح: {len(working_urls)}")
    print(f"   ❌ URLs بها مشاكل: {len(broken_urls)}")
    
    if broken_urls:
        print(f"\n🔧 URLs التي تحتاج إصلاح:")
        for url_name, error in broken_urls:
            print(f"   - {url_name}: {error}")
    
    return len(broken_urls) == 0

def check_permissions():
    """فحص الصلاحيات المستخدمة في القائمة الجانبية"""
    
    print("\n🔐 فحص الصلاحيات المستخدمة...")
    print("=" * 60)
    
    # إنشاء مستخدم تجريبي
    try:
        user = User.objects.filter(username='test_user').first()
        if not user:
            user = User.objects.create_user(
                username='test_user',
                email='<EMAIL>',
                password='test123',
                role_type=1  # مدير عام
            )
        
        # فحص الصلاحيات
        permissions_to_check = [
            'is_super_manager',
            'is_user_manager', 
            'is_sales_rep',
            'has_permission',
        ]
        
        for permission in permissions_to_check:
            if hasattr(user, permission):
                if callable(getattr(user, permission)):
                    result = getattr(user, permission)()
                else:
                    result = getattr(user, permission)
                print(f"✅ {permission:<25} -> {result}")
            else:
                print(f"❌ {permission:<25} -> غير موجود")
        
        # فحص صلاحيات محددة
        if hasattr(user, 'has_permission'):
            specific_permissions = [
                'can_manage_categories',
                'can_manage_users',
                'can_manage_clients',
                'can_approve_visits',
                'can_view_reports',
                'can_manage_tasks',
            ]
            
            print(f"\n📋 الصلاحيات المحددة:")
            for perm in specific_permissions:
                try:
                    result = user.has_permission(perm)
                    print(f"   {perm:<25} -> {result}")
                except Exception as e:
                    print(f"   {perm:<25} -> خطأ: {e}")
        
    except Exception as e:
        print(f"❌ خطأ في فحص الصلاحيات: {e}")

def check_templates():
    """فحص وجود Templates المطلوبة"""
    
    print("\n📄 فحص Templates المطلوبة...")
    print("=" * 60)
    
    templates_to_check = [
        'templates/base.html',
        'templates/dashboard/base.html',
        'dashboard/templates/dashboard/super_manager_dashboard.html',
        'dashboard/templates/dashboard/manager_dashboard.html',
        'dashboard/templates/dashboard/sales_rep_dashboard.html',
        'categories/templates/categories/dashboard.html',
        'categories/templates/categories/users.html',
        'categories/templates/categories/create.html',
        'categories/templates/categories/list.html',
        'static/css/sidebar-enhancements.css',
        'static/js/sidebar-enhancements.js',
    ]
    
    for template_path in templates_to_check:
        if os.path.exists(template_path):
            print(f"✅ {template_path}")
        else:
            print(f"❌ {template_path} - غير موجود")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء فحص النظام...")
    print("=" * 60)
    
    # فحص URLs
    urls_ok = check_urls()
    
    # فحص الصلاحيات
    check_permissions()
    
    # فحص Templates
    check_templates()
    
    print("\n" + "=" * 60)
    if urls_ok:
        print("🎉 جميع URLs تعمل بشكل صحيح!")
        print("✅ النظام جاهز للاستخدام")
    else:
        print("⚠️  هناك بعض المشاكل في URLs")
        print("🔧 يرجى إصلاح المشاكل المذكورة أعلاه")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
