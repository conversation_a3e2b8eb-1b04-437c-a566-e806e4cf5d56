<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_enhanced_task" modulePackage="com.company.fieldsalestracker" filePath="app\src\main\res\layout\item_enhanced_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/cardView"><Targets><Target id="@+id/cardView" tag="layout/item_enhanced_task_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="135" endOffset="35"/></Target><Target id="@+id/taskTypeIcon" view="ImageView"><Expressions/><location startLine="27" startOffset="12" endLine="32" endOffset="48"/></Target><Target id="@+id/titleText" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="43" endOffset="58"/></Target><Target id="@+id/statusIcon" view="ImageView"><Expressions/><location startLine="46" startOffset="12" endLine="51" endOffset="50"/></Target><Target id="@+id/clientText" view="TextView"><Expressions/><location startLine="56" startOffset="8" endLine="65" endOffset="43"/></Target><Target id="@+id/taskTypeText" view="TextView"><Expressions/><location startLine="74" startOffset="12" endLine="81" endOffset="64"/></Target><Target id="@+id/priorityIcon" view="ImageView"><Expressions/><location startLine="84" startOffset="12" endLine="89" endOffset="48"/></Target><Target id="@+id/priorityText" view="TextView"><Expressions/><location startLine="91" startOffset="12" endLine="96" endOffset="41"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="101" startOffset="8" endLine="107" endOffset="47"/></Target><Target id="@+id/dueDateText" view="TextView"><Expressions/><location startLine="110" startOffset="8" endLine="119" endOffset="43"/></Target><Target id="@+id/descriptionText" view="TextView"><Expressions/><location startLine="122" startOffset="8" endLine="131" endOffset="37"/></Target></Targets></Layout>