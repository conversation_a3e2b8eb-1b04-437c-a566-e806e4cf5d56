package com.company.fieldsalestracker;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import java.util.ArrayList;
import java.util.List;

public class TasksActivity extends AppCompatActivity {
    
    private RecyclerView recyclerView;
    private TaskAdapter taskAdapter;
    private SwipeRefreshLayout swipeRefreshLayout;
    private FloatingActionButton fabRefresh;
    private List<ApiService.Task> taskList = new ArrayList<>();
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_tasks);
        
        initViews();
        setupRecyclerView();
        loadTasks();
    }
    
    private void initViews() {
        recyclerView = findViewById(R.id.recyclerViewTasks);
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout);
        fabRefresh = findViewById(R.id.fabRefresh);
        
        // Setup toolbar
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("المهام المكلف بها");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        // Setup refresh
        swipeRefreshLayout.setOnRefreshListener(this::loadTasks);
        fabRefresh.setOnClickListener(v -> loadTasks());
    }
    
    private void setupRecyclerView() {
        taskAdapter = new TaskAdapter(taskList, new TaskAdapter.OnTaskClickListener() {
            @Override
            public void onTaskClick(ApiService.Task task) {
                openTaskDetails(task);
            }
            
            @Override
            public void onAcknowledgeClick(ApiService.Task task) {
                acknowledgeTask(task);
            }
            
            @Override
            public void onStartClick(ApiService.Task task) {
                startTask(task);
            }
        });
        
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(taskAdapter);
    }
    
    private void loadTasks() {
        swipeRefreshLayout.setRefreshing(true);
        
        ApiService apiService = ApiClient.getApiService();
        Call<ApiService.TasksResponse> call = apiService.getMyTasks();
        
        call.enqueue(new Callback<ApiService.TasksResponse>() {
            @Override
            public void onResponse(Call<ApiService.TasksResponse> call, Response<ApiService.TasksResponse> response) {
                swipeRefreshLayout.setRefreshing(false);
                
                if (response.isSuccessful() && response.body() != null) {
                    ApiService.TasksResponse tasksResponse = response.body();
                    if (tasksResponse.success) {
                        taskList.clear();
                        taskList.addAll(tasksResponse.tasks);
                        taskAdapter.notifyDataSetChanged();
                        
                        AppLogger.log("Tasks loaded: " + taskList.size());
                    } else {
                        Toast.makeText(TasksActivity.this, "فشل في جلب المهام", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(TasksActivity.this, "خطأ في الاتصال", Toast.LENGTH_SHORT).show();
                    AppLogger.log("Error loading tasks: " + response.code());
                }
            }
            
            @Override
            public void onFailure(Call<ApiService.TasksResponse> call, Throwable t) {
                swipeRefreshLayout.setRefreshing(false);
                Toast.makeText(TasksActivity.this, "خطأ في الشبكة", Toast.LENGTH_SHORT).show();
                AppLogger.log("Network error loading tasks: " + t.getMessage());
            }
        });
    }
    
    private void acknowledgeTask(ApiService.Task task) {
        ApiService apiService = ApiClient.getApiService();
        Call<ApiService.TaskActionResponse> call = apiService.acknowledgeTask(task.id);
        
        call.enqueue(new Callback<ApiService.TaskActionResponse>() {
            @Override
            public void onResponse(Call<ApiService.TaskActionResponse> call, Response<ApiService.TaskActionResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiService.TaskActionResponse actionResponse = response.body();
                    if (actionResponse.success) {
                        Toast.makeText(TasksActivity.this, actionResponse.message, Toast.LENGTH_SHORT).show();
                        loadTasks(); // Refresh the list
                    } else {
                        Toast.makeText(TasksActivity.this, "فشل في تأكيد الاطلاع", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(TasksActivity.this, "خطأ في الاتصال", Toast.LENGTH_SHORT).show();
                }
            }
            
            @Override
            public void onFailure(Call<ApiService.TaskActionResponse> call, Throwable t) {
                Toast.makeText(TasksActivity.this, "خطأ في الشبكة", Toast.LENGTH_SHORT).show();
                AppLogger.e("Error","Network error acknowledging task: " + t.getMessage());
            }
        });
    }
    
    private void startTask(ApiService.Task task) {
        ApiService apiService = ApiClient.getApiService();
        Call<ApiService.TaskActionResponse> call = apiService.startTask(task.id);
        
        call.enqueue(new Callback<ApiService.TaskActionResponse>() {
            @Override
            public void onResponse(Call<ApiService.TaskActionResponse> call, Response<ApiService.TaskActionResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiService.TaskActionResponse actionResponse = response.body();
                    if (actionResponse.success) {
                        Toast.makeText(TasksActivity.this, actionResponse.message, Toast.LENGTH_SHORT).show();
                        
                        // Open barcode scanner for this task
                        Intent intent = new Intent(TasksActivity.this, BarcodeScannerActivity.class);
                        intent.putExtra("task_id", task.id);
                        intent.putExtra("client_id", task.client.id);
                        intent.putExtra("client_name", task.client.name);
                        startActivity(intent);
                        
                    } else {
                        Toast.makeText(TasksActivity.this, "فشل في بدء المهمة", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(TasksActivity.this, "خطأ في الاتصال", Toast.LENGTH_SHORT).show();
                }
            }
            
            @Override
            public void onFailure(Call<ApiService.TaskActionResponse> call, Throwable t) {
                Toast.makeText(TasksActivity.this, "خطأ في الشبكة", Toast.LENGTH_SHORT).show();
                AppLogger.log("Network error starting task: " + t.getMessage());
            }
        });
    }
    
    private void openTaskDetails(ApiService.Task task) {
        Intent intent = new Intent(this, TaskDetailsActivity.class);
        intent.putExtra("task_id", task.id);
        startActivity(intent);
    }
    
    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        loadTasks(); // Refresh tasks when returning to this activity
    }
}
