#!/usr/bin/env python3
"""
اختبار API endpoints للتطبيق المحمول
"""

import json
import urllib.request
import urllib.error

# إعدادات الاختبار
BASE_URL = "http://localhost:9000"
TOKEN = "****************************************"

def test_api_endpoint(endpoint, method="GET", data=None):
    """اختبار API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    # إعداد الطلب
    headers = {
        'Authorization': f'Bearer {TOKEN}',
        'Content-Type': 'application/json'
    }
    
    try:
        if method == "GET":
            req = urllib.request.Request(url, headers=headers)
        else:
            req = urllib.request.Request(
                url, 
                data=json.dumps(data).encode('utf-8') if data else None,
                headers=headers,
                method=method
            )
        
        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            print(f"✅ {method} {endpoint}")
            print(f"Status: {response.status}")
            print(f"Response: {result[:200]}...")
            print("-" * 50)
            return True
            
    except urllib.error.HTTPError as e:
        print(f"❌ {method} {endpoint}")
        print(f"Status: {e.code}")
        print(f"Error: {e.read().decode('utf-8')[:200]}...")
        print("-" * 50)
        return False
    except Exception as e:
        print(f"❌ {method} {endpoint}")
        print(f"Error: {str(e)}")
        print("-" * 50)
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 اختبار API Endpoints للتطبيق المحمول")
    print("=" * 60)
    
    # قائمة الاختبارات
    tests = [
        ("/api/visits/my-tasks/", "GET"),
        ("/api/clients/", "GET"),
        # يمكن إضافة المزيد من الاختبارات هنا
    ]
    
    passed = 0
    total = len(tests)
    
    for endpoint, method in tests:
        if test_api_endpoint(endpoint, method):
            passed += 1
    
    print("=" * 60)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
    else:
        print("⚠️ بعض الاختبارات فشلت")

if __name__ == "__main__":
    main()
