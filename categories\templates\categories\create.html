{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}إنشاء تصنيف جديد{% endblock %}

{% block extra_css %}
<style>
    .create-form-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-create {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }
    
    .category-type-info {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 8px;
        color: #495057;
    }
    
    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 5px;
    }
    
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">إنشاء تصنيف جديد</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-left">
                        <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'categories:dashboard' %}">التصنيفات</a></li>
                        <li class="breadcrumb-item active">إنشاء تصنيف</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="card create-form-card">
                        <div class="card-header text-center">
                            <h3 class="card-title mb-0">
                                <i class="bi bi-plus-circle me-2"></i>
                                إنشاء تصنيف جديد
                            </h3>
                        </div>
                        <div class="card-body">
                            
                            <!-- معلومات نوع التصنيف -->
                            <div class="category-type-info">
                                <h5 class="mb-2">
                                    <i class="bi bi-info-circle me-2"></i>
                                    نوع التصنيف: {{ category_type_display }}
                                </h5>
                                <p class="mb-0 opacity-75">
                                    {% if category_type == 1 %}
                                    تصنيف خاص بالمستخدمين والمديرين في النظام
                                    {% elif category_type == 2 %}
                                    تصنيف خاص بمندوبي المبيعات
                                    {% elif category_type == 3 %}
                                    تصنيف خاص بالعملاء والشركات
                                    {% else %}
                                    تصنيف عام للنظام
                                    {% endif %}
                                </p>
                            </div>

                            <!-- نموذج الإنشاء -->
                            <form method="post" id="createCategoryForm">
                                {% csrf_token %}
                                
                                <!-- اسم التصنيف -->
                                <div class="form-group">
                                    <label for="name" class="form-label required-field">اسم التصنيف</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="name" 
                                           name="name" 
                                           required 
                                           placeholder="أدخل اسم التصنيف"
                                           maxlength="100">
                                    <div class="help-text">
                                        اسم واضح ومميز للتصنيف (حد أقصى 100 حرف)
                                    </div>
                                </div>

                                <!-- وصف التصنيف -->
                                <div class="form-group">
                                    <label for="description" class="form-label">الوصف</label>
                                    <textarea class="form-control" 
                                              id="description" 
                                              name="description" 
                                              rows="3" 
                                              placeholder="وصف مختصر للتصنيف (اختياري)"
                                              maxlength="500"></textarea>
                                    <div class="help-text">
                                        وصف مفصل للتصنيف وغرضه (حد أقصى 500 حرف)
                                    </div>
                                </div>

                                <!-- حقل مخفي لنوع التصنيف -->
                                <input type="hidden" name="category_type" value="{{ category_type }}">

                                <!-- حالة التصنيف -->
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="is_active" 
                                               name="is_active" 
                                               checked>
                                        <label class="form-check-label" for="is_active">
                                            تفعيل التصنيف فور الإنشاء
                                        </label>
                                    </div>
                                    <div class="help-text">
                                        يمكن تعديل حالة التصنيف لاحقاً من صفحة التعديل
                                    </div>
                                </div>

                                <!-- أزرار الإجراءات -->
                                <div class="row mt-4">
                                    <div class="col-6">
                                        <button type="submit" class="btn btn-create w-100">
                                            <i class="bi bi-check-circle me-2"></i>
                                            إنشاء التصنيف
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <a href="{% url 'categories:dashboard' %}" class="btn btn-outline-light w-100">
                                            <i class="bi bi-arrow-left me-2"></i>
                                            إلغاء
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-lightbulb me-2"></i>
                                نصائح مهمة
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="d-flex align-items-start">
                                        <i class="bi bi-check-circle text-success me-3 mt-1"></i>
                                        <div>
                                            <h6>اختر اسماً واضحاً</h6>
                                            <small class="text-muted">استخدم اسماً يعبر عن محتوى التصنيف بوضوح</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-start">
                                        <i class="bi bi-people text-info me-3 mt-1"></i>
                                        <div>
                                            <h6>فكر في المستخدمين</h6>
                                            <small class="text-muted">تأكد من أن التصنيف سيكون مفيداً للمستخدمين المستهدفين</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-start">
                                        <i class="bi bi-gear text-warning me-3 mt-1"></i>
                                        <div>
                                            <h6>يمكن التعديل لاحقاً</h6>
                                            <small class="text-muted">يمكنك تعديل جميع خصائص التصنيف بعد إنشائه</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createCategoryForm');
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    
    // تحسين تجربة المستخدم
    nameInput.addEventListener('input', function() {
        const value = this.value.trim();
        if (value.length > 0) {
            this.classList.add('is-valid');
            this.classList.remove('is-invalid');
        } else {
            this.classList.add('is-invalid');
            this.classList.remove('is-valid');
        }
    });
    
    // عداد الأحرف للوصف
    descriptionInput.addEventListener('input', function() {
        const remaining = 500 - this.value.length;
        const helpText = this.parentNode.querySelector('.help-text');
        helpText.textContent = `وصف مفصل للتصنيف وغرضه (متبقي ${remaining} حرف)`;
        
        if (remaining < 50) {
            helpText.style.color = '#dc3545';
        } else {
            helpText.style.color = '#6c757d';
        }
    });
    
    // التحقق من صحة النموذج قبل الإرسال
    form.addEventListener('submit', function(e) {
        const name = nameInput.value.trim();
        
        if (!name) {
            e.preventDefault();
            nameInput.classList.add('is-invalid');
            nameInput.focus();
            
            // إظهار رسالة خطأ
            Swal.fire({
                icon: 'error',
                title: 'خطأ في البيانات',
                text: 'يرجى إدخال اسم التصنيف',
                confirmButtonText: 'حسناً'
            });
            return;
        }
        
        // إظهار مؤشر التحميل
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الإنشاء...';
        submitBtn.disabled = true;
        
        // في حالة فشل الإرسال، استعادة الزر
        setTimeout(() => {
            if (submitBtn.disabled) {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }, 10000);
    });
    
    // تأثيرات بصرية
    const card = document.querySelector('.create-form-card');
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        card.style.transition = 'all 0.5s ease';
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
    }, 100);
});
</script>

<!-- SweetAlert2 للرسائل -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
{% endblock %}
