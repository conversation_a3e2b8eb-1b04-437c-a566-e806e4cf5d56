#!/usr/bin/env python
"""
اختبار إصلاحات التعارضات بين التصنيفات والمستخدمين
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'field_sales_tracker.settings')
django.setup()

from users.models import User
from categories.models import Category
import uuid

def test_category_user_relationship():
    """اختبار العلاقة بين التصنيفات والمستخدمين"""
    print("🧪 اختبار العلاقة بين التصنيفات والمستخدمين")
    print("=" * 50)
    
    # 1. إنشاء تصنيف تجريبي
    print("1. إنشاء تصنيف تجريبي...")
    test_category = Category.objects.create(
        name="تصنيف تجريبي للاختبار",
        description="تصنيف لاختبار الإصلاحات",
        category_type=2,  # تصنيفات المستخدمين
        is_active=True
    )
    print(f"   ✅ تم إنشاء التصنيف: {test_category.name}")
    print(f"   📋 معرف التصنيف: {test_category.id}")
    print(f"   🏷️ نوع التصنيف: {test_category.get_category_type_display()}")
    
    # 2. اختبار إضافة تصنيف لمستخدم
    print("\n2. اختبار إضافة تصنيف لمستخدم...")
    user = User.objects.filter(role_type__in=[1, 2]).first()
    if user:
        print(f"   👤 المستخدم: {user.username}")
        print(f"   📋 التصنيفات قبل الإضافة: {user.categories}")
        
        # إضافة التصنيف
        user.add_category(test_category.id)
        user.refresh_from_db()
        
        print(f"   📋 التصنيفات بعد الإضافة: {user.categories}")
        print(f"   🔍 نوع البيانات: {type(user.categories)}")
        
        # التحقق من وجود التصنيف
        if str(test_category.id) in user.categories:
            print("   ✅ تم إضافة التصنيف بنجاح!")
        else:
            print("   ❌ فشل في إضافة التصنيف!")
            
        # اختبار get_categories()
        user_categories = user.get_categories()
        print(f"   📂 التصنيفات المرتبطة: {user_categories.count()}")
        for cat in user_categories:
            print(f"      - {cat.name} ({cat.id})")
            
    else:
        print("   ❌ لم يتم العثور على مستخدم للاختبار")
    
    # 3. اختبار حذف التصنيف مع التحقق من المستخدمين
    print("\n3. اختبار التحقق من المستخدمين المرتبطين...")
    users_with_category = []
    all_users = User.objects.filter(is_active=True)
    
    for u in all_users:
        if u.categories and str(test_category.id) in u.categories:
            users_with_category.append(u)
    
    print(f"   👥 عدد المستخدمين المرتبطين: {len(users_with_category)}")
    for u in users_with_category:
        print(f"      - {u.username} ({u.get_role_type_display()})")
    
    # 4. اختبار إزالة التصنيف
    print("\n4. اختبار إزالة التصنيف...")
    if user and str(test_category.id) in user.categories:
        user.remove_category(test_category.id)
        user.refresh_from_db()
        
        print(f"   📋 التصنيفات بعد الإزالة: {user.categories}")
        
        if str(test_category.id) not in user.categories:
            print("   ✅ تم إزالة التصنيف بنجاح!")
        else:
            print("   ❌ فشل في إزالة التصنيف!")
    
    # 5. تنظيف البيانات التجريبية
    print("\n5. تنظيف البيانات التجريبية...")
    test_category.delete()
    print("   🗑️ تم حذف التصنيف التجريبي")
    
    print("\n✅ انتهى اختبار العلاقة بين التصنيفات والمستخدمين")

def test_category_types():
    """اختبار أنواع التصنيفات"""
    print("\n🏷️ اختبار أنواع التصنيفات")
    print("=" * 50)
    
    print("أنواع التصنيفات المعرفة:")
    for type_id, type_name in Category.CATEGORY_TYPES:
        count = Category.objects.filter(category_type=type_id).count()
        print(f"   {type_id}: {type_name} ({count} تصنيف)")
    
    print("\nتوزيع المستخدمين حسب الأدوار:")
    for role_id, role_name in User.ROLE_TYPES:
        count = User.objects.filter(role_type=role_id).count()
        print(f"   {role_id}: {role_name} ({count} مستخدم)")

def test_data_integrity():
    """اختبار سلامة البيانات"""
    print("\n🔍 اختبار سلامة البيانات")
    print("=" * 50)
    
    # فحص المستخدمين الذين لديهم تصنيفات
    users_with_categories = User.objects.exclude(categories=[])
    print(f"المستخدمين الذين لديهم تصنيفات: {users_with_categories.count()}")
    
    for user in users_with_categories:
        print(f"\n👤 {user.username} ({user.get_role_type_display()}):")
        print(f"   📋 التصنيفات: {user.categories}")
        print(f"   🔍 نوع البيانات: {type(user.categories)}")
        
        # التحقق من صحة معرفات التصنيفات
        valid_categories = []
        invalid_categories = []
        
        for cat_id in user.categories:
            try:
                # التحقق من أن المعرف UUID صحيح
                uuid.UUID(str(cat_id))
                # التحقق من وجود التصنيف
                if Category.objects.filter(id=cat_id).exists():
                    valid_categories.append(cat_id)
                else:
                    invalid_categories.append(f"{cat_id} (غير موجود)")
            except (ValueError, TypeError):
                invalid_categories.append(f"{cat_id} (معرف غير صحيح)")
        
        print(f"   ✅ تصنيفات صحيحة: {len(valid_categories)}")
        print(f"   ❌ تصنيفات خاطئة: {len(invalid_categories)}")
        
        if invalid_categories:
            print(f"      التصنيفات الخاطئة: {invalid_categories}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاحات التعارضات")
    print("=" * 60)
    
    try:
        # اختبار أنواع التصنيفات
        test_category_types()
        
        # اختبار سلامة البيانات
        test_data_integrity()
        
        # اختبار العلاقة بين التصنيفات والمستخدمين
        test_category_user_relationship()
        
        print("\n🎉 تم إنجاز جميع الاختبارات بنجاح!")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
