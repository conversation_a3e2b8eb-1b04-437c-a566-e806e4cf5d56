#!/usr/bin/env python
"""
إعداد الأدوار والبيانات الافتراضية
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'field_sales_tracker.settings')
django.setup()

from users.models import User
from categories.models import Category
from clients.models import Client

def setup_user_1_as_super_manager():
    """تعيين المستخدم رقم 1 كمدير عام"""
    print("🔧 إعداد المستخدم رقم 1 كمدير عام...")
    
    try:
        user_1 = User.objects.get(id=1)
        user_1.role_type = 1  # مدير عام
        user_1.is_staff = True
        user_1.is_superuser = True
        user_1.is_active = True
        user_1.is_active_employee = True
        user_1.save()
        
        print(f"✅ تم تعيين {user_1.username} كمدير عام")
        print(f"   📋 الاسم: {user_1.get_full_name()}")
        print(f"   🏷️ الدور: {user_1.get_role_type_display()}")
        print(f"   🔑 صلاحيات الإدارة: نعم")
        
    except User.DoesNotExist:
        print("❌ المستخدم رقم 1 غير موجود")
        
        # إنشاء المستخدم رقم 1 إذا لم يكن موجوداً
        user_1 = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='المدير',
            last_name='العام',
            role_type=1,
            is_staff=True,
            is_superuser=True,
            is_active=True,
            is_active_employee=True,
        )
        print(f"✅ تم إنشاء المستخدم الأول: {user_1.username}")

def create_default_categories():
    """إنشاء تصنيفات افتراضية"""
    print("\n📂 إنشاء التصنيفات الافتراضية...")
    
    default_categories = [
        # تصنيفات المدير العام
        {
            'name': 'إدارة عليا',
            'description': 'تصنيف للإدارة العليا والمديرين التنفيذيين',
            'category_type': 1,
        },
        {
            'name': 'مديرو الفروع',
            'description': 'تصنيف لمديري الفروع الرئيسية',
            'category_type': 1,
        },
        
        # تصنيفات المستخدمين
        {
            'name': 'مديرو المبيعات',
            'description': 'تصنيف لمديري أقسام المبيعات',
            'category_type': 2,
        },
        {
            'name': 'مديرو التسويق',
            'description': 'تصنيف لمديري أقسام التسويق',
            'category_type': 2,
        },
        {
            'name': 'مديرو خدمة العملاء',
            'description': 'تصنيف لمديري خدمة العملاء',
            'category_type': 2,
        },
        
        # تصنيفات المناديب
        {
            'name': 'مناديب المنطقة الشمالية',
            'description': 'مناديب المبيعات في المنطقة الشمالية',
            'category_type': 3,
        },
        {
            'name': 'مناديب المنطقة الجنوبية',
            'description': 'مناديب المبيعات في المنطقة الجنوبية',
            'category_type': 3,
        },
        {
            'name': 'مناديب المنطقة الشرقية',
            'description': 'مناديب المبيعات في المنطقة الشرقية',
            'category_type': 3,
        },
        {
            'name': 'مناديب المنطقة الغربية',
            'description': 'مناديب المبيعات في المنطقة الغربية',
            'category_type': 3,
        },
        {
            'name': 'مناديب المنطقة الوسطى',
            'description': 'مناديب المبيعات في المنطقة الوسطى',
            'category_type': 3,
        },
        
        # تصنيفات العملاء
        {
            'name': 'عملاء VIP',
            'description': 'العملاء المميزون ذوو الأولوية العالية',
            'category_type': 4,
        },
        {
            'name': 'عملاء تجاريون',
            'description': 'العملاء من الشركات والمؤسسات التجارية',
            'category_type': 4,
        },
        {
            'name': 'عملاء أفراد',
            'description': 'العملاء الأفراد والمستهلكون',
            'category_type': 4,
        },
    ]
    
    created_count = 0
    for cat_data in default_categories:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults=cat_data
        )
        
        if created:
            created_count += 1
            print(f"   ✅ تم إنشاء: {category.name} ({category.get_category_type_display()})")
        else:
            print(f"   ℹ️ موجود مسبقاً: {category.name}")
    
    print(f"\n📊 تم إنشاء {created_count} تصنيف جديد")

def create_sample_users():
    """إنشاء مستخدمين تجريبيين"""
    print("\n👥 إنشاء مستخدمين تجريبيين...")
    
    sample_users = [
        # مديرو مستخدمين
        {
            'username': 'manager1',
            'email': '<EMAIL>',
            'password': 'manager123',
            'first_name': 'أحمد',
            'last_name': 'المدير',
            'role_type': 2,
            'categories': ['مديرو المبيعات'],
        },
        {
            'username': 'manager2',
            'email': '<EMAIL>',
            'password': 'manager123',
            'first_name': 'فاطمة',
            'last_name': 'المديرة',
            'role_type': 2,
            'categories': ['مديرو التسويق'],
        },
        
        # مناديب مبيعات
        {
            'username': 'sales1',
            'email': '<EMAIL>',
            'password': 'sales123',
            'first_name': 'محمد',
            'last_name': 'المندوب',
            'role_type': 3,
            'categories': ['مناديب المنطقة الشمالية'],
        },
        {
            'username': 'sales2',
            'email': '<EMAIL>',
            'password': 'sales123',
            'first_name': 'عائشة',
            'last_name': 'المندوبة',
            'role_type': 3,
            'categories': ['مناديب المنطقة الجنوبية'],
        },
        {
            'username': 'sales3',
            'email': '<EMAIL>',
            'password': 'sales123',
            'first_name': 'خالد',
            'last_name': 'المندوب',
            'role_type': 3,
            'categories': ['مناديب المنطقة الشرقية'],
        },
    ]
    
    created_count = 0
    for user_data in sample_users:
        categories_names = user_data.pop('categories', [])
        
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults=user_data
        )
        
        if created:
            created_count += 1
            print(f"   ✅ تم إنشاء: {user.username} ({user.get_role_type_display()})")
            
            # إضافة التصنيفات
            if categories_names:
                category_ids = []
                for cat_name in categories_names:
                    try:
                        category = Category.objects.get(name=cat_name)
                        category_ids.append(str(category.id))
                    except Category.DoesNotExist:
                        pass
                
                if category_ids:
                    user.categories = category_ids
                    user.save()
                    print(f"      📂 تم إضافة {len(category_ids)} تصنيف")
        else:
            print(f"   ℹ️ موجود مسبقاً: {user.username}")
    
    print(f"\n📊 تم إنشاء {created_count} مستخدم جديد")

def create_sample_clients():
    """إنشاء عملاء تجريبيين"""
    print("\n🏢 إنشاء عملاء تجريبيين...")
    
    try:
        sample_clients = [
            {
                'name': 'شركة الأمل التجارية',
                'contact_person': 'أحمد محمد',
                'phone': '0501234567',
                'email': '<EMAIL>',
                'address': 'الرياض، المملكة العربية السعودية',
                'category': 'عملاء تجاريون',
            },
            {
                'name': 'مؤسسة النجاح للتجارة',
                'contact_person': 'فاطمة أحمد',
                'phone': '0507654321',
                'email': '<EMAIL>',
                'address': 'جدة، المملكة العربية السعودية',
                'category': 'عملاء VIP',
            },
            {
                'name': 'محمد عبدالله (فرد)',
                'contact_person': 'محمد عبدالله',
                'phone': '0509876543',
                'email': '<EMAIL>',
                'address': 'الدمام، المملكة العربية السعودية',
                'category': 'عملاء أفراد',
            },
        ]
        
        created_count = 0
        for client_data in sample_clients:
            category_name = client_data.pop('category', None)
            
            client, created = Client.objects.get_or_create(
                name=client_data['name'],
                defaults=client_data
            )
            
            if created:
                created_count += 1
                print(f"   ✅ تم إنشاء: {client.name}")
                
                # إضافة التصنيف
                if category_name:
                    try:
                        category = Category.objects.get(name=category_name)
                        if not client.categories:
                            client.categories = []
                        client.categories.append(str(category.id))
                        client.save()
                        print(f"      📂 تم إضافة تصنيف: {category_name}")
                    except Category.DoesNotExist:
                        pass
            else:
                print(f"   ℹ️ موجود مسبقاً: {client.name}")
        
        print(f"\n📊 تم إنشاء {created_count} عميل جديد")
        
    except Exception as e:
        print(f"⚠️ تعذر إنشاء العملاء: {e}")

def display_summary():
    """عرض ملخص البيانات"""
    print("\n📊 ملخص البيانات الحالية:")
    print("=" * 50)
    
    # المستخدمين
    total_users = User.objects.count()
    super_managers = User.objects.filter(role_type=1).count()
    user_managers = User.objects.filter(role_type=2).count()
    sales_reps = User.objects.filter(role_type=3).count()
    
    print(f"👥 المستخدمين:")
    print(f"   إجمالي: {total_users}")
    print(f"   مديرين عامين: {super_managers}")
    print(f"   مديرو مستخدمين: {user_managers}")
    print(f"   مناديب مبيعات: {sales_reps}")
    
    # التصنيفات
    total_categories = Category.objects.count()
    super_categories = Category.objects.filter(category_type=1).count()
    user_categories = Category.objects.filter(category_type=2).count()
    sales_categories = Category.objects.filter(category_type=3).count()
    client_categories = Category.objects.filter(category_type=4).count()
    
    print(f"\n📂 التصنيفات:")
    print(f"   إجمالي: {total_categories}")
    print(f"   تصنيفات المدير العام: {super_categories}")
    print(f"   تصنيفات المستخدمين: {user_categories}")
    print(f"   تصنيفات المناديب: {sales_categories}")
    print(f"   تصنيفات العملاء: {client_categories}")
    
    # العملاء
    try:
        total_clients = Client.objects.count()
        print(f"\n🏢 العملاء:")
        print(f"   إجمالي: {total_clients}")
    except:
        print(f"\n🏢 العملاء: غير متاح")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إعداد البيانات الافتراضية")
    print("=" * 60)
    
    try:
        # إعداد المستخدم رقم 1
        setup_user_1_as_super_manager()
        
        # إنشاء التصنيفات الافتراضية
        create_default_categories()
        
        # إنشاء مستخدمين تجريبيين
        create_sample_users()
        
        # إنشاء عملاء تجريبيين
        create_sample_clients()
        
        # عرض الملخص
        display_summary()
        
        print("\n🎉 تم إكمال إعداد البيانات الافتراضية بنجاح!")
        print("✅ النظام جاهز للاستخدام")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء الإعداد: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
