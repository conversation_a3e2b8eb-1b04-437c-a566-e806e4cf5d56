from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
import random
from visits.models import Visit
from users.models import User
from clients.models import Client


class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية للمهام'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=20,
            help='عدد المهام المراد إنشاؤها'
        )

    def handle(self, *args, **options):
        count = options['count']
        
        # جلب المناديب والعملاء والمدراء
        sales_reps = User.objects.filter(role_type=3, is_active=True)
        clients = Client.objects.filter(is_active=True)
        managers = User.objects.filter(role_type__in=[1, 2], is_active=True)
        
        if not sales_reps.exists():
            self.stdout.write(
                self.style.ERROR('لا يوجد مناديب مبيعات في النظام')
            )
            return
        
        if not clients.exists():
            self.stdout.write(
                self.style.ERROR('لا يوجد عملاء في النظام')
            )
            return
        
        if not managers.exists():
            self.stdout.write(
                self.style.ERROR('لا يوجد مدراء في النظام')
            )
            return

        # قوائم البيانات التجريبية
        task_titles = [
            'زيارة عميل جديد',
            'متابعة طلب سابق',
            'تحديث بيانات العميل',
            'عرض منتجات جديدة',
            'جمع مستحقات',
            'تقييم رضا العميل',
            'توقيع عقد جديد',
            'حل مشكلة فنية',
            'تدريب العميل على المنتج',
            'مراجعة دورية',
        ]
        
        task_descriptions = [
            'يرجى زيارة العميل وتقديم عرض شامل للمنتجات الجديدة',
            'متابعة الطلب المقدم الأسبوع الماضي والتأكد من حالة التنفيذ',
            'تحديث معلومات الاتصال وبيانات العميل في النظام',
            'عرض المنتجات الجديدة وشرح مميزاتها للعميل',
            'جمع المستحقات المالية المتأخرة من العميل',
            'إجراء استبيان رضا العميل وتسجيل الملاحظات',
            'إنهاء إجراءات توقيع العقد الجديد مع العميل',
            'حل المشكلة الفنية المبلغ عنها من العميل',
            'تدريب فريق العميل على استخدام المنتج الجديد',
            'مراجعة دورية لحالة العميل ومتطلباته',
        ]
        
        priorities = ['low', 'medium', 'high', 'urgent']
        statuses = ['assigned', 'acknowledged', 'in_progress', 'completed', 'cancelled']
        
        created_tasks = 0
        
        for i in range(count):
            try:
                # اختيار بيانات عشوائية
                sales_rep = random.choice(sales_reps)
                client = random.choice(clients)
                manager = random.choice(managers)
                title = random.choice(task_titles)
                description = random.choice(task_descriptions)
                priority = random.choice(priorities)
                status = random.choice(statuses)
                
                # تواريخ عشوائية
                assigned_date = timezone.now() - timedelta(days=random.randint(1, 30))
                due_date = assigned_date + timedelta(days=random.randint(1, 14))
                
                # إنشاء المهمة
                task = Visit.objects.create(
                    sales_rep=sales_rep,
                    client=client,
                    assigned_by=manager,
                    task_type='manager_assigned',
                    task_status=status,
                    task_title=title,
                    task_description=description,
                    priority=priority,
                    assigned_at=assigned_date,
                    due_date=due_date,
                )
                
                # تحديث التواريخ حسب الحالة
                if status == 'acknowledged':
                    task.acknowledged_at = assigned_date + timedelta(hours=random.randint(1, 24))
                elif status == 'in_progress':
                    task.acknowledged_at = assigned_date + timedelta(hours=random.randint(1, 12))
                    task.started_at = task.acknowledged_at + timedelta(hours=random.randint(1, 12))
                elif status == 'completed':
                    task.acknowledged_at = assigned_date + timedelta(hours=random.randint(1, 6))
                    task.started_at = task.acknowledged_at + timedelta(hours=random.randint(1, 6))
                    task.completed_at = task.started_at + timedelta(hours=random.randint(1, 48))
                    
                    # إنشاء زيارة فعلية للمهام المكتملة
                    task.barcode_scanned = client.barcode
                    task.visit_latitude = float(client.latitude) if client.latitude else 24.7136
                    task.visit_longitude = float(client.longitude) if client.longitude else 46.6753
                    task.notes = f'تم إنجاز المهمة: {title}'
                    task.status = 'verified'  # حالة الزيارة
                elif status == 'cancelled':
                    task.acknowledged_at = assigned_date + timedelta(hours=random.randint(1, 12))
                    task.completed_at = task.acknowledged_at + timedelta(hours=random.randint(1, 24))
                
                task.save()
                created_tasks += 1
                
                self.stdout.write(f'تم إنشاء المهمة {i+1}: {title} - {sales_rep.get_full_name()} - {client.name}')
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'خطأ في إنشاء المهمة {i+1}: {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء {created_tasks} مهمة بنجاح من أصل {count}')
        )
