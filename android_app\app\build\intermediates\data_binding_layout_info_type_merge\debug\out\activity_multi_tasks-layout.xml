<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_multi_tasks" modulePackage="com.company.fieldsalestracker" filePath="app\src\main\res\layout\activity_multi_tasks.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_multi_tasks_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="123" endOffset="14"/></Target><Target id="@+id/btnAllTasks" view="Button"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="48"/></Target><Target id="@+id/btnRecurringOnly" view="Button"><Expressions/><location startLine="44" startOffset="12" endLine="53" endOffset="48"/></Target><Target id="@+id/btnOneTimeOnly" view="Button"><Expressions/><location startLine="55" startOffset="12" endLine="63" endOffset="50"/></Target><Target id="@+id/statsText" view="TextView"><Expressions/><location startLine="70" startOffset="4" endLine="79" endOffset="35"/></Target><Target id="@+id/swipeRefresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="82" startOffset="4" endLine="121" endOffset="59"/></Target><Target id="@+id/recyclerViewMultiTasks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="93" startOffset="12" endLine="98" endOffset="47"/></Target><Target id="@+id/emptyView" view="TextView"><Expressions/><location startLine="101" startOffset="12" endLine="109" endOffset="43"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="112" startOffset="12" endLine="117" endOffset="43"/></Target></Targets></Layout>