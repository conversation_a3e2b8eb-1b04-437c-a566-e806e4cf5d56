#!/usr/bin/env python
"""
إسناد القيم الافتراضية للأدوار والتصنيفات
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'field_sales_tracker.settings')
django.setup()

from users.models import User
from categories.models import Category

def setup_user_1_as_super_manager():
    """تعيين المستخدم رقم 1 كمدير عام"""
    print("🔧 إعداد المستخدم رقم 1 كمدير عام...")
    
    try:
        user_1 = User.objects.get(id=1)
        user_1.role_type = 1  # مدير عام
        user_1.is_staff = True
        user_1.is_superuser = True
        user_1.is_active = True
        user_1.is_active_employee = True
        user_1.save()
        
        print(f"✅ تم تعيين {user_1.username} كمدير عام")
        print(f"   📋 الاسم: {user_1.get_full_name()}")
        print(f"   🏷️ الدور: {user_1.get_role_type_display()}")
        
    except User.DoesNotExist:
        print("❌ المستخدم رقم 1 غير موجود")
        
        # إنشاء المستخدم رقم 1 إذا لم يكن موجوداً
        user_1 = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='المدير',
            last_name='العام',
            role_type=1,
            is_staff=True,
            is_superuser=True,
            is_active=True,
            is_active_employee=True,
        )
        print(f"✅ تم إنشاء المستخدم الأول: {user_1.username}")

def create_default_categories():
    """إنشاء تصنيفات افتراضية"""
    print("\n📂 إنشاء التصنيفات الافتراضية...")
    
    default_categories = [
        # تصنيفات المدير العام (type=1)
        {
            'name': 'إدارة عليا',
            'description': 'تصنيف للإدارة العليا والمديرين التنفيذيين',
            'category_type': 1,
        },
        {
            'name': 'مديرو الفروع',
            'description': 'تصنيف لمديري الفروع الرئيسية',
            'category_type': 1,
        },
        
        # تصنيفات المستخدمين (type=2)
        {
            'name': 'مديرو المبيعات',
            'description': 'تصنيف لمديري أقسام المبيعات',
            'category_type': 2,
        },
        {
            'name': 'مديرو التسويق',
            'description': 'تصنيف لمديري أقسام التسويق',
            'category_type': 2,
        },
        {
            'name': 'مديرو خدمة العملاء',
            'description': 'تصنيف لمديري خدمة العملاء',
            'category_type': 2,
        },
        
        # تصنيفات المناديب (type=3)
        {
            'name': 'مناديب المنطقة الشمالية',
            'description': 'مناديب المبيعات في المنطقة الشمالية',
            'category_type': 3,
        },
        {
            'name': 'مناديب المنطقة الجنوبية',
            'description': 'مناديب المبيعات في المنطقة الجنوبية',
            'category_type': 3,
        },
        {
            'name': 'مناديب المنطقة الشرقية',
            'description': 'مناديب المبيعات في المنطقة الشرقية',
            'category_type': 3,
        },
        {
            'name': 'مناديب المنطقة الوسطى',
            'description': 'مناديب المبيعات في المنطقة الوسطى',
            'category_type': 3,
        },
        
        # تصنيفات العملاء (type=4)
        {
            'name': 'عملاء VIP',
            'description': 'العملاء المميزون ذوو الأولوية العالية',
            'category_type': 4,
        },
        {
            'name': 'عملاء تجاريون',
            'description': 'العملاء من الشركات والمؤسسات التجارية',
            'category_type': 4,
        },
        {
            'name': 'عملاء أفراد',
            'description': 'العملاء الأفراد والمستهلكون',
            'category_type': 4,
        },
    ]
    
    created_count = 0
    for cat_data in default_categories:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults=cat_data
        )
        
        if created:
            created_count += 1
            print(f"   ✅ تم إنشاء: {category.name} ({category.get_category_type_display()})")
        else:
            print(f"   ℹ️ موجود مسبقاً: {category.name}")
    
    print(f"\n📊 تم إنشاء {created_count} تصنيف جديد")

def assign_categories_to_users():
    """إسناد تصنيفات للمستخدمين الموجودين"""
    print("\n👥 إسناد تصنيفات للمستخدمين...")
    
    # الحصول على التصنيفات
    super_categories = list(Category.objects.filter(category_type=1))
    user_categories = list(Category.objects.filter(category_type=2))
    sales_categories = list(Category.objects.filter(category_type=3))
    
    assigned_count = 0
    
    # إسناد تصنيفات للمديرين العامين
    super_managers = User.objects.filter(role_type=1)
    for user in super_managers:
        if not user.categories and super_categories:
            # إسناد أول تصنيف متاح
            user.categories = [str(super_categories[0].id)]
            user.save()
            assigned_count += 1
            print(f"   ✅ تم إسناد تصنيف '{super_categories[0].name}' للمدير العام: {user.username}")
    
    # إسناد تصنيفات لمديري المستخدمين
    user_managers = User.objects.filter(role_type=2)
    for i, user in enumerate(user_managers):
        if not user.categories and user_categories:
            # توزيع التصنيفات بالتناوب
            category = user_categories[i % len(user_categories)]
            user.categories = [str(category.id)]
            user.save()
            assigned_count += 1
            print(f"   ✅ تم إسناد تصنيف '{category.name}' لمدير المستخدمين: {user.username}")
    
    # إسناد تصنيفات للمناديب
    sales_reps = User.objects.filter(role_type=3)
    for i, user in enumerate(sales_reps):
        if not user.categories and sales_categories:
            # توزيع التصنيفات بالتناوب
            category = sales_categories[i % len(sales_categories)]
            user.categories = [str(category.id)]
            user.save()
            assigned_count += 1
            print(f"   ✅ تم إسناد تصنيف '{category.name}' للمندوب: {user.username}")
    
    print(f"\n📊 تم إسناد تصنيفات لـ {assigned_count} مستخدم")

def create_sample_users_with_categories():
    """إنشاء مستخدمين تجريبيين مع تصنيفات"""
    print("\n👤 إنشاء مستخدمين تجريبيين...")
    
    # الحصول على التصنيفات
    user_categories = Category.objects.filter(category_type=2)
    sales_categories = Category.objects.filter(category_type=3)
    
    sample_users = [
        # مديرو مستخدمين
        {
            'username': 'manager1',
            'email': '<EMAIL>',
            'password': 'manager123',
            'first_name': 'أحمد',
            'last_name': 'المدير',
            'role_type': 2,
            'category_type': 2,
        },
        {
            'username': 'manager2',
            'email': '<EMAIL>',
            'password': 'manager123',
            'first_name': 'فاطمة',
            'last_name': 'المديرة',
            'role_type': 2,
            'category_type': 2,
        },
        
        # مناديب مبيعات
        {
            'username': 'sales1',
            'email': '<EMAIL>',
            'password': 'sales123',
            'first_name': 'محمد',
            'last_name': 'المندوب',
            'role_type': 3,
            'category_type': 3,
        },
        {
            'username': 'sales2',
            'email': '<EMAIL>',
            'password': 'sales123',
            'first_name': 'عائشة',
            'last_name': 'المندوبة',
            'role_type': 3,
            'category_type': 3,
        },
        {
            'username': 'sales3',
            'email': '<EMAIL>',
            'password': 'sales123',
            'first_name': 'خالد',
            'last_name': 'المندوب',
            'role_type': 3,
            'category_type': 3,
        },
    ]
    
    created_count = 0
    for i, user_data in enumerate(sample_users):
        category_type = user_data.pop('category_type')
        
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults=user_data
        )
        
        if created:
            created_count += 1
            print(f"   ✅ تم إنشاء: {user.username} ({user.get_role_type_display()})")
            
            # إسناد تصنيف
            if category_type == 2 and user_categories.exists():
                category = user_categories[i % user_categories.count()]
                user.categories = [str(category.id)]
                user.save()
                print(f"      📂 تم إسناد تصنيف: {category.name}")
            elif category_type == 3 and sales_categories.exists():
                category = sales_categories[i % sales_categories.count()]
                user.categories = [str(category.id)]
                user.save()
                print(f"      📂 تم إسناد تصنيف: {category.name}")
        else:
            print(f"   ℹ️ موجود مسبقاً: {user.username}")
    
    print(f"\n📊 تم إنشاء {created_count} مستخدم جديد")

def display_summary():
    """عرض ملخص البيانات"""
    print("\n📊 ملخص البيانات الحالية:")
    print("=" * 50)
    
    # المستخدمين
    total_users = User.objects.count()
    super_managers = User.objects.filter(role_type=1).count()
    user_managers = User.objects.filter(role_type=2).count()
    sales_reps = User.objects.filter(role_type=3).count()
    
    print(f"👥 المستخدمين:")
    print(f"   إجمالي: {total_users}")
    print(f"   مديرين عامين: {super_managers}")
    print(f"   مديرو مستخدمين: {user_managers}")
    print(f"   مناديب مبيعات: {sales_reps}")
    
    # التصنيفات
    total_categories = Category.objects.count()
    super_categories = Category.objects.filter(category_type=1).count()
    user_categories = Category.objects.filter(category_type=2).count()
    sales_categories = Category.objects.filter(category_type=3).count()
    client_categories = Category.objects.filter(category_type=4).count()
    
    print(f"\n📂 التصنيفات:")
    print(f"   إجمالي: {total_categories}")
    print(f"   تصنيفات المدير العام: {super_categories}")
    print(f"   تصنيفات المستخدمين: {user_categories}")
    print(f"   تصنيفات المناديب: {sales_categories}")
    print(f"   تصنيفات العملاء: {client_categories}")
    
    # المستخدمين مع التصنيفات
    users_with_categories = User.objects.exclude(categories=[]).count()
    print(f"\n🏷️ المستخدمين مع التصنيفات: {users_with_categories}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إسناد القيم الافتراضية للأدوار والتصنيفات")
    print("=" * 60)
    
    try:
        # إعداد المستخدم رقم 1
        setup_user_1_as_super_manager()
        
        # إنشاء التصنيفات الافتراضية
        create_default_categories()
        
        # إسناد تصنيفات للمستخدمين الموجودين
        assign_categories_to_users()
        
        # إنشاء مستخدمين تجريبيين
        create_sample_users_with_categories()
        
        # عرض الملخص
        display_summary()
        
        print("\n🎉 تم إكمال إسناد القيم الافتراضية بنجاح!")
        print("✅ النظام جاهز للاستخدام")
        print("\n📝 معلومات تسجيل الدخول:")
        print("   المدير العام: admin / admin123")
        print("   مدير مستخدمين: manager1 / manager123")
        print("   مندوب مبيعات: sales1 / sales123")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء الإعداد: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
