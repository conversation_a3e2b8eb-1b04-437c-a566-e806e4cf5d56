{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/management-pages.css' %}">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style>
.role-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.role-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
}

.role-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
}

.role-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.role-level {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin: 1rem 0;
}

.permission-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 0.9rem;
}

.permission-item.active {
    background: #e8f5e8;
    color: #388e3c;
}

.permission-item.inactive {
    background: #ffebee;
    color: #d32f2f;
}

.role-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.users-count {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-user-shield me-2"></i>
                {{ title }}
            </h1>
            <p class="text-muted mb-0">إدارة الأدوار والصلاحيات في النظام</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="createRole()">
                <i class="fas fa-plus me-2"></i>
                إنشاء دور جديد
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="stats-card">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات الأدوار
                </h5>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.total_roles }}</span>
                        <span class="stat-label">إجمالي الأدوار</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.active_roles }}</span>
                        <span class="stat-label">الأدوار النشطة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.inactive_roles }}</span>
                        <span class="stat-label">الأدوار غير النشطة</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Roles List -->
    <div class="row">
        {% for role in roles %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="role-card">
                <div class="role-header">
                    <div>
                        <div class="role-title">{{ role.display_name }}</div>
                        <small class="text-muted">{{ role.name }}</small>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <span class="role-level">المستوى {{ role.level }}</span>
                        {% if role.is_active %}
                        <span class="badge bg-success">نشط</span>
                        {% else %}
                        <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                    </div>
                </div>

                <div class="role-description mb-3">
                    <p class="text-muted mb-0">{{ role.description|default:"لا يوجد وصف" }}</p>
                </div>

                <div class="users-count mb-3">
                    <i class="fas fa-users me-1"></i>
                    {{ role.users_count }} مستخدم
                </div>

                <div class="permissions-grid">
                    <div class="permission-item {% if role.can_manage_users %}active{% else %}inactive{% endif %}">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </div>
                    <div class="permission-item {% if role.can_manage_clients %}active{% else %}inactive{% endif %}">
                        <i class="fas fa-building me-2"></i>
                        إدارة العملاء
                    </div>
                    <div class="permission-item {% if role.can_view_reports %}active{% else %}inactive{% endif %}">
                        <i class="fas fa-chart-line me-2"></i>
                        عرض التقارير
                    </div>
                    <div class="permission-item {% if role.can_manage_visits %}active{% else %}inactive{% endif %}">
                        <i class="fas fa-calendar-check me-2"></i>
                        إدارة الزيارات
                    </div>
                    <div class="permission-item {% if role.can_export_data %}active{% else %}inactive{% endif %}">
                        <i class="fas fa-download me-2"></i>
                        تصدير البيانات
                    </div>
                </div>

                <div class="role-actions">
                    <button class="btn btn-outline-primary btn-sm" onclick="viewRole('{{ role.id }}')">
                        <i class="fas fa-eye"></i>
                        عرض
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="editRole('{{ role.id }}')">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    {% if role.name not in 'super_manager,sales_rep' %}
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteRole('{{ role.id }}', '{{ role.display_name }}')">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-user-shield fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد أدوار</h4>
                <p class="text-muted">لم يتم إنشاء أي أدوار بعد</p>
                <button class="btn btn-primary" onclick="createRole()">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء أول دور
                </button>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- CSRF Token -->
{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
function createRole() {
    Swal.fire({
        title: 'إنشاء دور جديد',
        html: `
            <form id="createRoleForm">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم الدور (بالإنجليزية)</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الاسم المعروض</label>
                        <input type="text" class="form-control" name="display_name" required>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">الوصف</label>
                    <textarea class="form-control" name="description" rows="3"></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">المستوى</label>
                    <select class="form-control" name="level" required>
                        <option value="1">1 - مدير عام</option>
                        <option value="2">2 - مدير إقليمي</option>
                        <option value="3">3 - مدير فرع</option>
                        <option value="4">4 - مشرف مبيعات</option>
                        <option value="5">5 - مندوب مبيعات</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">الصلاحيات</label>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="can_manage_users">
                                <label class="form-check-label">إدارة المستخدمين</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="can_manage_clients">
                                <label class="form-check-label">إدارة العملاء</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="can_view_reports">
                                <label class="form-check-label">عرض التقارير</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="can_manage_visits">
                                <label class="form-check-label">إدارة الزيارات</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="can_export_data">
                                <label class="form-check-label">تصدير البيانات</label>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        `,
        showCancelButton: true,
        confirmButtonText: 'إنشاء الدور',
        cancelButtonText: 'إلغاء',
        width: '600px',
        preConfirm: () => {
            const form = document.getElementById('createRoleForm');
            const formData = new FormData(form);
            
            return fetch('/users/roles/create/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    throw new Error(data.error || 'حدث خطأ غير متوقع');
                }
                return data;
            });
        }
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('تم!', result.value.message, 'success').then(() => {
                location.reload();
            });
        }
    }).catch(error => {
        Swal.fire('خطأ!', error.message, 'error');
    });
}

function viewRole(id) {
    fetch(`/users/roles/${id}/details/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const role = data.role;
                const permissions = [
                    { key: 'can_manage_users', label: 'إدارة المستخدمين' },
                    { key: 'can_manage_clients', label: 'إدارة العملاء' },
                    { key: 'can_view_reports', label: 'عرض التقارير' },
                    { key: 'can_manage_visits', label: 'إدارة الزيارات' },
                    { key: 'can_export_data', label: 'تصدير البيانات' }
                ];
                
                let permissionsHtml = '<div class="permissions-list">';
                permissions.forEach(perm => {
                    const status = role[perm.key] ? 'نعم' : 'لا';
                    const statusClass = role[perm.key] ? 'text-success' : 'text-danger';
                    permissionsHtml += `<p><strong>${perm.label}:</strong> <span class="${statusClass}">${status}</span></p>`;
                });
                permissionsHtml += '</div>';
                
                Swal.fire({
                    title: role.display_name,
                    html: `
                        <div class="text-start">
                            <p><strong>اسم الدور:</strong> ${role.name}</p>
                            <p><strong>الوصف:</strong> ${role.description || 'لا يوجد'}</p>
                            <p><strong>المستوى:</strong> ${role.level}</p>
                            <p><strong>الحالة:</strong> ${role.is_active ? 'نشط' : 'غير نشط'}</p>
                            <p><strong>تاريخ الإنشاء:</strong> ${new Date(role.created_at).toLocaleDateString('ar-SA')}</p>
                            <hr>
                            <h6>الصلاحيات:</h6>
                            ${permissionsHtml}
                        </div>
                    `,
                    icon: 'info',
                    confirmButtonText: 'إغلاق',
                    width: '500px'
                });
            }
        })
        .catch(error => {
            Swal.fire('خطأ!', 'حدث خطأ في تحميل البيانات', 'error');
        });
}

function editRole(id) {
    fetch(`/users/roles/${id}/details/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const role = data.role;
                
                Swal.fire({
                    title: 'تعديل الدور',
                    html: `
                        <form id="editRoleForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اسم الدور</label>
                                    <input type="text" class="form-control" value="${role.name}" disabled>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم المعروض</label>
                                    <input type="text" class="form-control" name="display_name" value="${role.display_name}" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" rows="3">${role.description || ''}</textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المستوى</label>
                                <select class="form-control" name="level" required>
                                    <option value="1" ${role.level == 1 ? 'selected' : ''}>1 - مدير عام</option>
                                    <option value="2" ${role.level == 2 ? 'selected' : ''}>2 - مدير إقليمي</option>
                                    <option value="3" ${role.level == 3 ? 'selected' : ''}>3 - مدير فرع</option>
                                    <option value="4" ${role.level == 4 ? 'selected' : ''}>4 - مشرف مبيعات</option>
                                    <option value="5" ${role.level == 5 ? 'selected' : ''}>5 - مندوب مبيعات</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_active" ${role.is_active ? 'checked' : ''}>
                                    <label class="form-check-label">نشط</label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الصلاحيات</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_manage_users" ${role.can_manage_users ? 'checked' : ''}>
                                            <label class="form-check-label">إدارة المستخدمين</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_manage_clients" ${role.can_manage_clients ? 'checked' : ''}>
                                            <label class="form-check-label">إدارة العملاء</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_view_reports" ${role.can_view_reports ? 'checked' : ''}>
                                            <label class="form-check-label">عرض التقارير</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_manage_visits" ${role.can_manage_visits ? 'checked' : ''}>
                                            <label class="form-check-label">إدارة الزيارات</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_export_data" ${role.can_export_data ? 'checked' : ''}>
                                            <label class="form-check-label">تصدير البيانات</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    `,
                    showCancelButton: true,
                    confirmButtonText: 'حفظ التغييرات',
                    cancelButtonText: 'إلغاء',
                    width: '600px',
                    preConfirm: () => {
                        const form = document.getElementById('editRoleForm');
                        const formData = new FormData(form);
                        
                        return fetch(`/users/roles/${id}/edit/`, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (!data.success) {
                                throw new Error(data.error || 'حدث خطأ غير متوقع');
                            }
                            return data;
                        });
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire('تم!', result.value.message, 'success').then(() => {
                            location.reload();
                        });
                    }
                }).catch(error => {
                    Swal.fire('خطأ!', error.message, 'error');
                });
            }
        });
}

function deleteRole(id, name) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: `سيتم حذف الدور "${name}" نهائياً`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/users/roles/${id}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم الحذف!', data.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.error, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}
</script>
{% endblock %}
