<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/colorPrimary"
        android:elevation="4dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="المهام متعددة الزيارات"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="12dp" />

        <!-- Filter <PERSON> -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/btnAllTasks"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="جميع المهام"
                android:textColor="@android:color/white"
                android:background="@drawable/button_acknowledge"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/btnRecurringOnly"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="المتكررة"
                android:textColor="@android:color/white"
                android:background="@drawable/button_start"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/btnOneTimeOnly"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="لمرة واحدة"
                android:textColor="@android:color/white"
                android:background="@drawable/button_scanner"
                android:layout_marginStart="4dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Stats -->
    <TextView
        android:id="@+id/statsText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:background="@android:color/white"
        android:textSize="14sp"
        android:textColor="@color/colorPrimary"
        android:gravity="center"
        android:visibility="gone" />

    <!-- SwipeRefreshLayout -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewMultiTasks"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="8dp"
                android:clipToPadding="false" />

            <!-- Empty View -->
            <TextView
                android:id="@+id/emptyView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="لا توجد مهام متعددة"
                android:textSize="18sp"
                android:textColor="@android:color/darker_gray"
                android:gravity="center"
                android:visibility="gone" />

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>
