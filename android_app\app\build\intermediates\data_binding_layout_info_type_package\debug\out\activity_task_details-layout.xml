<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_task_details" modulePackage="com.company.fieldsalestracker" filePath="app\src\main\res\layout\activity_task_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_task_details_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="323" endOffset="12"/></Target><Target id="@+id/titleText" view="TextView"><Expressions/><location startLine="29" startOffset="16" endLine="37" endOffset="55"/></Target><Target id="@+id/taskIdText" view="TextView"><Expressions/><location startLine="39" startOffset="16" endLine="46" endOffset="55"/></Target><Target id="@+id/taskTypeText" view="TextView"><Expressions/><location startLine="48" startOffset="16" endLine="55" endOffset="55"/></Target><Target id="@+id/tvTaskTitle" view="TextView"><Expressions/><location startLine="57" startOffset="16" endLine="66" endOffset="47"/></Target><Target id="@+id/tvTaskDescription" view="TextView"><Expressions/><location startLine="68" startOffset="16" endLine="75" endOffset="47"/></Target><Target id="@+id/cardStatus" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="89" startOffset="12" endLine="123" endOffset="47"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="113" startOffset="20" endLine="119" endOffset="50"/></Target><Target id="@+id/cardPriority" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="126" startOffset="12" endLine="160" endOffset="47"/></Target><Target id="@+id/tvPriority" view="TextView"><Expressions/><location startLine="150" startOffset="20" endLine="156" endOffset="50"/></Target><Target id="@+id/tvClientName" view="TextView"><Expressions/><location startLine="187" startOffset="16" endLine="197" endOffset="55"/></Target><Target id="@+id/tvClientAddress" view="TextView"><Expressions/><location startLine="199" startOffset="16" endLine="208" endOffset="55"/></Target><Target id="@+id/tvAssignedAt" view="TextView"><Expressions/><location startLine="237" startOffset="16" endLine="247" endOffset="55"/></Target><Target id="@+id/tvDueDate" view="TextView"><Expressions/><location startLine="249" startOffset="16" endLine="260" endOffset="47"/></Target><Target id="@+id/tvAssignedBy" view="TextView"><Expressions/><location startLine="262" startOffset="16" endLine="271" endOffset="55"/></Target><Target id="@+id/btnAcknowledge" view="Button"><Expressions/><location startLine="284" startOffset="12" endLine="294" endOffset="43"/></Target><Target id="@+id/btnStart" view="Button"><Expressions/><location startLine="296" startOffset="12" endLine="306" endOffset="43"/></Target><Target id="@+id/btnOpenScanner" view="Button"><Expressions/><location startLine="308" startOffset="12" endLine="317" endOffset="43"/></Target></Targets></Layout>