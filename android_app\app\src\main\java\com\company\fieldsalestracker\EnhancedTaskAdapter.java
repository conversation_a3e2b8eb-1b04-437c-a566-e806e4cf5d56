package com.company.fieldsalestracker;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class EnhancedTaskAdapter extends RecyclerView.Adapter<EnhancedTaskAdapter.TaskViewHolder> {
    
    private List<ApiService.EnhancedVisit> tasks;
    private OnTaskClickListener listener;
    
    public interface OnTaskClickListener {
        void onTaskClick(ApiService.EnhancedVisit task);
    }
    
    public EnhancedTaskAdapter(List<ApiService.EnhancedVisit> tasks, OnTaskClickListener listener) {
        this.tasks = tasks;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public TaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_enhanced_task, parent, false);
        return new TaskViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull TaskViewHolder holder, int position) {
        ApiService.EnhancedVisit task = tasks.get(position);
        holder.bind(task, listener);
    }
    
    @Override
    public int getItemCount() {
        return tasks.size();
    }
    
    static class TaskViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private TextView titleText;
        private TextView clientText;
        private TextView statusText;
        private TextView priorityText;
        private TextView dueDateText;
        private TextView descriptionText;
        private TextView taskTypeText;
        private ImageView statusIcon;
        private ImageView priorityIcon;
        private ImageView taskTypeIcon;
        
        public TaskViewHolder(@NonNull View itemView) {
            super(itemView);
            
            cardView = itemView.findViewById(R.id.cardView);
            titleText = itemView.findViewById(R.id.titleText);
            clientText = itemView.findViewById(R.id.clientText);
            statusText = itemView.findViewById(R.id.statusText);
            priorityText = itemView.findViewById(R.id.priorityText);
            dueDateText = itemView.findViewById(R.id.dueDateText);
            descriptionText = itemView.findViewById(R.id.descriptionText);
            taskTypeText = itemView.findViewById(R.id.taskTypeText);
            statusIcon = itemView.findViewById(R.id.statusIcon);
            priorityIcon = itemView.findViewById(R.id.priorityIcon);
            taskTypeIcon = itemView.findViewById(R.id.taskTypeIcon);
        }
        
        public void bind(ApiService.EnhancedVisit task, OnTaskClickListener listener) {
            // عنوان المهمة
            titleText.setText(task.task_title != null ? task.task_title : "مهمة زيارة");
            
            // العميل
            if (task.client != null) {
                clientText.setText(task.client.name);
                clientText.setVisibility(View.VISIBLE);
            } else {
                clientText.setVisibility(View.GONE);
            }
            
            // حالة المهمة
            setupTaskStatus(task);
            
            // أولوية المهمة
            setupPriority(task);
            
            // تاريخ الاستحقاق
            setupDueDate(task);
            
            // وصف المهمة
            if (task.task_description != null && !task.task_description.isEmpty()) {
                descriptionText.setText(task.task_description);
                descriptionText.setVisibility(View.VISIBLE);
            } else {
                descriptionText.setVisibility(View.GONE);
            }
            
            // نوع المهمة
            setupTaskType(task);
            
            // إعداد النقر
            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onTaskClick(task);
                }
            });
            
            // تلوين البطاقة حسب الحالة
            setupCardColor(task);
        }
        
        private void setupTaskStatus(ApiService.EnhancedVisit task) {
            if (task.task_status_display != null) {
                statusText.setText(task.task_status_display);
            } else {
                statusText.setText("غير محدد");
            }
            
            // تلوين حسب الحالة
            switch (task.task_status != null ? task.task_status : "") {
                case "assigned":
                    statusText.setTextColor(Color.parseColor("#FFA500")); // برتقالي
                    statusIcon.setImageResource(R.drawable.ic_assignment);
                    break;
                case "acknowledged":
                    statusText.setTextColor(Color.parseColor("#2196F3")); // أزرق
                    statusIcon.setImageResource(R.drawable.ic_check_circle);
                    break;
                case "in_progress":
                    statusText.setTextColor(Color.parseColor("#2196F3")); // أزرق
                    statusIcon.setImageResource(R.drawable.ic_play_arrow);
                    break;
                case "completed":
                    statusText.setTextColor(Color.parseColor("#4CAF50")); // أخضر
                    statusIcon.setImageResource(R.drawable.ic_check_circle);
                    break;
                default:
                    statusText.setTextColor(Color.parseColor("#757575")); // رمادي
                    statusIcon.setImageResource(R.drawable.ic_help);
                    break;
            }
        }
        
        private void setupPriority(ApiService.EnhancedVisit task) {
            if (task.priority_display != null) {
                priorityText.setText(task.priority_display);
            } else {
                priorityText.setText("عادية");
            }
            
            // تلوين حسب الأولوية
            switch (task.priority != null ? task.priority : "medium") {
                case "high":
                    priorityText.setTextColor(Color.parseColor("#F44336")); // أحمر
                    priorityIcon.setImageResource(R.drawable.ic_priority_high);
                    break;
                case "medium":
                    priorityText.setTextColor(Color.parseColor("#FF9800")); // برتقالي
                    priorityIcon.setImageResource(R.drawable.ic_priority_medium);
                    break;
                case "low":
                    priorityText.setTextColor(Color.parseColor("#4CAF50")); // أخضر
                    priorityIcon.setImageResource(R.drawable.ic_priority_low);
                    break;
                default:
                    priorityText.setTextColor(Color.parseColor("#757575")); // رمادي
                    priorityIcon.setImageResource(R.drawable.ic_priority_medium);
                    break;
            }
        }
        
        private void setupDueDate(ApiService.EnhancedVisit task) {
            if (task.visit_datetime != null) {
                try {
                    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault());
                    SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
                    
                    Date date = inputFormat.parse(task.visit_datetime);
                    if (date != null) {
                        dueDateText.setText("موعد الزيارة: " + outputFormat.format(date));
                        
                        // تحقق من التأخير
                        if (date.before(new Date())) {
                            dueDateText.setTextColor(Color.parseColor("#F44336")); // أحمر للمتأخر
                        } else {
                            dueDateText.setTextColor(Color.parseColor("#757575")); // رمادي عادي
                        }
                    }
                } catch (ParseException e) {
                    dueDateText.setText("تاريخ غير صحيح");
                }
                dueDateText.setVisibility(View.VISIBLE);
            } else {
                dueDateText.setVisibility(View.GONE);
            }
        }
        
        private void setupTaskType(ApiService.EnhancedVisit task) {
            String taskType = "";
            int iconResource = R.drawable.ic_task;
            
            if (task.is_recurring_task) {
                taskType = "مهمة متكررة";
                iconResource = R.drawable.ic_repeat;
            } else if (task.is_parent_task) {
                taskType = "مهمة متعددة";
                iconResource = R.drawable.ic_group;
            } else if (task.is_child_visit) {
                taskType = "زيارة فرعية";
                iconResource = R.drawable.ic_subdirectory_arrow_right;
            } else {
                taskType = "مهمة عادية";
                iconResource = R.drawable.ic_task;
            }
            
            taskTypeText.setText(taskType);
            taskTypeIcon.setImageResource(iconResource);
            
            // إضافة معلومات إضافية للمهام المتعددة
            if (task.is_parent_task && task.child_visits_count > 0) {
                String additionalInfo = String.format(" (%d/%d زيارات)", 
                    task.completed_child_visits_count, task.child_visits_count);
                taskTypeText.setText(taskType + additionalInfo);
            }
        }
        
        private void setupCardColor(ApiService.EnhancedVisit task) {
            int borderColor = Color.parseColor("#E0E0E0"); // رمادي افتراضي
            
            // تلوين حسب نوع المهمة
            if (task.is_recurring_task) {
                borderColor = Color.parseColor("#4CAF50"); // أخضر للمتكررة
            } else if (task.is_parent_task) {
                borderColor = Color.parseColor("#2196F3"); // أزرق للمتعددة
            } else if (task.is_child_visit) {
                borderColor = Color.parseColor("#FF9800"); // برتقالي للفرعية
            }
            
            // يمكن إضافة تأثير الحدود هنا إذا كان التصميم يدعم ذلك
            // cardView.setCardBackgroundColor(borderColor);
        }
    }
}
