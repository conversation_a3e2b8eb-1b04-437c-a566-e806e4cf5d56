plugins {
    id 'com.android.application'
}

android {
    namespace 'com.company.fieldsalestracker'
    compileSdk 33

    defaultConfig {
        applicationId "com.company.fieldsalestracker"
        minSdk 24
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    // حل مشكلة تضارب مكتبات Kotlin
    configurations.all {
        resolutionStrategy {
            force 'org.jetbrains.kotlin:kotlin-stdlib:1.8.20'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20'
        }

        // استبعاد المكتبات القديمة المتضاربة
        exclude group: 'org.jetbrains.kotlin', module: 'kotlin-stdlib-jdk7'
        exclude group: 'org.jetbrains.kotlin', module: 'kotlin-stdlib-jdk8'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    // فرض استخدام إصدار موحد من Kotlin stdlib
    implementation 'org.jetbrains.kotlin:kotlin-stdlib:1.8.20'

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2'
    implementation 'androidx.navigation:navigation-fragment:2.6.0'
    implementation 'androidx.navigation:navigation-ui:2.6.0'

    // Camera and Image handling
    implementation 'androidx.camera:camera-core:1.2.3'
    implementation 'androidx.camera:camera-camera2:1.2.3'
    implementation 'androidx.camera:camera-lifecycle:1.2.3'
    implementation 'androidx.camera:camera-view:1.2.3'
    implementation 'androidx.camera:camera-extensions:1.2.3'

    // ML Kit for barcode scanning
    implementation 'com.google.mlkit:barcode-scanning:17.1.0'

    // Location services
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation 'com.google.android.gms:play-services-maps:18.1.0'

    // Networking
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.11.0'

    // Image loading
    implementation 'com.github.bumptech.glide:glide:4.15.1'

    // Permissions
    implementation 'pub.devrel:easypermissions:3.0.0'

    // SwipeRefreshLayout
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}

// حل شامل لمشكلة تضارب مكتبات Kotlin
configurations.all {
    resolutionStrategy.eachDependency { details ->
        if (details.requested.group == 'org.jetbrains.kotlin') {
            if (details.requested.name == 'kotlin-stdlib-jdk7' ||
                details.requested.name == 'kotlin-stdlib-jdk8') {
                details.useTarget 'org.jetbrains.kotlin:kotlin-stdlib:1.8.20'
                details.because 'حل تضارب مكتبات Kotlin'
            }
        }
    }
}
