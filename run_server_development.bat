@echo off
echo 🔓 تشغيل الخادم بإعدادات التطوير (بدون فحوصات أمان)
echo ⚠️  تحذير: هذه الإعدادات للتطوير فقط!
echo.

REM تفعيل البيئة الافتراضية
if exist "venv\Scripts\activate.bat" (
    echo 🔄 تفعيل البيئة الافتراضية...
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  لم يتم العثور على البيئة الافتراضية
)

REM تشغيل الخادم بإعدادات التطوير
echo 🚀 تشغيل خادم Django...
echo 📡 الخادم سيعمل على: http://0.0.0.0:9000
echo 🌐 يمكن الوصول من أي IP في الشبكة
echo.

set DJANGO_SETTINGS_MODULE=field_sales_tracker.settings_development
python manage.py runserver 0.0.0.0:9000

pause
