{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/management-pages.css' %}">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style>
.category-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
}
.category-type-1 { background-color: #e3f2fd; color: #1976d2; }
.category-type-2 { background-color: #f3e5f5; color: #7b1fa2; }
.category-type-3 { background-color: #e8f5e8; color: #388e3c; }

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
    background: rgba(255,255,255,0.1);
    padding: 1rem;
    border-radius: 10px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-layer-group me-2"></i>
                {{ title }}
            </h1>
            <p class="text-muted mb-0">إدارة وتنظيم جميع التصنيفات في النظام</p>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="fas fa-plus me-2"></i>
                إضافة تصنيف جديد
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="stats-card">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات التصنيفات
                </h5>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.total }}</span>
                        <span class="stat-label">إجمالي التصنيفات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.active }}</span>
                        <span class="stat-label">التصنيفات النشطة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.type_1 }}</span>
                        <span class="stat-label">تصنيفات المدير العام</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.type_2 }}</span>
                        <span class="stat-label">تصنيفات المستخدمين</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.type_3 }}</span>
                        <span class="stat-label">تصنيفات المناديب</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.inactive }}</span>
                        <span class="stat-label">غير نشطة</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث في التصنيفات..." 
                           value="{{ search_query }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع التصنيف</label>
                    <select name="type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        {% for type_id, type_name in category_types %}
                        <option value="{{ type_id }}" {% if category_type == type_id|stringformat:"s" %}selected{% endif %}>
                            {{ type_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                        <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة التصنيفات
            </h5>
            <div>
                <button class="btn btn-success btn-sm" onclick="exportCategories()">
                    <i class="fas fa-download me-1"></i>
                    تصدير Excel
                </button>
            </div>
        </div>
        <div class="card-body">
            {% if categories %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>اسم التصنيف</th>
                            <th>النوع</th>
                            <th>الوصف</th>
                            <th>عدد المستخدمين</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories %}
                        <tr>
                            <td>
                                <strong>
                                    <a href="{% url 'categories:users' category.id %}" class="text-decoration-none">
                                        {{ category.name }}
                                    </a>
                                </strong>
                            </td>
                            <td>
                                <span class="badge category-type-badge category-type-{{ category.category_type }}">
                                    {% for type_id, type_name in category_types %}
                                        {% if type_id == category.category_type %}{{ type_name }}{% endif %}
                                    {% endfor %}
                                </span>
                            </td>
                            <td>{{ category.description|default:"-"|truncatechars:50 }}</td>
                            <td>
                                <span class="badge bg-info">{{ category.users_count|default:0 }}</span>
                            </td>
                            <td>
                                {% if category.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>{{ category.created_at|date:"Y-m-d" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-info" onclick="assignUsers('{{ category.id }}', '{{ category.name }}', {{ category.category_type }})" title="إضافة مستخدمين">
                                        <i class="fas fa-users"></i>
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="viewCategory('{{ category.id }}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="editCategory('{{ category.id }}')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteCategory('{{ category.id }}', '{{ category.name }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if categories.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if categories.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ categories.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_type %}&type={{ category_type }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابق</a>
                    </li>
                    {% endif %}

                    {% for num in categories.paginator.page_range %}
                    {% if categories.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > categories.number|add:'-3' and num < categories.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_type %}&type={{ category_type }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if categories.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ categories.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_type %}&type={{ category_type }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد تصنيفات</h4>
                <p class="text-muted">لم يتم العثور على أي تصنيفات مطابقة للبحث</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول تصنيف
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة تصنيف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCategoryForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">اسم التصنيف</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نوع التصنيف</label>
                        <select name="category_type" class="form-select" required>
                            <option value="">اختر النوع</option>
                            {% for type_id, type_name in category_types %}
                            <option value="{{ type_id }}">{{ type_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea name="description" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add Category Form
document.getElementById('addCategoryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{% url "categories:create_ajax" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('خطأ: ' + (data.error || 'حدث خطأ غير متوقع'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
});

function viewCategory(id) {
    fetch(`/categories/${id}/details/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const category = data.category;
                const typeText = category.category_type == 1 ? 'تصنيفات المدير العام' :
                               category.category_type == 2 ? 'تصنيفات المستخدمين' :
                               category.category_type == 3 ? 'تصنيفات المناديب' : 'تصنيفات العملاء';

                Swal.fire({
                    title: category.name,
                    html: `
                        <div class="text-start">
                            <p><strong>الوصف:</strong> ${category.description || 'لا يوجد'}</p>
                            <p><strong>النوع:</strong> ${typeText}</p>
                            <p><strong>الحالة:</strong> ${category.is_active ? 'نشط' : 'غير نشط'}</p>
                            <p><strong>تاريخ الإنشاء:</strong> ${new Date(category.created_at).toLocaleDateString('ar-SA')}</p>
                        </div>
                    `,
                    icon: 'info',
                    confirmButtonText: 'إغلاق'
                });
            }
        })
        .catch(error => {
            Swal.fire('خطأ!', 'حدث خطأ في تحميل البيانات', 'error');
        });
}

function editCategory(id) {
    fetch(`/categories/${id}/details/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const category = data.category;

                Swal.fire({
                    title: 'تعديل التصنيف',
                    html: `
                        <form id="editCategoryForm">
                            <div class="mb-3">
                                <label class="form-label">اسم التصنيف</label>
                                <input type="text" class="form-control" name="name" value="${category.name}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" rows="3">${category.description || ''}</textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">نوع التصنيف</label>
                                <select class="form-control" name="category_type" required>
                                    <option value="1" ${category.category_type == 1 ? 'selected' : ''}>تصنيفات المدير العام</option>
                                    <option value="2" ${category.category_type == 2 ? 'selected' : ''}>تصنيفات المستخدمين</option>
                                    <option value="3" ${category.category_type == 3 ? 'selected' : ''}>تصنيفات المناديب</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_active" ${category.is_active ? 'checked' : ''}>
                                    <label class="form-check-label">نشط</label>
                                </div>
                            </div>
                        </form>
                    `,
                    showCancelButton: true,
                    confirmButtonText: 'حفظ التغييرات',
                    cancelButtonText: 'إلغاء',
                    preConfirm: () => {
                        const form = document.getElementById('editCategoryForm');
                        const formData = new FormData(form);

                        return fetch(`/categories/${id}/edit/`, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (!data.success) {
                                throw new Error(data.error || 'حدث خطأ غير متوقع');
                            }
                            return data;
                        });
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire('تم!', result.value.message, 'success').then(() => {
                            location.reload();
                        });
                    }
                }).catch(error => {
                    Swal.fire('خطأ!', error.message, 'error');
                });
            }
        });
}

function deleteCategory(id, name) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: `سيتم حذف التصنيف "${name}" نهائياً`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/categories/${id}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم الحذف!', data.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.error, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

function assignUsers(categoryId, categoryName, categoryType) {
    fetch(`/categories/${categoryId}/assign-users/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const users = data.users;
                const typeText = categoryType == 2 ? 'المستخدمين' : categoryType == 3 ? 'المناديب' : 'المدراء';

                let usersHtml = `<div class="mb-3"><h6>إضافة ${typeText} للتصنيف: ${categoryName}</h6></div>`;

                if (users.length === 0) {
                    usersHtml += '<p class="text-muted">لا يوجد مستخدمين متاحين</p>';
                } else {
                    usersHtml += '<div class="user-list" style="max-height: 300px; overflow-y: auto;">';
                    users.forEach(user => {
                        const roleText = user.role_type == 1 ? 'مدير عام' :
                                       user.role_type == 2 ? 'مدير مستخدمين' : 'مندوب مبيعات';

                        usersHtml += `
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="${user.id}"
                                       ${user.is_assigned ? 'checked' : ''} id="user_${user.id}">
                                <label class="form-check-label" for="user_${user.id}">
                                    <strong>${user.name}</strong> (${user.username}) - ${roleText}
                                </label>
                            </div>
                        `;
                    });
                    usersHtml += '</div>';
                }

                Swal.fire({
                    title: 'إدارة المستخدمين',
                    html: usersHtml,
                    showCancelButton: true,
                    confirmButtonText: 'حفظ التغييرات',
                    cancelButtonText: 'إلغاء',
                    width: '600px',
                    preConfirm: () => {
                        const checkedUsers = Array.from(document.querySelectorAll('.form-check-input:checked'))
                                                 .map(cb => cb.value);

                        const formData = new FormData();
                        checkedUsers.forEach(userId => {
                            formData.append('user_ids', userId);
                        });

                        return fetch(`/categories/${categoryId}/assign-users/`, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (!data.success) {
                                throw new Error(data.error || 'حدث خطأ غير متوقع');
                            }
                            return data;
                        });
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire('تم!', result.value.message, 'success');
                    }
                });
            }
        })
        .catch(error => {
            Swal.fire('خطأ!', 'حدث خطأ في تحميل البيانات', 'error');
        });
}

function exportCategories() {
    Swal.fire('قريباً!', 'وظيفة التصدير ستكون متاحة قريباً', 'info');
}
</script>
{% endblock %}
