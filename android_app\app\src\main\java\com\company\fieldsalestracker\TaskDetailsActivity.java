package com.company.fieldsalestracker;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class TaskDetailsActivity extends AppCompatActivity {
    
    private String taskId;
    private ApiService.Task currentTask;
    
    // Views
    private TextView tvTaskTitle, tvTaskDescription, tvClientName, tvClientAddress;
    private TextView tvStatus, tvPriority, tvAssignedAt, tvDueDate, tvAssignedBy;
    private CardView cardStatus, cardPriority;
    private Button btnAcknowledge, btnStart, btnOpenScanner;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_task_details);
        
        // Get task ID from intent
        taskId = getIntent().getStringExtra("task_id");
        if (taskId == null) {
            Toast.makeText(this, "خطأ: معرف المهمة غير موجود", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        initViews();
        loadTaskDetails();
    }
    
    private void initViews() {
        // Setup toolbar
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("تفاصيل المهمة");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        // Initialize views
        tvTaskTitle = findViewById(R.id.tvTaskTitle);
        tvTaskDescription = findViewById(R.id.tvTaskDescription);
        tvClientName = findViewById(R.id.tvClientName);
        tvClientAddress = findViewById(R.id.tvClientAddress);
        tvStatus = findViewById(R.id.tvStatus);
        tvPriority = findViewById(R.id.tvPriority);
        tvAssignedAt = findViewById(R.id.tvAssignedAt);
        tvDueDate = findViewById(R.id.tvDueDate);
        tvAssignedBy = findViewById(R.id.tvAssignedBy);
        
        cardStatus = findViewById(R.id.cardStatus);
        cardPriority = findViewById(R.id.cardPriority);
        
        btnAcknowledge = findViewById(R.id.btnAcknowledge);
        btnStart = findViewById(R.id.btnStart);
        btnOpenScanner = findViewById(R.id.btnOpenScanner);
        
        // Setup click listeners
        btnAcknowledge.setOnClickListener(v -> acknowledgeTask());
        btnStart.setOnClickListener(v -> startTask());
        btnOpenScanner.setOnClickListener(v -> openBarcodeScanner());
    }
    
    private void loadTaskDetails() {
        // For now, we'll use a simple approach since we don't have a specific task details API
        // In a real implementation, you would call an API to get task details
        
        // Show loading state
        Toast.makeText(this, "جاري تحميل تفاصيل المهمة...", Toast.LENGTH_SHORT).show();
        
        // For demonstration, we'll create a mock task
        // In real implementation, replace this with actual API call
        createMockTaskForDemo();
    }
    
    private void createMockTaskForDemo() {
        // This is just for demonstration
        // In real implementation, you would call the API
        currentTask = new ApiService.Task();
        currentTask.id = taskId;
        currentTask.title = "زيارة عميل مهمة";
        currentTask.description = "يرجى زيارة العميل وتسجيل الزيارة";
        currentTask.status = "assigned";
        currentTask.status_display = "مُكلف";
        currentTask.priority = "medium";
        currentTask.priority_display = "متوسط";
        currentTask.assigned_at = "2025-01-20T10:00:00";
        currentTask.due_date = "2025-01-21T17:00:00";
        currentTask.is_overdue = false;
        
        // Mock client
        currentTask.client = new ApiService.Client();
        currentTask.client.id = "client-123";
        currentTask.client.name = "شركة الرياض التجارية";
        currentTask.client.address = "شارع الملك فهد، الرياض";
        
        // Mock assigned by
        currentTask.assigned_by = new ApiService.User();
        currentTask.assigned_by.first_name = "أحمد";
        currentTask.assigned_by.last_name = "المدير";
        
        updateUI();
    }
    
    private void updateUI() {
        if (currentTask == null) return;
        
        // Set task info
        tvTaskTitle.setText(currentTask.title != null ? currentTask.title : "مهمة زيارة");
        
        if (currentTask.description != null && !currentTask.description.isEmpty()) {
            tvTaskDescription.setText(currentTask.description);
            tvTaskDescription.setVisibility(View.VISIBLE);
        } else {
            tvTaskDescription.setVisibility(View.GONE);
        }
        
        // Set client info
        if (currentTask.client != null) {
            tvClientName.setText(currentTask.client.name);
            tvClientAddress.setText(currentTask.client.address);
        }
        
        // Set status
        tvStatus.setText(currentTask.status_display != null ? currentTask.status_display : currentTask.status);
        setStatusColor(tvStatus, cardStatus, currentTask.status);
        
        // Set priority
        tvPriority.setText(currentTask.priority_display != null ? currentTask.priority_display : currentTask.priority);
        setPriorityColor(tvPriority, cardPriority, currentTask.priority);
        
        // Set dates
        if (currentTask.assigned_at != null) {
            tvAssignedAt.setText("تاريخ التكليف: " + formatDate(currentTask.assigned_at));
        }
        
        if (currentTask.due_date != null) {
            tvDueDate.setText("الموعد المحدد: " + formatDate(currentTask.due_date));
            tvDueDate.setVisibility(View.VISIBLE);
            
            if (currentTask.is_overdue) {
                tvDueDate.setTextColor(Color.RED);
            }
        } else {
            tvDueDate.setVisibility(View.GONE);
        }
        
        // Set assigned by
        if (currentTask.assigned_by != null) {
            tvAssignedBy.setText("كُلف من: " + currentTask.assigned_by.getFullName());
        }
        
        // Setup buttons
        setupButtons();
    }
    
    private void setupButtons() {
        // Hide all buttons first
        btnAcknowledge.setVisibility(View.GONE);
        btnStart.setVisibility(View.GONE);
        btnOpenScanner.setVisibility(View.GONE);
        
        if (currentTask.status != null) {
            switch (currentTask.status) {
                case "assigned":
                    btnAcknowledge.setVisibility(View.VISIBLE);
                    break;
                case "acknowledged":
                    btnStart.setVisibility(View.VISIBLE);
                    break;
                case "in_progress":
                    btnOpenScanner.setVisibility(View.VISIBLE);
                    break;
                case "completed":
                    // No buttons for completed tasks
                    break;
                case "cancelled":
                    // No buttons for cancelled tasks
                    break;
            }
        }
    }
    
    private void acknowledgeTask() {
        ApiService apiService = ApiClient.getApiService();
        Call<ApiService.TaskActionResponse> call = apiService.acknowledgeTask(taskId);
        
        call.enqueue(new Callback<ApiService.TaskActionResponse>() {
            @Override
            public void onResponse(Call<ApiService.TaskActionResponse> call, Response<ApiService.TaskActionResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiService.TaskActionResponse actionResponse = response.body();
                    if (actionResponse.success) {
                        Toast.makeText(TaskDetailsActivity.this, actionResponse.message, Toast.LENGTH_SHORT).show();
                        // Update task status
                        currentTask.status = "acknowledged";
                        currentTask.status_display = "تم الاطلاع";
                        updateUI();
                    } else {
                        Toast.makeText(TaskDetailsActivity.this, "فشل في تأكيد الاطلاع", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(TaskDetailsActivity.this, "خطأ في الاتصال", Toast.LENGTH_SHORT).show();
                }
            }
            
            @Override
            public void onFailure(Call<ApiService.TaskActionResponse> call, Throwable t) {
                Toast.makeText(TaskDetailsActivity.this, "خطأ في الشبكة", Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    private void startTask() {
        ApiService apiService = ApiClient.getApiService();
        Call<ApiService.TaskActionResponse> call = apiService.startTask(taskId);
        
        call.enqueue(new Callback<ApiService.TaskActionResponse>() {
            @Override
            public void onResponse(Call<ApiService.TaskActionResponse> call, Response<ApiService.TaskActionResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiService.TaskActionResponse actionResponse = response.body();
                    if (actionResponse.success) {
                        Toast.makeText(TaskDetailsActivity.this, actionResponse.message, Toast.LENGTH_SHORT).show();
                        // Update task status
                        currentTask.status = "in_progress";
                        currentTask.status_display = "قيد التنفيذ";
                        updateUI();
                    } else {
                        Toast.makeText(TaskDetailsActivity.this, "فشل في بدء المهمة", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(TaskDetailsActivity.this, "خطأ في الاتصال", Toast.LENGTH_SHORT).show();
                }
            }
            
            @Override
            public void onFailure(Call<ApiService.TaskActionResponse> call, Throwable t) {
                Toast.makeText(TaskDetailsActivity.this, "خطأ في الشبكة", Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    private void openBarcodeScanner() {
        if (currentTask != null && currentTask.client != null) {
            Intent intent = new Intent(this, BarcodeScannerActivity.class);
            intent.putExtra("task_id", taskId);
            intent.putExtra("client_id", currentTask.client.id);
            intent.putExtra("client_name", currentTask.client.name);
            startActivity(intent);
        }
    }
    
    private void setStatusColor(TextView textView, CardView cardView, String status) {
        int color = Color.parseColor("#757575"); // Default gray
        
        if (status != null) {
            switch (status) {
                case "assigned":
                    color = Color.parseColor("#FF9800"); // Orange
                    break;
                case "acknowledged":
                    color = Color.parseColor("#2196F3"); // Blue
                    break;
                case "in_progress":
                    color = Color.parseColor("#9C27B0"); // Purple
                    break;
                case "completed":
                    color = Color.parseColor("#4CAF50"); // Green
                    break;
                case "cancelled":
                    color = Color.parseColor("#F44336"); // Red
                    break;
            }
        }
        
        textView.setTextColor(color);
        if (cardView != null) {
            cardView.setCardBackgroundColor(Color.argb(30, Color.red(color), Color.green(color), Color.blue(color)));
        }
    }
    
    private void setPriorityColor(TextView textView, CardView cardView, String priority) {
        int color = Color.parseColor("#757575"); // Default gray
        
        if (priority != null) {
            switch (priority) {
                case "urgent":
                    color = Color.parseColor("#F44336"); // Red
                    break;
                case "high":
                    color = Color.parseColor("#FF9800"); // Orange
                    break;
                case "medium":
                    color = Color.parseColor("#2196F3"); // Blue
                    break;
                case "low":
                    color = Color.parseColor("#757575"); // Gray
                    break;
            }
        }
        
        textView.setTextColor(color);
        if (cardView != null) {
            cardView.setCardBackgroundColor(Color.argb(30, Color.red(color), Color.green(color), Color.blue(color)));
        }
    }
    
    private String formatDate(String dateString) {
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault());
            SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
            Date date = inputFormat.parse(dateString);
            return outputFormat.format(date);
        } catch (ParseException e) {
            return dateString;
        }
    }
    
    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
