R_DEF: Internal format may change without notice
local
anim button_scale_down
anim button_scale_up
color background_color
color black
color border_color
color btn_camera_border
color btn_camera_end
color btn_camera_pressed_end
color btn_camera_pressed_start
color btn_camera_start
color btn_submit_border
color btn_submit_end
color btn_submit_pressed_end
color btn_submit_pressed_start
color btn_submit_start
color card_background
color colorAccent
color colorPrimary
color colorPrimaryDark
color divider_color
color error_background
color error_color
color info_background
color info_color
color overlay_dark
color overlay_light
color primary_color
color primary_dark
color primary_light
color purple_200
color purple_500
color purple_700
color secondary_color
color secondary_dark
color secondary_light
color success_background
color success_color
color surface_color
color teal_200
color teal_700
color text_hint
color text_primary
color text_secondary
color text_white
color warning_background
color warning_color
color white
drawable badge_background
drawable btn_camera_background
drawable btn_submit_background
drawable button_acknowledge
drawable button_scanner
drawable button_start
drawable circle_background
drawable ic_alarm
drawable ic_arrow_back
drawable ic_arrow_forward
drawable ic_assignment
drawable ic_business
drawable ic_camera
drawable ic_camera_enhanced
drawable ic_check_circle
drawable ic_enhanced_task
drawable ic_event
drawable ic_group
drawable ic_help
drawable ic_launcher
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_launcher_simple
drawable ic_list
drawable ic_location
drawable ic_location_on
drawable ic_lock
drawable ic_logout
drawable ic_multi_task
drawable ic_person
drawable ic_play_arrow
drawable ic_priority_high
drawable ic_priority_low
drawable ic_priority_medium
drawable ic_qr_code_scanner
drawable ic_refresh
drawable ic_repeat
drawable ic_schedule
drawable ic_send
drawable ic_send_enhanced
drawable ic_subdirectory_arrow_right
drawable ic_task
drawable login_background
drawable scanner_overlay
drawable splash_background
drawable status_background
id action_enhanced_tasks
id action_logout
id action_multi_tasks
id action_my_tasks
id action_my_visits
id action_refresh
id bottomPanel
id btnAcknowledge
id btnAllTasks
id btnBack
id btnCreateTask
id btnLogin
id btnMultiTasks
id btnOneTimeOnly
id btnOpenScanner
id btnRecurringOnly
id btnStart
id btnSubmitVisit
id btnTakePhoto
id btnVisitClient
id cardPriority
id cardStatus
id cardView
id clientText
id descriptionText
id detailsButton
id dueDateText
id emptyState
id emptyView
id etNotes
id etPassword
id etUsername
id etVisitNotes
id executeButton
id executionsText
id fabRefresh
id fabScanBarcode
id instructionsPanel
id ivLogo
id ivVisitPhoto
id loginCard
id logoContainer
id nextExecutionText
id previewView
id priorityIcon
id priorityText
id progressBar
id progressText
id recyclerViewClients
id recyclerViewMultiTasks
id recyclerViewTasks
id recyclerViewVisits
id salesRepText
id statsText
id statusIcon
id statusText
id swipeRefresh
id swipeRefreshLayout
id taskTypeIcon
id taskTypeText
id textAssignedAt
id textAssignedBy
id textClient
id textDescription
id textDueDate
id textPriority
id textStatus
id textTitle
id tilPassword
id tilUsername
id titleText
id toolbar
id tvAppName
id tvAssignedAt
id tvAssignedBy
id tvClientAddress
id tvClientBarcode
id tvClientDistance
id tvClientName
id tvClientPhone
id tvDueDate
id tvPriority
id tvRejectionReason
id tvStatus
id tvSubtitle
id tvTaskDescription
id tvTaskTitle
id tvVisitStatus
id tvVisitTime
layout activity_barcode_scanner
layout activity_enhanced_tasks
layout activity_login
layout activity_main
layout activity_multi_tasks
layout activity_my_visits
layout activity_splash
layout activity_task_details
layout activity_tasks
layout activity_visit_details
layout item_client
layout item_enhanced_task
layout item_multi_task
layout item_task
layout item_visit
menu main_menu
mipmap ic_launcher
mipmap ic_launcher_round
string all_tasks
string app_name
string barcode_detected
string camera_permission_required
string cancel
string child_visits
string client_address
string client_distance
string client_name
string client_not_found
string client_phone
string clients_title
string enhanced_tasks
string enhanced_tasks_title
string execute_task
string executions_count
string filter_by_status
string filter_by_type
string invalid_barcode
string loading
string loading_tasks
string location_permission_required
string location_too_far
string login_button
string login_error
string login_title
string logout
string multi_tasks
string multi_tasks_title
string my_tasks
string my_visits
string network_error
string next_execution
string no_tasks
string no_tasks_found
string notes_hint
string ok
string one_time_tasks
string password
string permissions_required
string photo_taken
string progress_visits
string recurring_tasks
string refresh
string refresh_tasks
string retry
string scan_barcode
string scanner_instruction
string scanner_title
string search_tasks
string server_error
string submit_visit
string success_rate
string take_photo
string task_acknowledge
string task_acknowledged
string task_assigned_at
string task_assigned_by
string task_continue
string task_details
string task_due_date
string task_due_now
string task_due_soon
string task_executed_successfully
string task_not_due
string task_overdue
string task_priority_high
string task_priority_low
string task_priority_medium
string task_priority_urgent
string task_start
string task_started
string task_status_acknowledged
string task_status_assigned
string task_status_cancelled
string task_status_completed
string task_status_in_progress
string task_type_child
string task_type_multi
string task_type_normal
string task_type_recurring
string tasks_title
string unknown_error
string username
string visit_error
string visit_notes
string visit_submitted
string visit_success
string visit_title
string visits_created
style ActionBarTitle
style CustomActionBar
style CustomButton
style CustomCard
style CustomFAB
style CustomTextInputLayout
style TextBody
style TextCaption
style TextHeadline
style TextTitle
style Theme.FieldSalesTracker
style Theme.FieldSalesTracker.NoActionBar
style Theme.FieldSalesTracker.Splash
xml backup_rules
xml data_extraction_rules
xml file_paths
xml network_security_config
