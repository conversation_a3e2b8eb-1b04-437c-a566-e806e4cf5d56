# Generated by Django 5.2.4 on 2025-07-20 06:40

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('categories', '0001_initial'),
        ('users', '0002_role_rename_is_active_rep_user_is_active_employee_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='user',
            options={'ordering': ['role_type', 'first_name', 'last_name'], 'verbose_name': 'مستخدم', 'verbose_name_plural': 'المستخدمون'},
        ),
        migrations.RemoveField(
            model_name='user',
            name='manager',
        ),
        migrations.RemoveField(
            model_name='user',
            name='organization_unit',
        ),
        migrations.RemoveField(
            model_name='user',
            name='role',
        ),
        migrations.AddField(
            model_name='user',
            name='categories',
            field=models.JSONField(blank=True, default=list, help_text='قائمة معرفات التصنيفات المرتبطة بالمستخدم', verbose_name='التصنيفات'),
        ),
        migrations.AddField(
            model_name='user',
            name='direct_managers',
            field=models.JSONField(blank=True, default=list, help_text='قائمة معرفات المدراء المباشرين', verbose_name='المدراء المباشرين'),
        ),
        migrations.AddField(
            model_name='user',
            name='managed_categories',
            field=models.JSONField(blank=True, default=list, help_text='قائمة معرفات التصنيفات المدارة', verbose_name='التصنيفات المدارة'),
        ),
        migrations.AddField(
            model_name='user',
            name='managed_users',
            field=models.JSONField(blank=True, default=list, help_text='قائمة معرفات المستخدمين المدارين مباشرة', verbose_name='المستخدمين المدارين'),
        ),
        migrations.AddField(
            model_name='user',
            name='permissions',
            field=models.JSONField(blank=True, default=dict, help_text='صلاحيات إضافية مخصصة للمستخدم', verbose_name='الصلاحيات المخصصة'),
        ),
        migrations.AddField(
            model_name='user',
            name='role_type',
            field=models.IntegerField(choices=[(1, 'مدير عام'), (2, 'مستخدم'), (3, 'مندوب مبيعات')], default=3, help_text='تحديد نوع دور المستخدم في النظام', verbose_name='نوع الدور'),
        ),
        migrations.AlterField(
            model_name='user',
            name='address',
            field=models.TextField(blank=True, null=True, verbose_name='العنوان'),
        ),
        migrations.AlterField(
            model_name='user',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='user',
            name='emergency_contact',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='جهة اتصال الطوارئ'),
        ),
        migrations.AlterField(
            model_name='user',
            name='emergency_phone',
            field=models.CharField(blank=True, max_length=15, null=True, verbose_name='هاتف الطوارئ'),
        ),
        migrations.AlterField(
            model_name='user',
            name='employee_id',
            field=models.CharField(blank=True, max_length=50, null=True, unique=True, verbose_name='رقم الموظف'),
        ),
        migrations.AlterField(
            model_name='user',
            name='hire_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ التوظيف'),
        ),
        migrations.AlterField(
            model_name='user',
            name='is_active_employee',
            field=models.BooleanField(default=True, verbose_name='موظف نشط'),
        ),
        migrations.AlterField(
            model_name='user',
            name='national_id',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهوية'),
        ),
        migrations.AlterField(
            model_name='user',
            name='phone_number',
            field=models.CharField(blank=True, max_length=15, null=True, verbose_name='رقم الهاتف'),
        ),
        migrations.AlterField(
            model_name='user',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.CreateModel(
            name='UserHierarchy',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('relationship_type', models.CharField(choices=[('direct_management', 'إدارة مباشرة'), ('category_management', 'إدارة عبر التصنيف'), ('temporary_assignment', 'تكليف مؤقت')], default='direct_management', max_length=50, verbose_name='نوع العلاقة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('start_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ البداية')),
                ('end_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ النهاية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hierarchy_relationships', to='categories.category', verbose_name='التصنيف')),
                ('manager', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hierarchy_as_manager', to=settings.AUTH_USER_MODEL, verbose_name='المدير')),
                ('subordinate', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hierarchy_as_subordinate', to=settings.AUTH_USER_MODEL, verbose_name='المُدار')),
            ],
            options={
                'verbose_name': 'علاقة هرمية',
                'verbose_name_plural': 'العلاقات الهرمية',
                'db_table': 'user_hierarchy',
                'unique_together': {('manager', 'category', 'relationship_type'), ('manager', 'subordinate', 'relationship_type')},
            },
        ),
    ]
