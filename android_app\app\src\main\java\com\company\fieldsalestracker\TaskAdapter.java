package com.company.fieldsalestracker;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class TaskAdapter extends RecyclerView.Adapter<TaskAdapter.TaskViewHolder> {
    
    private List<ApiService.Task> taskList;
    private OnTaskClickListener listener;
    
    public interface OnTaskClickListener {
        void onTaskClick(ApiService.Task task);
        void onAcknowledgeClick(ApiService.Task task);
        void onStartClick(ApiService.Task task);
    }
    
    public TaskAdapter(List<ApiService.Task> taskList, OnTaskClickListener listener) {
        this.taskList = taskList;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public TaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_task, parent, false);
        return new TaskViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull TaskViewHolder holder, int position) {
        ApiService.Task task = taskList.get(position);
        
        // Set task title
        holder.textTitle.setText(task.title != null ? task.title : "مهمة زيارة");
        
        // Set client name
        if (task.client != null) {
            holder.textClient.setText("العميل: " + task.client.name);
        }
        
        // Set description
        if (task.description != null && !task.description.isEmpty()) {
            holder.textDescription.setText(task.description);
            holder.textDescription.setVisibility(View.VISIBLE);
        } else {
            holder.textDescription.setVisibility(View.GONE);
        }
        
        // Set status
        holder.textStatus.setText(task.status_display != null ? task.status_display : task.status);
        setStatusColor(holder.textStatus, task.status);
        
        // Set priority
        holder.textPriority.setText(task.priority_display != null ? task.priority_display : task.priority);
        setPriorityColor(holder.textPriority, task.priority);
        
        // Set dates
        if (task.assigned_at != null) {
            holder.textAssignedAt.setText("تاريخ التكليف: " + formatDate(task.assigned_at));
        }
        
        if (task.due_date != null) {
            holder.textDueDate.setText("الموعد المحدد: " + formatDate(task.due_date));
            holder.textDueDate.setVisibility(View.VISIBLE);
            
            // Highlight overdue tasks
            if (task.is_overdue) {
                holder.textDueDate.setTextColor(Color.RED);
                holder.cardView.setCardBackgroundColor(Color.parseColor("#FFEBEE"));
            }
        } else {
            holder.textDueDate.setVisibility(View.GONE);
        }
        
        // Set assigned by
        if (task.assigned_by != null) {
            holder.textAssignedBy.setText("كُلف من: " + task.assigned_by.getFullName());
        }
        
        // Setup buttons based on task status
        setupButtons(holder, task);
        
        // Set click listeners
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onTaskClick(task);
            }
        });
        
        holder.btnAcknowledge.setOnClickListener(v -> {
            if (listener != null) {
                listener.onAcknowledgeClick(task);
            }
        });
        
        holder.btnStart.setOnClickListener(v -> {
            if (listener != null) {
                listener.onStartClick(task);
            }
        });
    }
    
    private void setupButtons(TaskViewHolder holder, ApiService.Task task) {
        // Hide all buttons first
        holder.btnAcknowledge.setVisibility(View.GONE);
        holder.btnStart.setVisibility(View.GONE);
        
        if (task.status != null) {
            switch (task.status) {
                case "assigned":
                    holder.btnAcknowledge.setVisibility(View.VISIBLE);
                    holder.btnAcknowledge.setText("تأكيد الاطلاع");
                    break;
                case "acknowledged":
                    holder.btnStart.setVisibility(View.VISIBLE);
                    holder.btnStart.setText("بدء التنفيذ");
                    break;
                case "in_progress":
                    if (!task.visit_completed) {
                        holder.btnStart.setVisibility(View.VISIBLE);
                        holder.btnStart.setText("متابعة التنفيذ");
                    }
                    break;
                case "completed":
                    // No buttons for completed tasks
                    break;
                case "cancelled":
                    // No buttons for cancelled tasks
                    break;
            }
        }
    }
    
    private void setStatusColor(TextView textView, String status) {
        if (status != null) {
            switch (status) {
                case "assigned":
                    textView.setTextColor(Color.parseColor("#FF9800")); // Orange
                    break;
                case "acknowledged":
                    textView.setTextColor(Color.parseColor("#2196F3")); // Blue
                    break;
                case "in_progress":
                    textView.setTextColor(Color.parseColor("#9C27B0")); // Purple
                    break;
                case "completed":
                    textView.setTextColor(Color.parseColor("#4CAF50")); // Green
                    break;
                case "cancelled":
                    textView.setTextColor(Color.parseColor("#F44336")); // Red
                    break;
                default:
                    textView.setTextColor(Color.parseColor("#757575")); // Gray
                    break;
            }
        }
    }
    
    private void setPriorityColor(TextView textView, String priority) {
        if (priority != null) {
            switch (priority) {
                case "urgent":
                    textView.setTextColor(Color.parseColor("#F44336")); // Red
                    break;
                case "high":
                    textView.setTextColor(Color.parseColor("#FF9800")); // Orange
                    break;
                case "medium":
                    textView.setTextColor(Color.parseColor("#2196F3")); // Blue
                    break;
                case "low":
                    textView.setTextColor(Color.parseColor("#757575")); // Gray
                    break;
                default:
                    textView.setTextColor(Color.parseColor("#757575")); // Gray
                    break;
            }
        }
    }
    
    private String formatDate(String dateString) {
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault());
            SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
            Date date = inputFormat.parse(dateString);
            return outputFormat.format(date);
        } catch (ParseException e) {
            return dateString; // Return original if parsing fails
        }
    }
    
    @Override
    public int getItemCount() {
        return taskList.size();
    }
    
    static class TaskViewHolder extends RecyclerView.ViewHolder {
        CardView cardView;
        TextView textTitle, textClient, textDescription, textStatus, textPriority;
        TextView textAssignedAt, textDueDate, textAssignedBy;
        Button btnAcknowledge, btnStart;
        
        public TaskViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.cardView);
            textTitle = itemView.findViewById(R.id.textTitle);
            textClient = itemView.findViewById(R.id.textClient);
            textDescription = itemView.findViewById(R.id.textDescription);
            textStatus = itemView.findViewById(R.id.textStatus);
            textPriority = itemView.findViewById(R.id.textPriority);
            textAssignedAt = itemView.findViewById(R.id.textAssignedAt);
            textDueDate = itemView.findViewById(R.id.textDueDate);
            textAssignedBy = itemView.findViewById(R.id.textAssignedBy);
            btnAcknowledge = itemView.findViewById(R.id.btnAcknowledge);
            btnStart = itemView.findViewById(R.id.btnStart);
        }
    }
}
