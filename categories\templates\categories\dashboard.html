{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}لوحة تحكم التصنيفات{% endblock %}

{% block extra_css %}
<style>
    .category-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }
    
    .category-type-0 { border-left: 4px solid #e74c3c; }
    .category-type-1 { border-left: 4px solid #3498db; }
    .category-type-2 { border-left: 4px solid #2ecc71; }
    .category-type-3 { border-left: 4px solid #f39c12; }
    .category-type-4 { border-left: 4px solid #9b59b6; }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    
    .quick-action-btn {
        border-radius: 10px;
        padding: 15px;
        text-decoration: none;
        color: white;
        display: block;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .quick-action-btn:hover {
        transform: translateY(-2px);
        color: white;
    }
    
    .quick-action-btn.blue { background: linear-gradient(45deg, #3498db, #2980b9); }
    .quick-action-btn.green { background: linear-gradient(45deg, #2ecc71, #27ae60); }
    .quick-action-btn.orange { background: linear-gradient(45deg, #f39c12, #e67e22); }
    .quick-action-btn.purple { background: linear-gradient(45deg, #9b59b6, #8e44ad); }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">لوحة تحكم التصنيفات</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-left">
                        <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">التصنيفات</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ total_categories }}</h3>
                            <p>إجمالي التصنيفات</p>
                        </div>
                        <div class="icon">
                            <i class="bi bi-tags"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ active_categories }}</h3>
                            <p>التصنيفات النشطة</p>
                        </div>
                        <div class="icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ category_types_count }}</h3>
                            <p>أنواع التصنيفات</p>
                        </div>
                        <div class="icon">
                            <i class="bi bi-collection"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ recent_categories_count }}</h3>
                            <p>تصنيفات حديثة</p>
                        </div>
                        <div class="icon">
                            <i class="bi bi-clock"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-lightning-charge me-2"></i>
                                إجراءات سريعة
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="{% url 'categories:list' %}" class="quick-action-btn blue">
                                        <i class="bi bi-list me-2"></i>
                                        جميع التصنيفات
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{% url 'categories:list' %}?type=1" class="quick-action-btn green">
                                        <i class="bi bi-people me-2"></i>
                                        تصنيفات المستخدمين
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{% url 'categories:list' %}?type=2" class="quick-action-btn orange">
                                        <i class="bi bi-person-badge me-2"></i>
                                        تصنيفات المناديب
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{% url 'categories:list' %}?type=3" class="quick-action-btn purple">
                                        <i class="bi bi-building me-2"></i>
                                        تصنيفات العملاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أنواع التصنيفات -->
            <div class="row">
                {% for cat_type, type_name in category_types %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card category-card category-type-{{ cat_type }}">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-tag me-2"></i>
                                {{ type_name }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center">
                                        <h4 class="text-primary">-</h4>
                                        <small class="text-muted">التصنيفات</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <h4 class="text-success">-</h4>
                                        <small class="text-muted">النشطة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <a href="{% url 'categories:list_by_type' cat_type %}"
                                   class="btn btn-primary btn-sm me-2">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض
                                </a>
                                <a href="#" onclick="createCategory({{ cat_type }})"
                                   class="btn btn-success btn-sm">
                                    <i class="bi bi-plus me-1"></i>
                                    إضافة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- التصنيفات الحديثة -->
            {% if recent_categories %}
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-clock-history me-2"></i>
                                التصنيفات الحديثة
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>النوع</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for category in recent_categories %}
                                        <tr>
                                            <td>
                                                <strong>{{ category.name }}</strong>
                                                {% if category.description %}
                                                <br><small class="text-muted">{{ category.description|truncatechars:50 }}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ category.get_category_type_display }}</span>
                                            </td>
                                            <td>
                                                {% if category.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                                {% else %}
                                                <span class="badge bg-secondary">غير نشط</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ category.created_at|date:"Y-m-d H:i" }}</td>
                                            <td>
                                                <a href="{% url 'categories:edit' pk=category.pk %}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <a href="{% url 'categories:users' pk=category.pk %}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="bi bi-people"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

        </div>
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثيرات الحركة للبطاقات
    const cards = document.querySelectorAll('.category-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate__animated', 'animate__fadeInUp');
    });
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(function() {
        // يمكن إضافة AJAX لتحديث الإحصائيات
        console.log('تحديث الإحصائيات...');
    }, 30000);
});

// دالة إنشاء تصنيف جديد
function createCategory(categoryType) {
    const categoryName = prompt('أدخل اسم التصنيف الجديد:');
    if (categoryName && categoryName.trim()) {
        // يمكن إضافة AJAX لإنشاء التصنيف
        alert('سيتم إضافة وظيفة إنشاء التصنيف قريباً');
        console.log('إنشاء تصنيف:', categoryName, 'نوع:', categoryType);
    }
}
});
</script>
{% endblock %}
