from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import random

from clients.models import Client
from visits.models import Visit

User = get_user_model()

class Command(BaseCommand):
    help = 'إنشاء زيارات تجريبية'

    def handle(self, *args, **options):
        self.stdout.write("📍 إنشاء الزيارات التجريبية...")
        
        # الحصول على المناديب والعملاء
        sales_reps = User.objects.filter(role_type=3)
        clients = Client.objects.all()
        
        self.stdout.write(f"المناديب المتاحين: {sales_reps.count()}")
        self.stdout.write(f"العملاء المتاحين: {clients.count()}")
        
        if not sales_reps.exists():
            self.stdout.write(self.style.ERROR("لا توجد مناديب في النظام"))
            return
            
        if not clients.exists():
            self.stdout.write(self.style.ERROR("لا توجد عملاء في النظام"))
            return
        
        # إنشاء زيارات للأسبوعين الماضيين
        start_date = timezone.now() - timedelta(days=14)
        visit_statuses = ['pending', 'verified', 'rejected']
        visit_types = ['regular', 'follow_up', 'complaint', 'delivery']
        visit_notes = ['روتينية', 'متابعة', 'طلب خاص', 'شكوى']
        
        visits_created = 0
        
        for day in range(14):
            visit_date = start_date + timedelta(days=day)
            daily_visits = random.randint(3, 8)
            
            self.stdout.write(f"إنشاء {daily_visits} زيارة لتاريخ {visit_date.date()}")
            
            for _ in range(daily_visits):
                rep = random.choice(list(sales_reps))
                client = random.choice(list(clients))
                
                # تجنب الزيارات المكررة لنفس العميل في نفس اليوم
                existing_visit = Visit.objects.filter(
                    sales_rep=rep,
                    client=client,
                    visit_datetime__date=visit_date.date()
                ).exists()

                if existing_visit:
                    continue

                try:
                    visit = Visit.objects.create(
                        sales_rep=rep,
                        client=client,
                        visit_datetime=visit_date,
                        visit_latitude=float(client.latitude) + random.uniform(-0.001, 0.001),
                        visit_longitude=float(client.longitude) + random.uniform(-0.001, 0.001),
                        notes=f"زيارة {random.choice(visit_notes)} للعميل {client.name}",
                        status=random.choice(visit_statuses),
                        task_type=random.choice(visit_types),
                        distance_from_client=random.uniform(5, 50)
                    )
                    visits_created += 1
                    
                except Exception as e:
                    self.stdout.write(f"خطأ في إنشاء زيارة: {str(e)}")
        
        self.stdout.write(self.style.SUCCESS(f"✅ تم إنشاء {visits_created} زيارة بنجاح"))
        
        # إحصائيات الزيارات
        total_visits = Visit.objects.count()
        pending_visits = Visit.objects.filter(status='pending').count()
        verified_visits = Visit.objects.filter(status='verified').count()
        rejected_visits = Visit.objects.filter(status='rejected').count()
        
        self.stdout.write("\n📊 إحصائيات الزيارات:")
        self.stdout.write(f"إجمالي الزيارات: {total_visits}")
        self.stdout.write(f"زيارات معلقة: {pending_visits}")
        self.stdout.write(f"زيارات مؤكدة: {verified_visits}")
        self.stdout.write(f"زيارات مرفوضة: {rejected_visits}")
