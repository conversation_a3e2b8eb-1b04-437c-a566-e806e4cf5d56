package com.company.fieldsalestracker;

import android.content.Context;
import android.content.SharedPreferences;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class ApiClient {
    // استخدام العنوان من ملف التكوين المركزي
    private static final String BASE_URL = Config.BASE_URL;

    private static final String PREF_NAME = "FieldSalesTracker";
    private static final String KEY_TOKEN = "auth_token";
    private static final String KEY_USER_ID = "user_id";
    private static final String KEY_USERNAME = "username";

    private static Retrofit retrofit;
    private static ApiService apiService;
    private static Context appContext;
    
    public static void init(Context context) {
        appContext = context.getApplicationContext();
        setupRetrofit();
    }


    
    private static void setupRetrofit() {
        // Logging interceptor for debugging
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        
        // Auth interceptor to add token to requests
        Interceptor authInterceptor = new Interceptor() {
            @Override
            public Response intercept(Chain chain) throws IOException {
                Request originalRequest = chain.request();
                String token = getAuthToken();
                
                if (token != null && !token.isEmpty()) {
                    Request authenticatedRequest = originalRequest.newBuilder()
                            .header("Authorization", "Token " + token)
                            .build();
                    return chain.proceed(authenticatedRequest);
                }
                
                return chain.proceed(originalRequest);
            }
        };
        
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(authInterceptor)
                .addInterceptor(loggingInterceptor)
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
        
        retrofit = new Retrofit.Builder()
                .baseUrl(BASE_URL)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        
        apiService = retrofit.create(ApiService.class);
    }
    
    public static ApiService getApiService() {
        if (apiService == null) {
            throw new IllegalStateException("ApiClient not initialized. Call init() first.");
        }
        return apiService;
    }

    // Backward compatibility method
    public static Retrofit getClient() {
        if (retrofit == null) {
            throw new IllegalStateException("ApiClient not initialized. Call init() first.");
        }
        return retrofit;
    }
    
    // Authentication token management
    public static void saveAuthToken(String token) {
        SharedPreferences prefs = appContext.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        prefs.edit().putString(KEY_TOKEN, token).apply();
    }
    
    public static String getAuthToken() {
        SharedPreferences prefs = appContext.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_TOKEN, null);
    }
    
    public static void clearAuthToken() {
        SharedPreferences prefs = appContext.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        prefs.edit().remove(KEY_TOKEN).apply();
    }
    
    public static boolean isLoggedIn() {
        String token = getAuthToken();
        return token != null && !token.isEmpty();
    }
    
    // User data management
    public static void saveUserData(String userId, String username) {
        SharedPreferences prefs = appContext.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        prefs.edit()
                .putString(KEY_USER_ID, userId)
                .putString(KEY_USERNAME, username)
                .apply();
    }
    
    public static String getUserId() {
        SharedPreferences prefs = appContext.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_USER_ID, null);
    }
    
    public static String getUsername() {
        SharedPreferences prefs = appContext.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_USERNAME, null);
    }
    
    public static void clearUserData() {
        SharedPreferences prefs = appContext.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        prefs.edit()
                .remove(KEY_USER_ID)
                .remove(KEY_USERNAME)
                .apply();
    }
    
    public static void logout() {
        clearAuthToken();
        clearUserData();
    }
    
    // Utility methods
    public static String getBaseUrl() {
        return BASE_URL;
    }
}
