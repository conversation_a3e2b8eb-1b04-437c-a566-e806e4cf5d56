<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_multi_task" modulePackage="com.company.fieldsalestracker" filePath="app\src\main\res\layout\item_multi_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/cardView"><Targets><Target id="@+id/cardView" tag="layout/item_multi_task_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="164" endOffset="35"/></Target><Target id="@+id/taskTypeIcon" view="ImageView"><Expressions/><location startLine="27" startOffset="12" endLine="32" endOffset="48"/></Target><Target id="@+id/titleText" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="43" endOffset="58"/></Target><Target id="@+id/statusIcon" view="ImageView"><Expressions/><location startLine="46" startOffset="12" endLine="51" endOffset="50"/></Target><Target id="@+id/salesRepText" view="TextView"><Expressions/><location startLine="56" startOffset="8" endLine="65" endOffset="43"/></Target><Target id="@+id/taskTypeText" view="TextView"><Expressions/><location startLine="68" startOffset="8" endLine="75" endOffset="47"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="78" startOffset="8" endLine="84" endOffset="47"/></Target><Target id="@+id/progressText" view="TextView"><Expressions/><location startLine="93" startOffset="12" endLine="100" endOffset="51"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="102" startOffset="12" endLine="108" endOffset="38"/></Target><Target id="@+id/nextExecutionText" view="TextView"><Expressions/><location startLine="113" startOffset="8" endLine="122" endOffset="43"/></Target><Target id="@+id/executionsText" view="TextView"><Expressions/><location startLine="125" startOffset="8" endLine="132" endOffset="48"/></Target><Target id="@+id/detailsButton" view="Button"><Expressions/><location startLine="141" startOffset="12" endLine="149" endOffset="48"/></Target><Target id="@+id/executeButton" view="Button"><Expressions/><location startLine="151" startOffset="12" endLine="158" endOffset="58"/></Target></Targets></Layout>