<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_task_details" modulePackage="com.company.fieldsalestracker" filePath="app\src\main\res\layout\activity_task_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_task_details_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="294" endOffset="12"/></Target><Target id="@+id/tvTaskTitle" view="TextView"><Expressions/><location startLine="29" startOffset="16" endLine="37" endOffset="55"/></Target><Target id="@+id/tvTaskDescription" view="TextView"><Expressions/><location startLine="39" startOffset="16" endLine="46" endOffset="47"/></Target><Target id="@+id/cardStatus" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="60" startOffset="12" endLine="94" endOffset="47"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="84" startOffset="20" endLine="90" endOffset="50"/></Target><Target id="@+id/cardPriority" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="97" startOffset="12" endLine="131" endOffset="47"/></Target><Target id="@+id/tvPriority" view="TextView"><Expressions/><location startLine="121" startOffset="20" endLine="127" endOffset="50"/></Target><Target id="@+id/tvClientName" view="TextView"><Expressions/><location startLine="158" startOffset="16" endLine="168" endOffset="55"/></Target><Target id="@+id/tvClientAddress" view="TextView"><Expressions/><location startLine="170" startOffset="16" endLine="179" endOffset="55"/></Target><Target id="@+id/tvAssignedAt" view="TextView"><Expressions/><location startLine="208" startOffset="16" endLine="218" endOffset="55"/></Target><Target id="@+id/tvDueDate" view="TextView"><Expressions/><location startLine="220" startOffset="16" endLine="231" endOffset="47"/></Target><Target id="@+id/tvAssignedBy" view="TextView"><Expressions/><location startLine="233" startOffset="16" endLine="242" endOffset="55"/></Target><Target id="@+id/btnAcknowledge" view="Button"><Expressions/><location startLine="255" startOffset="12" endLine="265" endOffset="43"/></Target><Target id="@+id/btnStart" view="Button"><Expressions/><location startLine="267" startOffset="12" endLine="277" endOffset="43"/></Target><Target id="@+id/btnOpenScanner" view="Button"><Expressions/><location startLine="279" startOffset="12" endLine="288" endOffset="43"/></Target></Targets></Layout>