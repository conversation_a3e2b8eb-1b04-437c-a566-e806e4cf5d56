package com.company.fieldsalestracker;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import java.util.ArrayList;
import java.util.List;

public class MultiTasksActivity extends AppCompatActivity {
    private static final String TAG = "MultiTasksActivity";
    
    private RecyclerView recyclerView;
    private MultiTaskAdapter adapter;
    private ProgressBar progressBar;
    private TextView emptyView;
    private SwipeRefreshLayout swipeRefresh;
    private TextView statsText;
    private Button btnRecurringOnly;
    private Button btnOneTimeOnly;
    private Button btnAllTasks;
    
    private ApiService apiService;
    private SharedPreferences sharedPreferences;
    private List<ApiService.MultiTask> tasksList = new ArrayList<>();
    private String currentFilter = ""; // all, recurring, one_time
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_multi_tasks);
        
        initViews();
        setupRecyclerView();
        setupApiService();
        loadMultiTasks();
    }
    
    private void initViews() {
        recyclerView = findViewById(R.id.recyclerViewMultiTasks);
        progressBar = findViewById(R.id.progressBar);
        emptyView = findViewById(R.id.emptyView);
        swipeRefresh = findViewById(R.id.swipeRefresh);
        statsText = findViewById(R.id.statsText);
        btnRecurringOnly = findViewById(R.id.btnRecurringOnly);
        btnOneTimeOnly = findViewById(R.id.btnOneTimeOnly);
        btnAllTasks = findViewById(R.id.btnAllTasks);
        
        // Setup toolbar
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("المهام متعددة الزيارات");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        // Setup click listeners
        swipeRefresh.setOnRefreshListener(this::loadMultiTasks);
        
        btnAllTasks.setOnClickListener(v -> {
            currentFilter = "";
            updateFilterButtons();
            loadMultiTasks();
        });
        
        btnRecurringOnly.setOnClickListener(v -> {
            currentFilter = "recurring";
            updateFilterButtons();
            loadMultiTasks();
        });
        
        btnOneTimeOnly.setOnClickListener(v -> {
            currentFilter = "one_time";
            updateFilterButtons();
            loadMultiTasks();
        });
        
        updateFilterButtons();
    }
    
    private void setupRecyclerView() {
        adapter = new MultiTaskAdapter(tasksList, this::onTaskClick, this::onExecuteTask);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }
    
    private void setupApiService() {
        apiService = ApiClient.getClient().create(ApiService.class);
        sharedPreferences = getSharedPreferences("app_prefs", MODE_PRIVATE);
    }
    
    private void loadMultiTasks() {
        showLoading(true);
        
        Call<ApiService.MultiTasksResponse> call = apiService.getMultiTasks(
            null, // search
            null, // status
            currentFilter.isEmpty() ? null : currentFilter, // recurrence filter
            1 // page
        );
        
        call.enqueue(new Callback<ApiService.MultiTasksResponse>() {
            @Override
            public void onResponse(Call<ApiService.MultiTasksResponse> call, 
                                 Response<ApiService.MultiTasksResponse> response) {
                showLoading(false);
                
                if (response.isSuccessful() && response.body() != null) {
                    ApiService.MultiTasksResponse tasksResponse = response.body();
                    
                    if (tasksResponse.success) {
                        tasksList.clear();
                        tasksList.addAll(tasksResponse.parent_tasks);
                        adapter.notifyDataSetChanged();
                        updateUI();
                        updateStats(tasksResponse.stats);
                        
                        Log.d(TAG, "تم تحميل " + tasksList.size() + " مهمة متعددة");
                    } else {
                        showError("فشل في تحميل المهام المتعددة");
                    }
                } else {
                    showError("خطأ في الاتصال: " + response.code());
                }
            }
            
            @Override
            public void onFailure(Call<ApiService.MultiTasksResponse> call, Throwable t) {
                showLoading(false);
                showError("خطأ في الشبكة: " + t.getMessage());
                Log.e(TAG, "خطأ في تحميل المهام المتعددة", t);
            }
        });
    }
    
    private void onTaskClick(ApiService.MultiTask task) {
        Intent intent = new Intent(this, MultiTaskDetailsActivity.class);
        intent.putExtra("task_id", task.id);
        intent.putExtra("task_title", task.task_title);
        intent.putExtra("is_recurring", task.is_recurring_task);
        startActivity(intent);
    }
    
    private void onExecuteTask(ApiService.MultiTask task) {
        if (!task.is_due_for_execution) {
            Toast.makeText(this, "هذه المهمة ليست مستحقة للتنفيذ حالياً", Toast.LENGTH_SHORT).show();
            return;
        }
        
        executeRecurringTask(task.id);
    }
    
    private void executeRecurringTask(String taskId) {
        progressBar.setVisibility(View.VISIBLE);
        
        Call<ApiService.TaskExecutionResponse> call = apiService.executeRecurringTask(taskId);
        
        call.enqueue(new Callback<ApiService.TaskExecutionResponse>() {
            @Override
            public void onResponse(Call<ApiService.TaskExecutionResponse> call, 
                                 Response<ApiService.TaskExecutionResponse> response) {
                progressBar.setVisibility(View.GONE);
                
                if (response.isSuccessful() && response.body() != null) {
                    ApiService.TaskExecutionResponse executionResponse = response.body();
                    
                    if (executionResponse.success) {
                        String message = String.format("تم تنفيذ المهمة بنجاح!\nتم إنشاء %d زيارة جديدة", 
                            executionResponse.visits_created);
                        Toast.makeText(MultiTasksActivity.this, message, Toast.LENGTH_LONG).show();
                        
                        // إعادة تحميل القائمة
                        loadMultiTasks();
                    } else {
                        showError("فشل في تنفيذ المهمة: " + executionResponse.message);
                    }
                } else {
                    showError("خطأ في تنفيذ المهمة: " + response.code());
                }
            }
            
            @Override
            public void onFailure(Call<ApiService.TaskExecutionResponse> call, Throwable t) {
                progressBar.setVisibility(View.GONE);
                showError("خطأ في الشبكة: " + t.getMessage());
                Log.e(TAG, "خطأ في تنفيذ المهمة", t);
            }
        });
    }
    
    private void updateFilterButtons() {
        // إعادة تعيين ألوان الأزرار
        btnAllTasks.setBackgroundColor(getResources().getColor(android.R.color.darker_gray));
        btnRecurringOnly.setBackgroundColor(getResources().getColor(android.R.color.darker_gray));
        btnOneTimeOnly.setBackgroundColor(getResources().getColor(android.R.color.darker_gray));
        
        // تمييز الزر النشط
        switch (currentFilter) {
            case "recurring":
                btnRecurringOnly.setBackgroundColor(getResources().getColor(android.R.color.holo_blue_dark));
                break;
            case "one_time":
                btnOneTimeOnly.setBackgroundColor(getResources().getColor(android.R.color.holo_blue_dark));
                break;
            default:
                btnAllTasks.setBackgroundColor(getResources().getColor(android.R.color.holo_blue_dark));
                break;
        }
    }
    
    private void showLoading(boolean show) {
        progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        swipeRefresh.setRefreshing(false);
    }
    
    private void updateUI() {
        if (tasksList.isEmpty()) {
            recyclerView.setVisibility(View.GONE);
            emptyView.setVisibility(View.VISIBLE);
            
            String emptyMessage;
            switch (currentFilter) {
                case "recurring":
                    emptyMessage = "لا توجد مهام متكررة";
                    break;
                case "one_time":
                    emptyMessage = "لا توجد مهام لمرة واحدة";
                    break;
                default:
                    emptyMessage = "لا توجد مهام متعددة الزيارات";
                    break;
            }
            emptyView.setText(emptyMessage);
        } else {
            recyclerView.setVisibility(View.VISIBLE);
            emptyView.setVisibility(View.GONE);
        }
    }
    
    private void updateStats(ApiService.MultiTaskStats stats) {
        if (stats != null) {
            String statsMessage = String.format(
                "إجمالي: %d | متكررة: %d | لمرة واحدة: %d | نشطة: %d",
                stats.total_tasks,
                stats.recurring_tasks,
                stats.one_time_tasks,
                stats.active_tasks
            );
            statsText.setText(statsMessage);
            statsText.setVisibility(View.VISIBLE);
        } else {
            statsText.setVisibility(View.GONE);
        }
    }
    
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
        Log.e(TAG, message);
    }
    
    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // إعادة تحميل المهام عند العودة للنشاط
        loadMultiTasks();
    }
}
