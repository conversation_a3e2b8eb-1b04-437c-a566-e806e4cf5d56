from django.urls import path
from . import views

app_name = 'categories'

urlpatterns = [
    # لوحة التحكم الرئيسية
    path('', views.categories_dashboard, name='dashboard'),
    path('statistics/', views.categories_statistics, name='statistics'),

    # إدارة التصنيفات - URLs محددة أولاً
    path('list/', views.categories_list, name='list'),
    path('export/', views.categories_export, name='export'),

    # APIs - URLs محددة
    path('api/list/', views.categories_api_list, name='api_list'),
    path('api/stats/', views.categories_stats, name='stats'),
    path('api/create/', views.category_create_ajax, name='create_ajax'),
    path('api/tree/<int:category_type>/', views.category_tree_api, name='tree_api'),
    path('api/reorder/', views.category_reorder, name='reorder'),

    # URLs مع أرقام (category_type)
    path('type/<int:category_type>/', views.category_list, name='list_by_type'),
    path('type/<int:category_type>/create/', views.category_create, name='create'),

    # URLs مع UUID - يجب أن تكون في النهاية
    path('edit/<uuid:pk>/', views.category_edit, name='edit'),
    path('delete/<uuid:pk>/', views.category_delete, name='delete'),
    path('<uuid:pk>/edit/', views.edit_category, name='edit_category'),
    path('<uuid:pk>/delete/', views.delete_category, name='delete_category'),
    path('<uuid:pk>/details/', views.get_category_details, name='get_category_details'),
    path('<uuid:pk>/assign-users/', views.assign_users_to_category, name='assign_users_to_category'),
    path('<uuid:pk>/users/', views.category_users, name='users'),
    path('<uuid:pk>/remove-user/', views.remove_user_from_category, name='remove_user'),
]
