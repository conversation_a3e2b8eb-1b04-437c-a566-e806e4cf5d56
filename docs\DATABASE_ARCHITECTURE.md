# 🗄️ **هيكل قاعدة البيانات - نظام تتبع العملاء الميداني**

## 📊 **نظرة عامة**
نظام قاعدة بيانات متكامل لإدارة المبيعات الميدانية مع دعم المهام المتقدمة والزيارات المتعددة.

---

## 🏗️ **الجداول الرئيسية**

### 👤 **1. جدول المستخدمين (Users)**
```sql
- id: Primary Key
- username: اسم المستخدم (فريد)
- email: البريد الإلكتروني
- first_name, last_name: الاسم الأول والأخير
- role_type: نوع الدور (1=مدير عام, 2=مدير مستخدمين, 3=مندوب)
- is_super_manager: مدير عام (Boolean)
- is_user_manager: مدير مستخدمين (Boolean)
- categories: التصنيفات المرتبطة (JSON Array)
- phone_number: رقم الهاتف
- is_active: نشط/غير نشط
- auth_token: رمز المصادقة
- created_at, updated_at: تواريخ الإنشاء والتحديث
```

### 🏢 **2. جدول العملاء (Clients)**
```sql
- id: Primary Key
- name: اسم العميل
- address: العنوان
- phone: رقم الهاتف
- email: البريد الإلكتروني
- latitude, longitude: الإحداثيات الجغرافية
- barcode: الباركود الفريد
- qr_code_image: صورة رمز QR
- category: التصنيف
- is_active: نشط/غير نشط
- created_at, updated_at: تواريخ الإنشاء والتحديث
```

### 📋 **3. جدول الزيارات (Visits)**
```sql
- id: Primary Key (UUID)
- client: مرجع للعميل (Foreign Key)
- sales_rep: مرجع للمندوب (Foreign Key)
- visit_datetime: تاريخ ووقت الزيارة
- status: الحالة (pending, verified, rejected)
- notes: ملاحظات الزيارة
- visit_image: صورة الزيارة
- visit_latitude, visit_longitude: إحداثيات الزيارة
- distance_from_client: المسافة من العميل
- barcode_scanned: الباركود الممسوح
- rejection_reason: سبب الرفض
- verified_at: تاريخ التحقق
- verified_by: من قام بالتحقق

-- حقول المهام المتقدمة
- task_type: نوع المهمة (manager_assigned, self_initiated)
- task_title: عنوان المهمة
- task_description: وصف المهمة
- task_status: حالة المهمة (assigned, acknowledged, in_progress, completed)
- priority: الأولوية (low, medium, high, urgent)
- assigned_at: تاريخ التكليف
- acknowledged_at: تاريخ الإقرار
- started_at: تاريخ البدء
- due_date: تاريخ الاستحقاق

-- حقول المهام المتكررة
- is_recurring_task: مهمة متكررة (Boolean)
- recurrence_type: نوع التكرار (daily, weekly, monthly)
- recurrence_interval: فترة التكرار
- next_execution: التنفيذ التالي
- recurrence_end_date: تاريخ انتهاء التكرار
- max_visits_per_execution: أقصى زيارات لكل تنفيذ

-- حقول المهام المتعددة
- is_parent_task: مهمة أب (Boolean)
- is_child_visit: زيارة فرعية (Boolean)
- parent_task: مرجع للمهمة الأب
- total_executions: إجمالي التنفيذات
- successful_executions: التنفيذات الناجحة

- created_at, updated_at: تواريخ الإنشاء والتحديث
```

### 🏷️ **4. جدول التصنيفات (Categories)**
```sql
- id: Primary Key
- name: اسم التصنيف
- category_type: نوع التصنيف (client, user, region)
- parent: التصنيف الأب (Self Foreign Key)
- is_system: تصنيف نظام (Boolean)
- is_active: نشط/غير نشط
- created_at, updated_at: تواريخ الإنشاء والتحديث
```

---

## 🔗 **العلاقات بين الجداول**

### **1. علاقات المستخدمين:**
- `User` ← `Visit` (One-to-Many): مستخدم واحد له زيارات متعددة
- `User` ← `Category` (Many-to-Many): مستخدم يمكن أن ينتمي لتصنيفات متعددة

### **2. علاقات العملاء:**
- `Client` ← `Visit` (One-to-Many): عميل واحد له زيارات متعددة
- `Client` ← `Category` (Many-to-One): عميل ينتمي لتصنيف واحد

### **3. علاقات الزيارات:**
- `Visit` → `Client` (Many-to-One): زيارات متعددة لعميل واحد
- `Visit` → `User` (Many-to-One): زيارات متعددة لمندوب واحد
- `Visit` ← `Visit` (Self-Reference): المهام الأب والزيارات الفرعية

### **4. علاقات التصنيفات:**
- `Category` ← `Category` (Self-Reference): تصنيفات هرمية
- `Category` ← `Client` (One-to-Many): تصنيف واحد له عملاء متعددون

---

## 📈 **الحقول المحسوبة والخصائص**

### **خصائص المهام:**
```python
@property
def child_visits_count(self):
    """عدد الزيارات الفرعية"""
    return self.child_visits.count()

@property
def completed_child_visits_count(self):
    """عدد الزيارات الفرعية المكتملة"""
    return self.child_visits.filter(status='verified').count()

@property
def success_rate(self):
    """معدل النجاح"""
    if self.child_visits_count == 0:
        return 0
    return (self.completed_child_visits_count / self.child_visits_count) * 100

@property
def is_due_for_execution(self):
    """هل المهمة مستحقة للتنفيذ"""
    if not self.is_recurring_task or not self.next_execution:
        return False
    return timezone.now() >= self.next_execution
```

---

## 🔍 **الفهارس والتحسينات**

### **فهارس الأداء:**
```sql
-- فهارس الزيارات
CREATE INDEX idx_visits_client ON visits(client_id);
CREATE INDEX idx_visits_sales_rep ON visits(sales_rep_id);
CREATE INDEX idx_visits_datetime ON visits(visit_datetime);
CREATE INDEX idx_visits_status ON visits(status);
CREATE INDEX idx_visits_task_status ON visits(task_status);

-- فهارس العملاء
CREATE INDEX idx_clients_barcode ON clients(barcode);
CREATE INDEX idx_clients_category ON clients(category_id);
CREATE INDEX idx_clients_location ON clients(latitude, longitude);

-- فهارس المستخدمين
CREATE INDEX idx_users_role ON users(role_type);
CREATE INDEX idx_users_token ON users(auth_token);
```

---

## 🔄 **إجراءات قاعدة البيانات**

### **1. إنشاء زيارات فرعية للمهام المتكررة:**
```python
def create_child_visits(self):
    """إنشاء زيارات فرعية للمهمة المتكررة"""
    if not self.is_recurring_task:
        return []
    
    target_clients = self.get_target_clients()
    created_visits = []
    
    for client in target_clients[:self.max_visits_per_execution]:
        child_visit = Visit.objects.create(
            client=client,
            sales_rep=self.sales_rep,
            task_title=self.task_title,
            task_description=self.task_description,
            is_child_visit=True,
            parent_task=self,
            task_status='assigned',
            priority=self.priority,
            visit_datetime=timezone.now() + timedelta(days=1)
        )
        created_visits.append(child_visit)
    
    # تحديث التنفيذ التالي
    self.update_next_execution()
    return created_visits
```

### **2. تحديث إحصائيات المهام:**
```python
def update_task_statistics(self):
    """تحديث إحصائيات المهمة"""
    if self.is_parent_task:
        self.total_executions += 1
        if self.success_rate >= 80:  # معدل نجاح 80% أو أكثر
            self.successful_executions += 1
        self.save()
```

---

## 📊 **استعلامات مهمة**

### **1. الحصول على المهام المستحقة:**
```python
due_tasks = Visit.objects.filter(
    is_recurring_task=True,
    next_execution__lte=timezone.now(),
    task_status__in=['assigned', 'in_progress']
)
```

### **2. إحصائيات المندوب:**
```python
rep_stats = Visit.objects.filter(sales_rep=user).aggregate(
    total_visits=Count('id'),
    verified_visits=Count('id', filter=Q(status='verified')),
    pending_visits=Count('id', filter=Q(status='pending')),
    task_visits=Count('id', filter=Q(task_type='manager_assigned'))
)
```

### **3. أفضل العملاء (الأكثر زيارة):**
```python
top_clients = Client.objects.annotate(
    visit_count=Count('visit_set')
).order_by('-visit_count')[:10]
```

---

## 🔐 **الأمان وصلاحيات البيانات**

### **مستويات الوصول:**
- **المدير العام:** وصول كامل لجميع البيانات
- **مدير المستخدمين:** وصول للمناديب التابعين فقط
- **المندوب:** وصول لبياناته الشخصية فقط

### **فلترة البيانات:**
```python
def get_user_visits(user):
    if user.is_super_manager:
        return Visit.objects.all()
    elif user.is_user_manager:
        managed_users = get_managed_users(user)
        return Visit.objects.filter(sales_rep__in=managed_users)
    else:
        return Visit.objects.filter(sales_rep=user)
```

---

## 📈 **إحصائيات وتقارير**

### **مؤشرات الأداء الرئيسية:**
- معدل نجاح الزيارات
- متوسط المسافة المقطوعة
- عدد الزيارات اليومية/الأسبوعية/الشهرية
- معدل إنجاز المهام
- توزيع الزيارات حسب التصنيفات

### **تقارير تلقائية:**
- تقرير الأداء اليومي
- تقرير المهام المتأخرة
- تقرير أفضل المناديب
- تقرير العملاء الأكثر زيارة

---

**📊 قاعدة البيانات محسنة للأداء العالي والمرونة في التطوير المستقبلي**
