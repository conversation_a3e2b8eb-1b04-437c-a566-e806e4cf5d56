{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}المستخدمين المرتبطين بالتصنيف - {{ category.name }}{% endblock %}

{% block extra_css %}
<style>
    .category-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .user-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }
    
    .user-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }
    
    .user-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(45deg, #3498db, #2980b9);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .role-badge {
        font-size: 0.8rem;
        padding: 4px 8px;
        border-radius: 15px;
    }
    
    .role-super-manager { background: linear-gradient(45deg, #e74c3c, #c0392b); }
    .role-user-manager { background: linear-gradient(45deg, #f39c12, #e67e22); }
    .role-sales-rep { background: linear-gradient(45deg, #2ecc71, #27ae60); }
    
    .stats-card {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
    }
    
    .manager-section {
        background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">المستخدمين المرتبطين بالتصنيف</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-left">
                        <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'categories:dashboard' %}">التصنيفات</a></li>
                        <li class="breadcrumb-item active">المستخدمين</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <!-- معلومات التصنيف -->
            <div class="category-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">
                            <i class="bi bi-tag me-2"></i>
                            {{ category.name }}
                        </h2>
                        {% if category.description %}
                        <p class="mb-0 opacity-75">{{ category.description }}</p>
                        {% endif %}
                        <small class="opacity-75">
                            <i class="bi bi-info-circle me-1"></i>
                            {{ category.get_category_type_display }}
                        </small>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex justify-content-end align-items-center gap-3">
                            <div class="stats-card">
                                <h3 class="mb-1">{{ users|length }}</h3>
                                <small>مستخدم مرتبط</small>
                            </div>
                            <div>
                                <button class="btn btn-light btn-sm me-2" onclick="addUsersToCategory()">
                                    <i class="fas fa-user-plus me-1"></i>
                                    إضافة مستخدمين
                                </button>
                                <a href="{% url 'categories:dashboard' %}" class="btn btn-outline-light btn-sm">
                                    <i class="fas fa-arrow-right me-1"></i>
                                    العودة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المدراء المسؤولين عن هذا التصنيف -->
            {% if managers %}
            <div class="manager-section">
                <h4 class="mb-3">
                    <i class="bi bi-people-fill me-2"></i>
                    المدراء المسؤولين عن هذا التصنيف
                </h4>
                <div class="row">
                    {% for manager in managers %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3">
                                {{ manager.first_name|first|default:manager.username|first }}
                            </div>
                            <div>
                                <h6 class="mb-1">{{ manager.get_full_name|default:manager.username }}</h6>
                                <small class="opacity-75">
                                    <i class="bi bi-shield-check me-1"></i>
                                    {{ manager.get_role_type_display }}
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ users.count }}</h3>
                            <p>إجمالي المستخدمين</p>
                        </div>
                        <div class="icon">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ users|length }}</h3>
                            <p>المستخدمين النشطين</p>
                        </div>
                        <div class="icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ managers.count }}</h3>
                            <p>المدراء المسؤولين</p>
                        </div>
                        <div class="icon">
                            <i class="bi bi-person-gear"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ category.get_category_type_display|length }}</h3>
                            <p>نوع التصنيف</p>
                        </div>
                        <div class="icon">
                            <i class="bi bi-tag"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة المستخدمين -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-people me-2"></i>
                                المستخدمين المرتبطين بهذا التصنيف
                            </h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                    <i class="bi bi-dash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            {% if users %}
                            <div class="row">
                                {% for user in users %}
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card user-card">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="user-avatar me-3">
                                                    {{ user.first_name|first|default:user.username|first }}
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1">{{ user.get_full_name|default:user.username }}</h6>
                                                    <span class="badge role-badge role-{{ user.role_type|slugify }}">
                                                        {{ user.get_role_type_display }}
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            <div class="user-info">
                                                {% if user.email %}
                                                <p class="mb-1">
                                                    <i class="bi bi-envelope me-2 text-muted"></i>
                                                    <small>{{ user.email }}</small>
                                                </p>
                                                {% endif %}
                                                
                                                {% if user.phone %}
                                                <p class="mb-1">
                                                    <i class="bi bi-phone me-2 text-muted"></i>
                                                    <small>{{ user.phone }}</small>
                                                </p>
                                                {% endif %}
                                                
                                                <p class="mb-1">
                                                    <i class="bi bi-calendar me-2 text-muted"></i>
                                                    <small>انضم في {{ user.date_joined|date:"Y-m-d" }}</small>
                                                </p>
                                                
                                                <p class="mb-0">
                                                    <i class="bi bi-activity me-2 text-muted"></i>
                                                    <small>
                                                        {% if user.is_active %}
                                                        <span class="text-success">نشط</span>
                                                        {% else %}
                                                        <span class="text-danger">غير نشط</span>
                                                        {% endif %}
                                                    </small>
                                                </p>
                                            </div>
                                            
                                            <div class="mt-3">
                                                <a href="{% url 'users:user_detail' user.id %}" class="btn btn-sm btn-outline-primary me-2">
                                                    <i class="bi bi-eye me-1"></i>
                                                    عرض
                                                </a>
                                                {% if request.user.is_super_manager %}
                                                <a href="{% url 'users:edit_user' user.id %}" class="btn btn-sm btn-outline-secondary me-2">
                                                    <i class="bi bi-pencil me-1"></i>
                                                    تعديل
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" onclick="removeUserFromCategory('{{ user.id }}', '{{ user.get_full_name|default:user.username }}')">
                                                    <i class="bi bi-person-dash me-1"></i>
                                                    إزالة
                                                </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <i class="bi bi-people display-1 text-muted"></i>
                                <h4 class="mt-3 text-muted">لا يوجد مستخدمين مرتبطين بهذا التصنيف</h4>
                                <p class="text-muted">لم يتم تعيين أي مستخدمين لهذا التصنيف بعد.</p>
                                {% if request.user.is_super_manager %}
                                <a href="{% url 'users:hierarchy_management' %}" class="btn btn-primary">
                                    <i class="bi bi-plus me-2"></i>
                                    إدارة التسلسل الهرمي
                                </a>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            {% if request.user.is_super_manager %}
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-lightning-charge me-2"></i>
                                إجراءات سريعة
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="{% url 'users:hierarchy_management' %}" class="btn btn-primary w-100">
                                        <i class="bi bi-diagram-3 me-2"></i>
                                        إدارة التسلسل الهرمي
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{% url 'categories:edit' pk=category.pk %}" class="btn btn-warning w-100">
                                        <i class="bi bi-pencil me-2"></i>
                                        تعديل التصنيف
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{% url 'users:add_user' %}" class="btn btn-success w-100">
                                        <i class="bi bi-person-plus me-2"></i>
                                        إضافة مستخدم جديد
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{% url 'categories:dashboard' %}" class="btn btn-secondary w-100">
                                        <i class="bi bi-arrow-left me-2"></i>
                                        العودة للتصنيفات
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

        </div>
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثيرات الحركة للبطاقات
    const userCards = document.querySelectorAll('.user-card');
    userCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate__animated', 'animate__fadeInUp');
    });
    
    // تحديث الإحصائيات
    console.log('تم تحميل صفحة المستخدمين للتصنيف: {{ category.name }}');
});

// إضافة مستخدمين للتصنيف
function addUsersToCategory() {
    // استخدام نفس modal من صفحة إدارة التصنيفات
    const categoryId = '{{ category.id }}';
    const categoryName = '{{ category.name }}';
    const categoryType = {{ category.category_type }};

    // فتح modal إضافة المستخدمين
    assignUsers(categoryId, categoryName, categoryType);
}

// إزالة مستخدم من التصنيف
function removeUserFromCategory(userId, userName) {
    Swal.fire({
        title: 'تأكيد الإزالة',
        text: `هل أنت متأكد من إزالة "${userName}" من هذا التصنيف؟`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، إزالة',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // إرسال طلب إزالة المستخدم
            fetch(`/categories/{{ category.id }}/remove-user/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    user_id: userId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', data.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.error, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

// دالة assignUsers (نسخة مبسطة)
function assignUsers(categoryId, categoryName, categoryType) {
    // يمكن إضافة modal هنا أو إعادة توجيه لصفحة إدارة التصنيفات
    window.location.href = `/categories/?highlight=${categoryId}`;
}
</script>
{% endblock %}
