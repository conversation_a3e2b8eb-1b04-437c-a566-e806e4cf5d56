package com.company.fieldsalestracker;

import android.content.Intent;
import android.os.Bundle;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class EnhancedTaskDetailsActivity extends AppCompatActivity {
    private static final String TAG = "EnhancedTaskDetails";
    
    private TextView titleText;
    private TextView taskIdText;
    private TextView taskTypeText;
    
    private String taskId;
    private String taskTitle;
    private boolean isParentTask;
    private boolean isRecurring;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_task_details);
        
        // الحصول على البيانات من Intent
        Intent intent = getIntent();
        taskId = intent.getStringExtra("task_id");
        taskTitle = intent.getStringExtra("task_title");
        isParentTask = intent.getBooleanExtra("is_parent_task", false);
        isRecurring = intent.getBooleanExtra("is_recurring", false);
        
        initViews();
        setupToolbar();
        displayTaskDetails();
    }
    
    private void initViews() {
        titleText = findViewById(R.id.titleText);
        taskIdText = findViewById(R.id.taskIdText);
        taskTypeText = findViewById(R.id.taskTypeText);
    }
    
    private void setupToolbar() {
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("تفاصيل المهمة");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }
    
    private void displayTaskDetails() {
        if (taskTitle != null) {
            titleText.setText(taskTitle);
        }
        
        if (taskId != null) {
            taskIdText.setText("رقم المهمة: " + taskId);
        }
        
        String taskType = "مهمة عادية";
        if (isRecurring) {
            taskType = "مهمة متكررة";
        } else if (isParentTask) {
            taskType = "مهمة متعددة";
        }
        taskTypeText.setText("النوع: " + taskType);
    }
    
    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
