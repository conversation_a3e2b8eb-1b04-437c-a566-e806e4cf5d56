{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}تعديل التصنيف - {{ category.name }}{% endblock %}

{% block extra_css %}
<link href="{% static 'css/categories.css' %}" rel="stylesheet">
<style>
.category-form {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.form-header h2 {
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: block;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-group {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    border: 2px solid #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    border: 2px solid #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    border-color: #545b62;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    border: 2px solid #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    border-color: #c82333;
    transform: translateY(-2px);
}

.category-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.category-info h5 {
    color: #495057;
    margin-bottom: 0.5rem;
}

.category-info p {
    margin: 0;
    color: #6c757d;
}

.alert {
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit me-2"></i>
                تعديل التصنيف
            </h1>
            <p class="text-muted mb-0">تعديل بيانات التصنيف: {{ category.name }}</p>
        </div>
        <div>
            <a href="{% url 'categories:dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Category Info -->
    <div class="category-info">
        <h5>معلومات التصنيف</h5>
        <div class="row">
            <div class="col-md-4">
                <strong>المعرف:</strong> {{ category.id }}
            </div>
            <div class="col-md-4">
                <strong>النوع:</strong> {{ category.get_category_type_display }}
            </div>
            <div class="col-md-4">
                <strong>الحالة:</strong> 
                {% if category.is_active %}
                    <span class="badge bg-success">نشط</span>
                {% else %}
                    <span class="badge bg-danger">غير نشط</span>
                {% endif %}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-6">
                <strong>تاريخ الإنشاء:</strong> {{ category.created_at|date:"d/m/Y H:i" }}
            </div>
            <div class="col-md-6">
                <strong>آخر تحديث:</strong> {{ category.updated_at|date:"d/m/Y H:i" }}
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="category-form">
        <div class="form-header">
            <h2>
                <i class="fas fa-edit"></i>
                تعديل بيانات التصنيف
            </h2>
        </div>

        <form method="post">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label" for="id_name">اسم التصنيف</label>
                        <input type="text" 
                               name="name" 
                               id="id_name"
                               class="form-control" 
                               value="{{ form.name.value|default:category.name }}"
                               required>
                        {% if form.name.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label" for="id_category_type">نوع التصنيف</label>
                        <select name="category_type" id="id_category_type" class="form-control" required>
                            {% for type_id, type_name in category_types %}
                                <option value="{{ type_id }}" 
                                        {% if type_id == category.category_type %}selected{% endif %}>
                                    {{ type_name }}
                                </option>
                            {% endfor %}
                        </select>
                        {% if form.category_type.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.category_type.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="id_description">الوصف</label>
                <textarea name="description" 
                          id="id_description"
                          class="form-control" 
                          rows="4"
                          placeholder="وصف التصنيف (اختياري)">{{ form.description.value|default:category.description }}</textarea>
                {% if form.description.errors %}
                    <div class="text-danger mt-1">
                        {% for error in form.description.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" 
                                   name="is_active" 
                                   id="id_is_active"
                                   class="form-check-input"
                                   {% if form.is_active.value|default:category.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="id_is_active">
                                التصنيف نشط
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ التغييرات
                </button>
                
                <a href="{% url 'categories:dashboard' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    إلغاء
                </a>
                
                <a href="{% url 'categories:delete' category.id %}" 
                   class="btn btn-danger"
                   onclick="return confirm('هل أنت متأكد من حذف هذا التصنيف؟')">
                    <i class="fas fa-trash"></i>
                    حذف التصنيف
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    });
    
    // التحقق من صحة البيانات
    const nameInput = document.getElementById('id_name');
    nameInput.addEventListener('input', function() {
        if (this.value.trim().length < 2) {
            this.setCustomValidity('يجب أن يكون اسم التصنيف أكثر من حرفين');
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>
{% endblock %}
