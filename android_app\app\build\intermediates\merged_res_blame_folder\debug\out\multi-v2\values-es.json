{"logs": [{"outputFile": "com.company.fieldsalestracker.app-mergeDebugResources-53:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43bdbc0b1ff399fa79cb359f37c50201\\transformed\\material-1.9.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1094,1189,1270,1333,1422,1486,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2325,2414,2496,2637,2718,2798,2888,2944,3000,3066,3145,3227,3315,3389,3466,3536,3615,3699,3783,3875,3975,4049,4130,4232,4285,4352,4445,4534,4596,4660,4723,4836,4929,5033,5127,5187,5247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,89,55,55,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,59,59,82", "endOffsets": "273,354,433,520,621,717,821,943,1024,1089,1184,1265,1328,1417,1481,1550,1613,1687,1751,1807,1925,1983,2045,2101,2181,2320,2409,2491,2632,2713,2793,2883,2939,2995,3061,3140,3222,3310,3384,3461,3531,3610,3694,3778,3870,3970,4044,4125,4227,4280,4347,4440,4529,4591,4655,4718,4831,4924,5028,5122,5182,5242,5325"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,3407,3503,3607,3729,6147,6212,6307,6388,6451,6540,6604,6673,6736,6810,6874,6930,7048,7106,7168,7224,7304,7443,7532,7614,7755,7836,7916,8006,8062,8118,8184,8263,8345,8433,8507,8584,8654,8733,8817,8901,8993,9093,9167,9248,9350,9403,9470,9563,9652,9714,9778,9841,9954,10047,10151,10245,10305,10604", "endLines": "5,33,34,35,36,37,38,39,40,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,89,55,55,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,59,59,82", "endOffsets": "323,3135,3214,3301,3402,3498,3602,3724,3805,6207,6302,6383,6446,6535,6599,6668,6731,6805,6869,6925,7043,7101,7163,7219,7299,7438,7527,7609,7750,7831,7911,8001,8057,8113,8179,8258,8340,8428,8502,8579,8649,8728,8812,8896,8988,9088,9162,9243,9345,9398,9465,9558,9647,9709,9773,9836,9949,10042,10146,10240,10300,10360,10682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ce97e1d7afd464f10c84d8596a83f4ee\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,10687", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,10765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c88fe9f048cf6eb09539000df0730205\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4846", "endColumns": "159", "endOffsets": "5001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed709288d23a486d0424a1f6be3fc698\\transformed\\navigation-ui-2.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,174", "endColumns": "118,119", "endOffsets": "169,289"}, "to": {"startLines": "112,113", "startColumns": "4,4", "startOffsets": "10365,10484", "endColumns": "118,119", "endOffsets": "10479,10599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\043eee66ca32b7ab1e056bec5c84992b\\transformed\\core-1.9.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "116", "startColumns": "4", "startOffsets": "10770", "endColumns": "100", "endOffsets": "10866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb7e328e390e7f202e781ab04bb4484f\\transformed\\jetified-play-services-base-18.1.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3810,3918,4081,4212,4320,4481,4614,4736,5006,5198,5307,5472,5604,5769,5926,5993,6062", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "3913,4076,4207,4315,4476,4609,4731,4841,5193,5302,5467,5599,5764,5921,5988,6057,6142"}}]}]}