from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.views.decorators.http import require_http_methods
from .models import Category
from users.forms import CategoryForm
from users.models import User
import json


def is_super_manager(user):
    """التحقق من أن المستخدم مدير عام"""
    return user.is_authenticated and user.is_super_manager


@login_required
def categories_dashboard(request):
    """صفحة إدارة التصنيفات الموحدة"""
    if not request.user.is_super_manager and not request.user.has_permission('can_manage_categories'):
        messages.error(request, 'ليس لديك صلاحية للوصول لهذه الصفحة')
        return redirect('dashboard:home')

    # الفلاتر
    category_type = request.GET.get('type', '')
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')

    # جلب التصنيفات
    categories = Category.objects.all()

    if category_type:
        categories = categories.filter(category_type=category_type)

    if search_query:
        categories = categories.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    if status_filter:
        categories = categories.filter(is_active=(status_filter == 'active'))

    categories = categories.order_by('category_type', 'name')

    # إضافة عدد المستخدمين لكل تصنيف
    from users.models import User
    for category in categories:
        users_count = 0
        all_users = User.objects.filter(is_active=True)
        for user in all_users:
            if user.categories and str(category.id) in user.categories:
                users_count += 1
        category.users_count = users_count

    # إحصائيات
    stats = {
        'total': Category.objects.count(),
        'active': Category.objects.filter(is_active=True).count(),
        'inactive': Category.objects.filter(is_active=False).count(),
        'type_1': Category.objects.filter(category_type=1).count(),
        'type_2': Category.objects.filter(category_type=2).count(),
        'type_3': Category.objects.filter(category_type=3).count(),
    }

    # الترقيم
    paginator = Paginator(categories, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'categories': page_obj,
        'stats': stats,
        'category_types': Category.CATEGORY_TYPES,
        'search_query': search_query,
        'category_type': category_type,
        'status_filter': status_filter,
        'title': 'إدارة التصنيفات',
    }

    return render(request, 'categories/management.html', context)


@user_passes_test(is_super_manager)
def edit_category(request, pk):
    """تعديل تصنيف"""
    category = get_object_or_404(Category, pk=pk)

    if request.method == 'POST':
        try:
            category.name = request.POST.get('name', '').strip()
            category.description = request.POST.get('description', '').strip()
            category.category_type = int(request.POST.get('category_type', 1))
            category.is_active = request.POST.get('is_active') == 'on'
            category.save()

            return JsonResponse({
                'success': True,
                'message': f'تم تحديث التصنيف "{category.name}" بنجاح'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'حدث خطأ: {str(e)}'
            })

    return JsonResponse({
        'success': False,
        'error': 'طريقة غير مدعومة'
    })


@user_passes_test(is_super_manager)
def delete_category(request, pk):
    """حذف تصنيف"""
    category = get_object_or_404(Category, pk=pk)

    if request.method == 'POST':
        try:
            # التحقق من وجود مستخدمين مرتبطين (إصلاح طريقة البحث)
            users_count = 0
            from users.models import User

            # البحث في JSONField عن معرف التصنيف
            users_with_category = User.objects.filter(is_active=True)
            for user in users_with_category:
                if user.categories and str(category.id) in user.categories:
                    users_count += 1

            if users_count > 0:
                return JsonResponse({
                    'success': False,
                    'error': f'لا يمكن حذف هذا التصنيف لأنه مرتبط بـ {users_count} مستخدم'
                })

            category_name = category.name
            category.delete()

            return JsonResponse({
                'success': True,
                'message': f'تم حذف التصنيف "{category_name}" بنجاح'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'حدث خطأ: {str(e)}'
            })

    return JsonResponse({
        'success': False,
        'error': 'طريقة غير مدعومة'
    })


@user_passes_test(is_super_manager)
def get_category_details(request, pk):
    """الحصول على تفاصيل التصنيف"""
    category = get_object_or_404(Category, pk=pk)

    return JsonResponse({
        'success': True,
        'category': {
            'id': category.id,
            'name': category.name,
            'description': category.description,
            'category_type': category.category_type,
            'is_active': category.is_active,
            'created_at': category.created_at.isoformat(),
        }
    })


@user_passes_test(is_super_manager)
def assign_users_to_category(request, pk):
    """إضافة مستخدمين لتصنيف"""
    category = get_object_or_404(Category, pk=pk)

    if request.method == 'POST':
        try:
            user_ids = request.POST.getlist('user_ids')

            from users.models import User

            # تحديد نوع المستخدمين حسب نوع التصنيف
            if category.category_type == 1:  # تصنيفات المدير العام
                users = User.objects.filter(id__in=user_ids, role_type=1)
            elif category.category_type == 2:  # تصنيفات المستخدمين
                users = User.objects.filter(id__in=user_ids, role_type__in=[1, 2])
            elif category.category_type == 3:  # تصنيفات المناديب
                users = User.objects.filter(id__in=user_ids, role_type=3)
            else:
                return JsonResponse({
                    'success': False,
                    'error': 'نوع التصنيف غير صحيح'
                })

            assigned_count = 0
            for user in users:
                # إضافة التصنيف للمستخدم (إصلاح طريقة الحفظ)
                current_categories = user.categories if user.categories else []
                if str(category.id) not in current_categories:
                    current_categories.append(str(category.id))
                    user.categories = current_categories
                    user.save()
                    assigned_count += 1

            return JsonResponse({
                'success': True,
                'message': f'تم إضافة {assigned_count} مستخدم للتصنيف "{category.name}"'
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'حدث خطأ: {str(e)}'
            })

    # إرجاع قائمة المستخدمين المتاحين
    from users.models import User

    if category.category_type == 1:  # تصنيفات المدير العام
        available_users = User.objects.filter(role_type=1, is_active=True)
    elif category.category_type == 2:  # تصنيفات المستخدمين
        available_users = User.objects.filter(role_type__in=[1, 2], is_active=True)
    elif category.category_type == 3:  # تصنيفات المناديب
        available_users = User.objects.filter(role_type=3, is_active=True)
    else:
        available_users = User.objects.none()

    users_data = []
    for user in available_users:
        # التحقق من وجود التصنيف (إصلاح طريقة البحث)
        current_categories = user.categories if user.categories else []
        is_assigned = str(category.id) in current_categories

        users_data.append({
            'id': user.id,
            'name': user.get_full_name(),
            'username': user.username,
            'role_type': user.role_type,
            'is_assigned': is_assigned
        })

    return JsonResponse({
        'success': True,
        'users': users_data,
        'category': {
            'id': category.id,
            'name': category.name,
            'type': category.category_type
        }
    })


@login_required
def categories_list(request):
    """صفحة جميع التصنيفات الموحدة"""
    # الحصول على نوع التصنيف من query parameter
    category_type = request.GET.get('type')

    context = {
        'initial_filter_type': category_type if category_type else '',
    }

    return render(request, 'categories/list.html', context)


@login_required
def categories_api_list(request):
    """API لجلب التصنيفات مع الفلترة للـ DataTable"""
    try:
        # معاملات DataTable
        draw = int(request.GET.get('draw', 1))
        start = int(request.GET.get('start', 0))
        length = int(request.GET.get('length', 10))
        search_value = request.GET.get('search[value]', '')

        # فلاتر إضافية
        category_type = request.GET.get('category_type', '')
        category_types = request.GET.get('category_types', '')
        status = request.GET.get('status', '')
        search_term = request.GET.get('search_term', '')
        active_only = request.GET.get('active_only', False)

        # الحصول على التصنيفات
        categories = Category.objects.all()

        # تطبيق الفلاتر
        if category_type:
            categories = categories.filter(category_type=category_type)
        elif category_types:
            # دعم أنواع متعددة مفصولة بفاصلة
            types_list = [int(t.strip()) for t in category_types.split(',') if t.strip().isdigit()]
            if types_list:
                categories = categories.filter(category_type__in=types_list)

        if active_only:
            categories = categories.filter(is_active=True)

        if status == 'active':
            categories = categories.filter(is_active=True)
        elif status == 'inactive':
            categories = categories.filter(is_active=False)

        # البحث
        if search_value or search_term:
            search_query = search_value or search_term
            categories = categories.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )

        # العدد الإجمالي
        total_records = Category.objects.count()
        filtered_records = categories.count()

        # الترتيب والتقسيم
        categories = categories.order_by('-created_at')[start:start + length]

        # تحضير البيانات
        data = []
        for category in categories:
            # حساب عدد المستخدمين المرتبطين
            # استخدام icontains بدلاً من contains للتوافق مع SQLite
            users_count = User.objects.filter(
                categories__icontains=str(category.id)
            ).count()

            data.append({
                'id': str(category.id),
                'name': category.name,
                'category_type': category.category_type,
                'description': category.description or '',
                'is_active': category.is_active,
                'users_count': users_count,
                'created_at': category.created_at.isoformat(),
            })

        return JsonResponse({
            'draw': draw,
            'recordsTotal': total_records,
            'recordsFiltered': filtered_records,
            'data': data
        })

    except Exception as e:
        return JsonResponse({
            'draw': draw,
            'recordsTotal': 0,
            'recordsFiltered': 0,
            'data': [],
            'error': f'حدث خطأ في تحميل البيانات: {str(e)}'
        })


@login_required
def categories_stats(request):
    """API لإحصائيات التصنيفات"""
    try:
        total = Category.objects.count()
        type_1 = Category.objects.filter(category_type=1).count()
        type_2 = Category.objects.filter(category_type=2).count()
        type_3 = Category.objects.filter(category_type=3).count()

        return JsonResponse({
            'total': total,
            'type_1': type_1,
            'type_2': type_2,
            'type_3': type_3,
        })
    except Exception as e:
        return JsonResponse({'error': str(e)})


@login_required
def category_create_ajax(request):
    """إنشاء تصنيف جديد عبر AJAX"""
    if request.method == 'POST':
        try:
            name = request.POST.get('name', '').strip()
            category_type = request.POST.get('category_type')
            description = request.POST.get('description', '').strip()
            is_active = request.POST.get('is_active') == 'on'

            if not name:
                return JsonResponse({
                    'success': False,
                    'message': 'اسم التصنيف مطلوب'
                })

            if not category_type:
                return JsonResponse({
                    'success': False,
                    'message': 'نوع التصنيف مطلوب'
                })

            # التحقق من عدم وجود تصنيف بنفس الاسم والنوع
            if Category.objects.filter(name=name, category_type=category_type).exists():
                return JsonResponse({
                    'success': False,
                    'message': 'يوجد تصنيف بنفس الاسم والنوع'
                })

            # إنشاء التصنيف
            category = Category.objects.create(
                name=name,
                category_type=int(category_type),
                description=description,
                is_active=is_active,
                created_by=request.user
            )

            return JsonResponse({
                'success': True,
                'message': 'تم إنشاء التصنيف بنجاح',
                'category_id': str(category.id)
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'حدث خطأ: {str(e)}'
            })

    return JsonResponse({
        'success': False,
        'message': 'طريقة غير مدعومة'
    })


@login_required
def categories_export(request):
    """تصدير التصنيفات إلى Excel"""
    try:
        import openpyxl
        from django.http import HttpResponse
        from openpyxl.styles import Font, PatternFill, Alignment

        # إنشاء workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "التصنيفات"

        # العناوين
        headers = ['الاسم', 'النوع', 'الوصف', 'الحالة', 'عدد المستخدمين', 'تاريخ الإنشاء']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")

        # البيانات
        categories = Category.objects.all().order_by('-created_at')
        type_names = {1: 'تصنيفات المستخدمين', 2: 'تصنيفات المناديب', 3: 'تصنيفات العملاء'}

        for row, category in enumerate(categories, 2):
            users_count = User.objects.filter(
                categories__icontains=str(category.id)
            ).count()

            ws.cell(row=row, column=1, value=category.name)
            ws.cell(row=row, column=2, value=type_names.get(category.category_type, ''))
            ws.cell(row=row, column=3, value=category.description or '')
            ws.cell(row=row, column=4, value='نشط' if category.is_active else 'غير نشط')
            ws.cell(row=row, column=5, value=users_count)
            ws.cell(row=row, column=6, value=category.created_at.strftime('%Y-%m-%d'))

        # تنسيق الأعمدة
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # إعداد الاستجابة
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="categories.xlsx"'

        wb.save(response)
        return response

    except ImportError:
        return JsonResponse({
            'error': 'مكتبة openpyxl غير مثبتة'
        })
    except Exception as e:
        return JsonResponse({
            'error': f'حدث خطأ في التصدير: {str(e)}'
        })


@login_required
def category_list(request, category_type=None):
    """قائمة التصنيفات حسب النوع"""
    if not request.user.is_super_manager and not request.user.has_permission('can_manage_categories'):
        messages.error(request, 'ليس لديك صلاحية للوصول لهذه الصفحة')
        return redirect('dashboard:home')

    # تحديد نوع التصنيف
    if category_type is not None:
        category_type = int(category_type)
        categories = Category.objects.filter(category_type=category_type)
        type_name = dict(Category.CATEGORY_TYPES).get(category_type, 'غير محدد')
    else:
        categories = Category.objects.all()
        type_name = 'جميع التصنيفات'

    # البحث
    search_query = request.GET.get('search', '')
    if search_query:
        categories = categories.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(code__icontains=search_query)
        )

    # الترتيب
    categories = categories.order_by('category_type', 'sort_order', 'name')

    # إرسال جميع التصنيفات للـ DataTable
    context = {
        'categories': categories,
        'category_type': category_type,
        'type_name': type_name,
        'search_query': search_query,
        'category_types': Category.CATEGORY_TYPES,
    }

    return render(request, 'categories/list.html', context)


@login_required
def category_create(request, category_type=None):
    """إنشاء تصنيف جديد"""
    if not request.user.is_super_manager and not request.user.has_permission('can_manage_categories'):
        messages.error(request, 'ليس لديك صلاحية للوصول لهذه الصفحة')
        return redirect('dashboard:home')

    if request.method == 'POST':
        form = CategoryForm(request.POST)
        if form.is_valid():
            category = form.save(commit=False)
            category.created_by = request.user
            category.save()

            messages.success(request, f'تم إنشاء التصنيف "{category.name}" بنجاح')
            return redirect('categories:list', category_type=category.category_type)
    else:
        form = CategoryForm()

        # تحديد نوع التصنيف إذا تم تمريره
        if category_type is not None:
            form.fields['category_type'].initial = int(category_type)

    # تصفية التصنيفات الأب حسب النوع المحدد
    if category_type is not None:
        form.fields['parent_category'].queryset = Category.objects.filter(
            category_type=int(category_type),
            is_active=True
        )

    context = {
        'form': form,
        'category_type': category_type,
        'type_name': dict(Category.CATEGORY_TYPES).get(int(category_type), 'غير محدد') if category_type else None,
    }

    return render(request, 'categories/create.html', context)


@login_required
def category_edit(request, pk):
    """تعديل تصنيف"""
    if not request.user.is_super_manager and not request.user.has_permission('can_manage_categories'):
        messages.error(request, 'ليس لديك صلاحية للوصول لهذه الصفحة')
        return redirect('dashboard:home')

    category = get_object_or_404(Category, pk=pk)

    # منع تعديل تصنيفات النظام للمستخدمين غير المدراء العامين
    if category.is_system and not request.user.is_super_manager:
        messages.error(request, 'لا يمكن تعديل تصنيفات النظام')
        return redirect('categories:list', category_type=category.category_type)

    if request.method == 'POST':
        form = CategoryForm(request.POST, instance=category)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث التصنيف "{category.name}" بنجاح')
            return redirect('categories:list', category_type=category.category_type)
    else:
        form = CategoryForm(instance=category)

    # تصفية التصنيفات الأب حسب النوع (عدا التصنيف الحالي)
    form.fields['parent_category'].queryset = Category.objects.filter(
        category_type=category.category_type,
        is_active=True
    ).exclude(pk=category.pk)

    context = {
        'form': form,
        'category': category,
        'category_types': Category.CATEGORY_TYPES,
        'title': f'تعديل التصنيف - {category.name}',
    }

    return render(request, 'categories/edit.html', context)


@login_required
def category_delete(request, pk):
    """حذف تصنيف"""
    if not request.user.is_super_manager and not request.user.has_permission('can_manage_categories'):
        return JsonResponse({'success': False, 'error': 'ليس لديك صلاحية'})

    category = get_object_or_404(Category, pk=pk)

    # منع حذف تصنيفات النظام
    if category.is_system:
        return JsonResponse({'success': False, 'error': 'لا يمكن حذف تصنيفات النظام'})

    # التحقق من وجود تصنيفات فرعية
    if category.subcategories.exists():
        return JsonResponse({
            'success': False,
            'error': 'لا يمكن حذف التصنيف لوجود تصنيفات فرعية'
        })

    category_name = category.name
    category_type = category.category_type
    category.delete()

    return JsonResponse({
        'success': True,
        'message': f'تم حذف التصنيف "{category_name}" بنجاح',
        'redirect_url': f'/categories/{category_type}/'
    })


@login_required
def category_tree_api(request, category_type):
    """API لجلب شجرة التصنيفات"""
    if not request.user.is_super_manager and not request.user.has_permission('can_manage_categories'):
        return JsonResponse({'success': False, 'error': 'ليس لديك صلاحية'})

    def build_tree(parent=None):
        categories = Category.objects.filter(
            category_type=int(category_type),
            parent_category=parent,
            is_active=True
        ).order_by('sort_order', 'name')

        tree = []
        for category in categories:
            node = {
                'id': str(category.id),
                'name': category.name,
                'code': category.code,
                'color': category.color,
                'icon': category.icon,
                'level': category.get_level(),
                'children': build_tree(category)
            }
            tree.append(node)

        return tree

    tree = build_tree()

    return JsonResponse({
        'success': True,
        'tree': tree,
        'type_name': dict(Category.CATEGORY_TYPES).get(int(category_type), 'غير محدد')
    })


@login_required
@require_http_methods(["POST"])
def category_reorder(request):
    """إعادة ترتيب التصنيفات"""
    if not request.user.is_super_manager and not request.user.has_permission('can_manage_categories'):
        return JsonResponse({'success': False, 'error': 'ليس لديك صلاحية'})

    try:
        data = json.loads(request.body)
        category_orders = data.get('orders', [])

        for item in category_orders:
            category_id = item.get('id')
            sort_order = item.get('order')

            Category.objects.filter(id=category_id).update(sort_order=sort_order)

        return JsonResponse({'success': True, 'message': 'تم تحديث الترتيب بنجاح'})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def category_users(request, pk):
    """عرض المستخدمين المرتبطين بالتصنيف"""
    if not request.user.is_super_manager and not request.user.has_permission('can_manage_categories'):
        messages.error(request, 'ليس لديك صلاحية للوصول لهذه الصفحة')
        return redirect('dashboard:home')

    category = get_object_or_404(Category, pk=pk)

    # جلب المستخدمين المرتبطين بهذا التصنيف
    from users.models import User
    all_users = User.objects.filter(is_active=True)
    users = []

    for user in all_users:
        if user.categories and str(category.id) in user.categories:
            users.append(user)

    # ترتيب المستخدمين
    users.sort(key=lambda u: (u.first_name or '', u.last_name or ''))

    # جلب المدراء الذين يديرون هذا التصنيف
    all_managers = User.objects.filter(is_active=True)
    managers = []

    for manager in all_managers:
        if hasattr(manager, 'managed_categories') and manager.managed_categories and str(category.id) in manager.managed_categories:
            managers.append(manager)

    # ترتيب المدراء
    managers.sort(key=lambda m: (m.first_name or '', m.last_name or ''))

    context = {
        'category': category,
        'users': users,
        'managers': managers,
    }

    return render(request, 'categories/users.html', context)


@login_required
def remove_user_from_category(request, pk):
    """إزالة مستخدم من التصنيف"""
    if not request.user.is_super_manager and not request.user.has_permission('can_manage_categories'):
        return JsonResponse({'success': False, 'error': 'ليس لديك صلاحية'})

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'طريقة غير مسموحة'})

    try:
        import json
        data = json.loads(request.body)
        user_id = data.get('user_id')

        category = get_object_or_404(Category, pk=pk)

        from users.models import User
        user = get_object_or_404(User, pk=user_id)

        # إزالة التصنيف من المستخدم
        if user.categories and str(category.id) in user.categories:
            user.categories.remove(str(category.id))
            user.save()

            return JsonResponse({
                'success': True,
                'message': f'تم إزالة "{user.get_full_name()}" من التصنيف "{category.name}" بنجاح'
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'المستخدم غير مرتبط بهذا التصنيف'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'حدث خطأ: {str(e)}'
        })


@login_required
def categories_statistics(request):
    """إحصائيات التصنيفات"""
    if not request.user.is_super_manager:
        messages.error(request, 'ليس لديك صلاحية للوصول لهذه الصفحة')
        return redirect('dashboard:home')

    from users.models import User

    # إحصائيات عامة
    total_categories = Category.objects.filter(is_active=True).count()
    super_categories = Category.objects.filter(category_type=1, is_active=True).count()
    user_categories = Category.objects.filter(category_type=2, is_active=True).count()
    sales_categories = Category.objects.filter(category_type=3, is_active=True).count()
    client_categories = Category.objects.filter(category_type=4, is_active=True).count()

    # إحصائيات المستخدمين في التصنيفات
    total_users = User.objects.filter(is_active=True).count()
    users_with_categories = 0
    users_without_categories = 0

    for user in User.objects.filter(is_active=True):
        if user.categories:
            users_with_categories += 1
        else:
            users_without_categories += 1

    # أكثر التصنيفات استخداماً
    categories_usage = []
    for category in Category.objects.filter(is_active=True):
        users_count = 0
        for user in User.objects.filter(is_active=True):
            if user.categories and str(category.id) in user.categories:
                users_count += 1

        categories_usage.append({
            'category': category,
            'users_count': users_count,
            'percentage': (users_count / total_users * 100) if total_users > 0 else 0,
        })

    # ترتيب حسب الاستخدام
    categories_usage.sort(key=lambda x: x['users_count'], reverse=True)
    top_categories = categories_usage[:10]

    # إحصائيات حسب النوع
    type_stats = []
    for category_type, type_name in Category.CATEGORY_TYPES:
        categories_count = Category.objects.filter(category_type=category_type, is_active=True).count()

        # حساب عدد المستخدمين في هذا النوع
        users_in_type = 0
        for category in Category.objects.filter(category_type=category_type, is_active=True):
            for user in User.objects.filter(is_active=True):
                if user.categories and str(category.id) in user.categories:
                    users_in_type += 1
                    break  # لا نحسب المستخدم أكثر من مرة

        type_stats.append({
            'type': category_type,
            'type_name': type_name,
            'categories_count': categories_count,
            'users_count': users_in_type,
            'avg_users_per_category': (users_in_type / categories_count) if categories_count > 0 else 0,
        })

    # التصنيفات الفارغة
    empty_categories = []
    for category in Category.objects.filter(is_active=True):
        users_count = 0
        for user in User.objects.filter(is_active=True):
            if user.categories and str(category.id) in user.categories:
                users_count += 1

        if users_count == 0:
            empty_categories.append(category)

    context = {
        'title': 'إحصائيات التصنيفات',
        'total_categories': total_categories,
        'super_categories': super_categories,
        'user_categories': user_categories,
        'sales_categories': sales_categories,
        'client_categories': client_categories,
        'total_users': total_users,
        'users_with_categories': users_with_categories,
        'users_without_categories': users_without_categories,
        'top_categories': top_categories,
        'type_stats': type_stats,
        'empty_categories': empty_categories,
    }

    return render(request, 'categories/statistics.html', context)
