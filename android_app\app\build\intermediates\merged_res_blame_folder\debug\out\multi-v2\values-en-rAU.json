{"logs": [{"outputFile": "com.company.fieldsalestracker.app-mergeDebugResources-53:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed709288d23a486d0424a1f6be3fc698\\transformed\\navigation-ui-2.6.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,116", "endOffsets": "156,273"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2762,2868", "endColumns": "105,116", "endOffsets": "2863,2980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ce97e1d7afd464f10c84d8596a83f4ee\\transformed\\appcompat-1.6.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2985", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,3063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\043eee66ca32b7ab1e056bec5c84992b\\transformed\\core-1.9.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "32", "startColumns": "4", "startOffsets": "3068", "endColumns": "100", "endOffsets": "3164"}}]}]}