// Generated by view binder compiler. Do not edit!
package com.company.fieldsalestracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.company.fieldsalestracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemMultiTaskBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView cardView;

  @NonNull
  public final Button detailsButton;

  @NonNull
  public final Button executeButton;

  @NonNull
  public final TextView executionsText;

  @NonNull
  public final TextView nextExecutionText;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final TextView salesRepText;

  @NonNull
  public final ImageView statusIcon;

  @NonNull
  public final TextView statusText;

  @NonNull
  public final ImageView taskTypeIcon;

  @NonNull
  public final TextView taskTypeText;

  @NonNull
  public final TextView titleText;

  private ItemMultiTaskBinding(@NonNull CardView rootView, @NonNull CardView cardView,
      @NonNull Button detailsButton, @NonNull Button executeButton,
      @NonNull TextView executionsText, @NonNull TextView nextExecutionText,
      @NonNull ProgressBar progressBar, @NonNull TextView progressText,
      @NonNull TextView salesRepText, @NonNull ImageView statusIcon, @NonNull TextView statusText,
      @NonNull ImageView taskTypeIcon, @NonNull TextView taskTypeText,
      @NonNull TextView titleText) {
    this.rootView = rootView;
    this.cardView = cardView;
    this.detailsButton = detailsButton;
    this.executeButton = executeButton;
    this.executionsText = executionsText;
    this.nextExecutionText = nextExecutionText;
    this.progressBar = progressBar;
    this.progressText = progressText;
    this.salesRepText = salesRepText;
    this.statusIcon = statusIcon;
    this.statusText = statusText;
    this.taskTypeIcon = taskTypeIcon;
    this.taskTypeText = taskTypeText;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemMultiTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemMultiTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_multi_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemMultiTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      CardView cardView = (CardView) rootView;

      id = R.id.detailsButton;
      Button detailsButton = ViewBindings.findChildViewById(rootView, id);
      if (detailsButton == null) {
        break missingId;
      }

      id = R.id.executeButton;
      Button executeButton = ViewBindings.findChildViewById(rootView, id);
      if (executeButton == null) {
        break missingId;
      }

      id = R.id.executionsText;
      TextView executionsText = ViewBindings.findChildViewById(rootView, id);
      if (executionsText == null) {
        break missingId;
      }

      id = R.id.nextExecutionText;
      TextView nextExecutionText = ViewBindings.findChildViewById(rootView, id);
      if (nextExecutionText == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progressText;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.salesRepText;
      TextView salesRepText = ViewBindings.findChildViewById(rootView, id);
      if (salesRepText == null) {
        break missingId;
      }

      id = R.id.statusIcon;
      ImageView statusIcon = ViewBindings.findChildViewById(rootView, id);
      if (statusIcon == null) {
        break missingId;
      }

      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      id = R.id.taskTypeIcon;
      ImageView taskTypeIcon = ViewBindings.findChildViewById(rootView, id);
      if (taskTypeIcon == null) {
        break missingId;
      }

      id = R.id.taskTypeText;
      TextView taskTypeText = ViewBindings.findChildViewById(rootView, id);
      if (taskTypeText == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new ItemMultiTaskBinding((CardView) rootView, cardView, detailsButton, executeButton,
          executionsText, nextExecutionText, progressBar, progressText, salesRepText, statusIcon,
          statusText, taskTypeIcon, taskTypeText, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
