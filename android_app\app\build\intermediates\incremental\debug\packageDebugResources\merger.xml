<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\django_project\TrackCustomer\android_app\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\django_project\TrackCustomer\android_app\app\src\main\res"><file name="button_scale_down" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\anim\button_scale_down.xml" qualifiers="" type="anim"/><file name="button_scale_up" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\anim\button_scale_up.xml" qualifiers="" type="anim"/><file name="btn_camera_background" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\btn_camera_background.xml" qualifiers="" type="drawable"/><file name="btn_submit_background" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\btn_submit_background.xml" qualifiers="" type="drawable"/><file name="circle_background" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_arrow_forward" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_arrow_forward.xml" qualifiers="" type="drawable"/><file name="ic_business" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_business.xml" qualifiers="" type="drawable"/><file name="ic_camera" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_camera.xml" qualifiers="" type="drawable"/><file name="ic_camera_enhanced" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_camera_enhanced.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_launcher.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher_simple" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_launcher_simple.xml" qualifiers="" type="drawable"/><file name="ic_list" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_list.xml" qualifiers="" type="drawable"/><file name="ic_location" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_location.xml" qualifiers="" type="drawable"/><file name="ic_lock" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_lock.xml" qualifiers="" type="drawable"/><file name="ic_logout" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_logout.xml" qualifiers="" type="drawable"/><file name="ic_person" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_qr_code_scanner" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_qr_code_scanner.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_send" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_send.xml" qualifiers="" type="drawable"/><file name="ic_send_enhanced" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_send_enhanced.xml" qualifiers="" type="drawable"/><file name="login_background" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\login_background.xml" qualifiers="" type="drawable"/><file name="scanner_overlay" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\scanner_overlay.xml" qualifiers="" type="drawable"/><file name="splash_background" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\splash_background.xml" qualifiers="" type="drawable"/><file name="status_background" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\status_background.xml" qualifiers="" type="drawable"/><file name="activity_barcode_scanner" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\activity_barcode_scanner.xml" qualifiers="" type="layout"/><file name="activity_login" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_my_visits" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\activity_my_visits.xml" qualifiers="" type="layout"/><file name="activity_splash" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="activity_visit_details" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\activity_visit_details.xml" qualifiers="" type="layout"/><file name="item_client" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\item_client.xml" qualifiers="" type="layout"/><file name="item_visit" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\item_visit.xml" qualifiers="" type="layout"/><file name="main_menu" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\menu\main_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-hdpi\ic_launcher.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-hdpi\ic_launcher_round.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-mdpi\ic_launcher.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-mdpi\ic_launcher_round.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-xhdpi\ic_launcher.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-xhdpi\ic_launcher_round.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-xxhdpi\ic_launcher.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-xxxhdpi\ic_launcher.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\django_project\TrackCustomer\android_app\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary_color">#2563EB</color><color name="primary_dark">#1D4ED8</color><color name="primary_light">#3B82F6</color><color name="secondary_color">#64748B</color><color name="secondary_dark">#475569</color><color name="secondary_light">#94A3B8</color><color name="success_color">#059669</color><color name="warning_color">#D97706</color><color name="error_color">#DC2626</color><color name="info_color">#0EA5E9</color><color name="success_background">#ECFDF5</color><color name="warning_background">#FFFBEB</color><color name="error_background">#FEF2F2</color><color name="info_background">#F0F9FF</color><color name="background_color">#F8FAFC</color><color name="surface_color">#FFFFFF</color><color name="card_background">#FFFFFF</color><color name="text_primary">#1E293B</color><color name="text_secondary">#64748B</color><color name="text_hint">#94A3B8</color><color name="text_white">#FFFFFF</color><color name="border_color">#E2E8F0</color><color name="divider_color">#F1F5F9</color><color name="overlay_dark">#80000000</color><color name="overlay_light">#40FFFFFF</color><color name="btn_camera_start">#2196F3</color><color name="btn_camera_end">#1976D2</color><color name="btn_camera_pressed_start">#1976D2</color><color name="btn_camera_pressed_end">#0D47A1</color><color name="btn_submit_start">#4CAF50</color><color name="btn_submit_end">#388E3C</color><color name="btn_submit_pressed_start">#388E3C</color><color name="btn_submit_pressed_end">#1B5E20</color><color name="btn_camera_border">#E3F2FD</color><color name="btn_submit_border">#C8E6C9</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="colorPrimaryDark">#1976D2</color><color name="colorPrimary">#2196F3</color><color name="colorAccent">#FF4081</color></file><file path="D:\django_project\TrackCustomer\android_app\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Field Sales Tracker</string><string name="login_title">تسجيل الدخول</string><string name="username">اسم المستخدم</string><string name="password">كلمة المرور</string><string name="login_button">دخول</string><string name="login_error">خطأ في تسجيل الدخول</string><string name="clients_title">العملاء</string><string name="scan_barcode">مسح الباركود</string><string name="my_visits">زياراتي</string><string name="logout">تسجيل الخروج</string><string name="refresh">تحديث</string><string name="client_distance">المسافة: %1$s متر</string><string name="client_address">العنوان: %1$s</string><string name="client_phone">الهاتف: %1$s</string><string name="scanner_title">مسح الباركود</string><string name="scanner_instruction">وجه الكاميرا نحو الباركود</string><string name="barcode_detected">تم اكتشاف الباركود: %1$s</string><string name="invalid_barcode">باركود غير صحيح</string><string name="visit_title">تفاصيل الزيارة</string><string name="client_name">اسم العميل</string><string name="take_photo">التقاط صورة</string><string name="visit_notes">ملاحظات الزيارة</string><string name="submit_visit">إرسال الزيارة</string><string name="visit_success">تم تسجيل الزيارة بنجاح</string><string name="camera_permission_required">صلاحية الكاميرا مطلوبة</string><string name="location_permission_required">صلاحية الموقع مطلوبة</string><string name="permissions_required">الصلاحيات مطلوبة لعمل التطبيق</string><string name="network_error">خطأ في الشبكة</string><string name="server_error">خطأ في الخادم</string><string name="unknown_error">خطأ غير معروف</string><string name="client_not_found">العميل غير موجود</string><string name="location_too_far">الموقع بعيد جداً عن العميل</string><string name="ok">موافق</string><string name="cancel">إلغاء</string><string name="retry">إعادة المحاولة</string><string name="loading">جاري التحميل...</string><string name="photo_taken">تم التقاط الصورة</string><string name="visit_submitted">تم إرسال الزيارة بنجاح</string><string name="visit_error">فشل في إرسال الزيارة</string><string name="notes_hint">أضف ملاحظات حول الزيارة...</string><string name="filter_by_status">فلترة حسب الحالة</string><string name="multi_tasks">المهام المتعددة</string><string name="task_priority_urgent">عاجل</string><string name="task_executed_successfully">تم تنفيذ المهمة بنجاح</string><string name="task_type_normal">مهمة عادية</string><string name="success_rate">معدل النجاح: %.1f%%</string><string name="enhanced_tasks">المهام المحسنة</string><string name="one_time_tasks">المهام لمرة واحدة</string><string name="task_type_multi">مهمة متعددة</string><string name="task_details">تفاصيل المهمة</string><string name="task_due_soon">قريباً</string><string name="task_priority_medium">متوسط</string><string name="task_type_recurring">مهمة متكررة</string><string name="task_type_child">زيارة فرعية</string><string name="task_assigned_at">تاريخ التكليف: %1$s</string><string name="multi_tasks_title">المهام متعددة الزيارات</string><string name="enhanced_tasks_title">المهام المحسنة</string><string name="task_status_assigned">مُكلف</string><string name="no_tasks">لا توجد مهام حالياً</string><string name="loading_tasks">جاري تحميل المهام...</string><string name="search_tasks">البحث في المهام</string><string name="task_assigned_by">كُلف من: %1$s</string><string name="no_tasks_found">لا توجد مهام</string><string name="execute_task">تنفيذ المهمة</string><string name="task_due_date">الموعد المحدد: %1$s</string><string name="visits_created">تم إنشاء %d زيارة جديدة</string><string name="task_status_completed">مكتمل</string><string name="task_status_cancelled">ملغي</string><string name="task_acknowledged">تم تأكيد الاطلاع على المهمة</string><string name="task_continue">متابعة التنفيذ</string><string name="recurring_tasks">المهام المتكررة</string><string name="child_visits">الزيارات الفرعية</string><string name="task_acknowledge">تأكيد الاطلاع</string><string name="task_overdue">متأخرة</string><string name="next_execution">التنفيذ التالي</string><string name="task_due_now">مستحقة الآن</string><string name="task_not_due">المهمة ليست مستحقة للتنفيذ</string><string name="tasks_title">المهام المكلف بها</string><string name="progress_visits">%d/%d زيارات مكتملة</string><string name="task_started">تم بدء تنفيذ المهمة</string><string name="task_status_in_progress">قيد التنفيذ</string><string name="filter_by_type">فلترة حسب النوع</string><string name="all_tasks">جميع المهام</string><string name="task_start">بدء التنفيذ</string><string name="task_priority_high">عالي</string><string name="task_priority_low">منخفض</string><string name="refresh_tasks">تحديث المهام</string><string name="task_status_acknowledged">تم الاطلاع</string><string name="executions_count">تم التنفيذ %d مرة</string><string name="my_tasks">مهامي</string></file><file path="D:\django_project\TrackCustomer\android_app\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.FieldSalesTracker" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        
        <item name="colorSecondary">@color/secondary_color</item>
        <item name="colorSecondaryVariant">@color/secondary_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        
        <item name="android:statusBarColor">@color/primary_dark</item>
        
        
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
        
        
        <item name="actionBarStyle">@style/CustomActionBar</item>
        
        
        <item name="materialButtonStyle">@style/CustomButton</item>
        
        
        <item name="materialCardViewStyle">@style/CustomCard</item>
    </style><style name="Theme.FieldSalesTracker.NoActionBar" parent="Theme.FieldSalesTracker">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
    </style><style name="Theme.FieldSalesTracker.Splash" parent="Theme.FieldSalesTracker.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style><style name="CustomActionBar" parent="Widget.Material3.ActionBar.Solid">
        <item name="background">@color/primary_color</item>
        <item name="titleTextStyle">@style/ActionBarTitle</item>
        <item name="elevation">4dp</item>
    </style><style name="ActionBarTitle" parent="TextAppearance.Material3.ActionBar.Title">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">20sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="CustomButton" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/primary_color</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:minHeight">48dp</item>
    </style><style name="CustomCard" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="contentPadding">16dp</item>
    </style><style name="TextHeadline" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextTitle" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextBody" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_primary</item>
    </style><style name="TextCaption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/text_secondary</item>
    </style><style name="CustomTextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary_color</item>
        <item name="hintTextColor">@color/text_hint</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style><style name="CustomFAB" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="backgroundTint">@color/primary_color</item>
        <item name="tint">@color/white</item>
        <item name="elevation">6dp</item>
    </style></file><file name="backup_rules" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="activity_task_details" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\activity_task_details.xml" qualifiers="" type="layout"/><file name="activity_tasks" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\activity_tasks.xml" qualifiers="" type="layout"/><file name="item_task" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\item_task.xml" qualifiers="" type="layout"/><file name="button_acknowledge" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\button_acknowledge.xml" qualifiers="" type="drawable"/><file name="button_start" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\button_start.xml" qualifiers="" type="drawable"/><file name="ic_alarm" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_alarm.xml" qualifiers="" type="drawable"/><file name="ic_assignment" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_assignment.xml" qualifiers="" type="drawable"/><file name="ic_enhanced_task" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_enhanced_task.xml" qualifiers="" type="drawable"/><file name="ic_location_on" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_location_on.xml" qualifiers="" type="drawable"/><file name="ic_multi_task" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_multi_task.xml" qualifiers="" type="drawable"/><file name="ic_schedule" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_schedule.xml" qualifiers="" type="drawable"/><file name="badge_background" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\badge_background.xml" qualifiers="" type="drawable"/><file name="button_scanner" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\button_scanner.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_group" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_group.xml" qualifiers="" type="drawable"/><file name="ic_help" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_help.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_play_arrow.xml" qualifiers="" type="drawable"/><file name="ic_priority_high" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_priority_high.xml" qualifiers="" type="drawable"/><file name="ic_repeat" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_repeat.xml" qualifiers="" type="drawable"/><file name="ic_subdirectory_arrow_right" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_subdirectory_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_task" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_task.xml" qualifiers="" type="drawable"/><file name="ic_event" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_event.xml" qualifiers="" type="drawable"/><file name="ic_priority_low" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_priority_low.xml" qualifiers="" type="drawable"/><file name="ic_priority_medium" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\drawable\ic_priority_medium.xml" qualifiers="" type="drawable"/><file name="activity_enhanced_tasks" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\activity_enhanced_tasks.xml" qualifiers="" type="layout"/><file name="activity_multi_tasks" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\activity_multi_tasks.xml" qualifiers="" type="layout"/><file name="item_enhanced_task" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\item_enhanced_task.xml" qualifiers="" type="layout"/><file name="item_multi_task" path="D:\django_project\TrackCustomer\android_app\app\src\main\res\layout\item_multi_task.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\django_project\TrackCustomer\android_app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\django_project\TrackCustomer\android_app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\django_project\TrackCustomer\android_app\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\django_project\TrackCustomer\android_app\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>