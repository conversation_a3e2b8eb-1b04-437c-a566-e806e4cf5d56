from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid


class Notification(models.Model):
    """نموذج الإشعارات"""
    NOTIFICATION_TYPES = [
        ('task_assigned', 'مهمة جديدة'),
        ('task_updated', 'تحديث مهمة'),
        ('task_cancelled', 'إلغاء مهمة'),
        ('visit_approved', 'موافقة على زيارة'),
        ('visit_rejected', 'رفض زيارة'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='notifications'
    )
    title = models.CharField(max_length=200)
    message = models.TextField()
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    
    # ربط بالمهمة أو الزيارة
    related_task = models.ForeignKey(
        'Visit',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='notifications'
    )
    
    class Meta:
        ordering = ['-created_at']
        db_table = 'notifications'
        verbose_name = 'إشعار'
        verbose_name_plural = 'الإشعارات'
    
    def __str__(self):
        return f"{self.title} - {self.user.get_full_name()}"
    
    def mark_as_read(self):
        """تحديد الإشعار كمقروء"""
        self.is_read = True
        self.save(update_fields=['is_read'])


def create_task_notification(task, notification_type='task_assigned'):
    """إنشاء إشعار للمهمة"""
    try:
        # تحديد العنوان والرسالة حسب نوع الإشعار
        if notification_type == 'task_assigned':
            title = 'مهمة جديدة'
            message = f'تم تكليفك بمهمة جديدة: {task.task_title or f"زيارة {task.client.name}"}'
        elif notification_type == 'task_updated':
            title = 'تحديث مهمة'
            message = f'تم تحديث المهمة: {task.task_title or f"زيارة {task.client.name}"}'
        elif notification_type == 'task_cancelled':
            title = 'إلغاء مهمة'
            message = f'تم إلغاء المهمة: {task.task_title or f"زيارة {task.client.name}"}'
        else:
            title = 'إشعار'
            message = f'تحديث على المهمة: {task.task_title or f"زيارة {task.client.name}"}'
        
        # إنشاء الإشعار
        notification = Notification.objects.create(
            user=task.sales_rep,
            title=title,
            message=message,
            notification_type=notification_type,
            related_task=task
        )
        
        return notification
        
    except Exception as e:
        print(f"Error creating notification: {e}")
        return None


def create_visit_notification(visit, notification_type='visit_approved'):
    """إنشاء إشعار للزيارة"""
    try:
        # تحديد العنوان والرسالة حسب نوع الإشعار
        if notification_type == 'visit_approved':
            title = 'موافقة على زيارة'
            message = f'تم الموافقة على زيارتك لـ {visit.client.name}'
        elif notification_type == 'visit_rejected':
            title = 'رفض زيارة'
            message = f'تم رفض زيارتك لـ {visit.client.name}'
            if visit.rejection_reason:
                message += f'\nالسبب: {visit.rejection_reason}'
        else:
            title = 'تحديث زيارة'
            message = f'تحديث على زيارتك لـ {visit.client.name}'
        
        # إنشاء الإشعار
        notification = Notification.objects.create(
            user=visit.sales_rep,
            title=title,
            message=message,
            notification_type=notification_type,
            related_task=visit
        )
        
        return notification
        
    except Exception as e:
        print(f"Error creating visit notification: {e}")
        return None


def get_user_notifications(user, limit=50):
    """جلب إشعارات المستخدم"""
    return Notification.objects.filter(user=user).order_by('-created_at')[:limit]


def get_unread_count(user):
    """جلب عدد الإشعارات غير المقروءة"""
    return Notification.objects.filter(user=user, is_read=False).count()


def mark_all_as_read(user):
    """تحديد جميع الإشعارات كمقروءة"""
    return Notification.objects.filter(user=user, is_read=False).update(is_read=True)
