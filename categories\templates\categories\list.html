{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}جميع التصنيفات{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
<style>
    .category-type-badge {
        font-size: 0.8rem;
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: 600;
    }
    
    .category-type-1 { 
        background: linear-gradient(45deg, #e74c3c, #c0392b); 
        color: white;
    }
    .category-type-2 { 
        background: linear-gradient(45deg, #3498db, #2980b9); 
        color: white;
    }
    .category-type-3 { 
        background: linear-gradient(45deg, #2ecc71, #27ae60); 
        color: white;
    }
    
    .filter-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        margin-bottom: 20px;
    }
    
    .stats-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .category-actions .btn {
        margin: 2px;
        border-radius: 8px;
    }
    
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
    
    .dataTables_wrapper .dataTables_filter input {
        border-radius: 20px;
        border: 2px solid #e9ecef;
        padding: 8px 15px;
    }
    
    .dataTables_wrapper .dataTables_length select {
        border-radius: 8px;
        border: 2px solid #e9ecef;
        padding: 5px 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">جميع التصنيفات</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-left">
                        <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'categories:dashboard' %}">التصنيفات</a></li>
                        <li class="breadcrumb-item active">جميع التصنيفات</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-lg-3 col-6">
                    <div class="stats-card card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0" id="totalCategories">0</h3>
                                    <p class="mb-0">إجمالي التصنيفات</p>
                                </div>
                                <i class="bi bi-tags display-4 opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-6">
                    <div class="stats-card card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0" id="userCategories">0</h3>
                                    <p class="mb-0">تصنيفات المستخدمين</p>
                                </div>
                                <i class="bi bi-people display-4 opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-6">
                    <div class="stats-card card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0" id="repCategories">0</h3>
                                    <p class="mb-0">تصنيفات المناديب</p>
                                </div>
                                <i class="bi bi-person-badge display-4 opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-6">
                    <div class="stats-card card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0" id="clientCategories">0</h3>
                                    <p class="mb-0">تصنيفات العملاء</p>
                                </div>
                                <i class="bi bi-building display-4 opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="filter-card card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="bi bi-funnel me-2"></i>
                        فلاتر البحث والتصفية
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="categoryTypeFilter" class="form-label">نوع التصنيف</label>
                            <select class="form-select" id="categoryTypeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="1" {% if initial_filter_type == '1' %}selected{% endif %}>تصنيفات المستخدمين</option>
                                <option value="2" {% if initial_filter_type == '2' %}selected{% endif %}>تصنيفات المناديب</option>
                                <option value="3" {% if initial_filter_type == '3' %}selected{% endif %}>تصنيفات العملاء</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="statusFilter" class="form-label">الحالة</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="searchInput" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="ابحث في الاسم أو الوصف...">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="button" class="btn btn-light me-2" onclick="applyFilters()">
                                <i class="bi bi-search me-1"></i>
                                تطبيق الفلاتر
                            </button>
                            <button type="button" class="btn btn-outline-light" onclick="clearFilters()">
                                <i class="bi bi-x-circle me-1"></i>
                                مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول التصنيفات -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-list me-2"></i>
                        قائمة التصنيفات
                    </h3>
                    <div class="card-tools">
                        {% if request.user.is_super_manager %}
                        <div class="btn-group">
                            <button type="button" class="btn btn-success btn-sm" onclick="showAddCategoryModal()">
                                <i class="bi bi-plus me-1"></i>
                                إضافة تصنيف
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="exportCategories()">
                                <i class="bi bi-download me-1"></i>
                                تصدير
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="categoriesTable" class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                    <th>عدد المستخدمين</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تحميل البيانات عبر AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>

<!-- Modal إضافة تصنيف جديد -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة تصنيف جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCategoryForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">اسم التصنيف <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="categoryName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="categoryType" class="form-label">نوع التصنيف <span class="text-danger">*</span></label>
                        <select class="form-select" id="categoryType" name="category_type" required>
                            <option value="">اختر النوع</option>
                            <option value="1">تصنيفات المستخدمين</option>
                            <option value="2">تصنيفات المناديب</option>
                            <option value="3">تصنيفات العملاء</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="categoryDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="categoryActive" name="is_active" checked>
                            <label class="form-check-label" for="categoryActive">
                                تفعيل التصنيف
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check me-1"></i>
                        إضافة التصنيف
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
let categoriesTable;

$(document).ready(function() {
    // تهيئة DataTable
    categoriesTable = $('#categoriesTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{% url "categories:api_list" %}',
            type: 'GET',
            data: function(d) {
                d.category_type = $('#categoryTypeFilter').val();
                d.status = $('#statusFilter').val();
                d.search_term = $('#searchInput').val();
            }
        },
        columns: [
            { data: 'name', name: 'name' },
            { 
                data: 'category_type', 
                name: 'category_type',
                render: function(data, type, row) {
                    const types = {
                        1: '<span class="category-type-badge category-type-1">تصنيفات المستخدمين</span>',
                        2: '<span class="category-type-badge category-type-2">تصنيفات المناديب</span>',
                        3: '<span class="category-type-badge category-type-3">تصنيفات العملاء</span>'
                    };
                    return types[data] || data;
                }
            },
            { data: 'description', name: 'description' },
            { 
                data: 'is_active', 
                name: 'is_active',
                render: function(data, type, row) {
                    return data ? 
                        '<span class="badge bg-success">نشط</span>' : 
                        '<span class="badge bg-secondary">غير نشط</span>';
                }
            },
            { data: 'users_count', name: 'users_count' },
            { 
                data: 'created_at', 
                name: 'created_at',
                render: function(data, type, row) {
                    return new Date(data).toLocaleDateString('ar-SA');
                }
            },
            { 
                data: 'actions', 
                name: 'actions', 
                orderable: false, 
                searchable: false,
                render: function(data, type, row) {
                    let actions = '<div class="category-actions">';
                    actions += `<button class="btn btn-sm btn-info" onclick="viewCategory(${row.id})" title="عرض">`;
                    actions += '<i class="bi bi-eye"></i></button>';
                    
                    {% if request.user.is_super_manager %}
                    actions += `<button class="btn btn-sm btn-warning" onclick="editCategory(${row.id})" title="تعديل">`;
                    actions += '<i class="bi bi-pencil"></i></button>';
                    actions += `<button class="btn btn-sm btn-danger" onclick="deleteCategory(${row.id})" title="حذف">`;
                    actions += '<i class="bi bi-trash"></i></button>';
                    {% endif %}
                    
                    actions += '</div>';
                    return actions;
                }
            }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        order: [[5, 'desc']],
        pageLength: 25,
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: 'تصدير Excel',
                className: 'btn btn-success btn-sm'
            }
        ]
    });
    
    // تحديث الإحصائيات
    updateStats();

    // تطبيق الفلتر الأولي إذا كان موجود
    {% if initial_filter_type %}
    setTimeout(function() {
        applyFilters();
    }, 500);
    {% endif %}

    // إعداد نموذج الإضافة
    $('#addCategoryForm').on('submit', function(e) {
        e.preventDefault();
        addCategory();
    });
});

function updateStats() {
    $.get('{% url "categories:stats" %}', function(data) {
        $('#totalCategories').text(data.total || 0);
        $('#userCategories').text(data.type_1 || 0);
        $('#repCategories').text(data.type_2 || 0);
        $('#clientCategories').text(data.type_3 || 0);
    });
}

function applyFilters() {
    categoriesTable.ajax.reload();
}

function clearFilters() {
    $('#categoryTypeFilter').val('');
    $('#statusFilter').val('');
    $('#searchInput').val('');
    categoriesTable.ajax.reload();
}

function showAddCategoryModal() {
    $('#addCategoryModal').modal('show');
}

function addCategory() {
    const formData = new FormData($('#addCategoryForm')[0]);
    
    $.ajax({
        url: '{% url "categories:create_ajax" %}',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                $('#addCategoryModal').modal('hide');
                $('#addCategoryForm')[0].reset();
                categoriesTable.ajax.reload();
                updateStats();
                
                Swal.fire({
                    icon: 'success',
                    title: 'تم بنجاح',
                    text: 'تم إضافة التصنيف بنجاح',
                    timer: 2000
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: response.message || 'حدث خطأ أثناء إضافة التصنيف'
                });
            }
        },
        error: function() {
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ في الاتصال بالخادم'
            });
        }
    });
}

function viewCategory(id) {
    window.location.href = `/categories/${id}/users/`;
}

function editCategory(id) {
    // سيتم تطبيقها لاحقاً
    Swal.fire('قريباً', 'وظيفة التعديل ستكون متاحة قريباً', 'info');
}

function deleteCategory(id) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'لن تتمكن من التراجع عن هذا الإجراء!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // سيتم تطبيق الحذف لاحقاً
            Swal.fire('قريباً', 'وظيفة الحذف ستكون متاحة قريباً', 'info');
        }
    });
}

function exportCategories() {
    window.location.href = '{% url "categories:export" %}';
}
</script>
{% endblock %}
