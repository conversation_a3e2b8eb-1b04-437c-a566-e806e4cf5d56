// Generated by view binder compiler. Do not edit!
package com.company.fieldsalestracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.company.fieldsalestracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final Button btnAcknowledge;

  @NonNull
  public final Button btnStart;

  @NonNull
  public final CardView cardView;

  @NonNull
  public final TextView textAssignedAt;

  @NonNull
  public final TextView textAssignedBy;

  @NonNull
  public final TextView textClient;

  @NonNull
  public final TextView textDescription;

  @NonNull
  public final TextView textDueDate;

  @NonNull
  public final TextView textPriority;

  @NonNull
  public final TextView textStatus;

  @NonNull
  public final TextView textTitle;

  private ItemTaskBinding(@NonNull CardView rootView, @NonNull Button btnAcknowledge,
      @NonNull Button btnStart, @NonNull CardView cardView, @NonNull TextView textAssignedAt,
      @NonNull TextView textAssignedBy, @NonNull TextView textClient,
      @NonNull TextView textDescription, @NonNull TextView textDueDate,
      @NonNull TextView textPriority, @NonNull TextView textStatus, @NonNull TextView textTitle) {
    this.rootView = rootView;
    this.btnAcknowledge = btnAcknowledge;
    this.btnStart = btnStart;
    this.cardView = cardView;
    this.textAssignedAt = textAssignedAt;
    this.textAssignedBy = textAssignedBy;
    this.textClient = textClient;
    this.textDescription = textDescription;
    this.textDueDate = textDueDate;
    this.textPriority = textPriority;
    this.textStatus = textStatus;
    this.textTitle = textTitle;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAcknowledge;
      Button btnAcknowledge = ViewBindings.findChildViewById(rootView, id);
      if (btnAcknowledge == null) {
        break missingId;
      }

      id = R.id.btnStart;
      Button btnStart = ViewBindings.findChildViewById(rootView, id);
      if (btnStart == null) {
        break missingId;
      }

      CardView cardView = (CardView) rootView;

      id = R.id.textAssignedAt;
      TextView textAssignedAt = ViewBindings.findChildViewById(rootView, id);
      if (textAssignedAt == null) {
        break missingId;
      }

      id = R.id.textAssignedBy;
      TextView textAssignedBy = ViewBindings.findChildViewById(rootView, id);
      if (textAssignedBy == null) {
        break missingId;
      }

      id = R.id.textClient;
      TextView textClient = ViewBindings.findChildViewById(rootView, id);
      if (textClient == null) {
        break missingId;
      }

      id = R.id.textDescription;
      TextView textDescription = ViewBindings.findChildViewById(rootView, id);
      if (textDescription == null) {
        break missingId;
      }

      id = R.id.textDueDate;
      TextView textDueDate = ViewBindings.findChildViewById(rootView, id);
      if (textDueDate == null) {
        break missingId;
      }

      id = R.id.textPriority;
      TextView textPriority = ViewBindings.findChildViewById(rootView, id);
      if (textPriority == null) {
        break missingId;
      }

      id = R.id.textStatus;
      TextView textStatus = ViewBindings.findChildViewById(rootView, id);
      if (textStatus == null) {
        break missingId;
      }

      id = R.id.textTitle;
      TextView textTitle = ViewBindings.findChildViewById(rootView, id);
      if (textTitle == null) {
        break missingId;
      }

      return new ItemTaskBinding((CardView) rootView, btnAcknowledge, btnStart, cardView,
          textAssignedAt, textAssignedBy, textClient, textDescription, textDueDate, textPriority,
          textStatus, textTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
