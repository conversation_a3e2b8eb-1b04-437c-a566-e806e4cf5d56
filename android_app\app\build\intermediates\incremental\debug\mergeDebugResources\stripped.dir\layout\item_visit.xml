<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Visit Icon -->
        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/circle_background"
            android:padding="8dp"
            android:src="@drawable/ic_location"
            app:tint="@color/white" />

        <!-- Visit Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvClientName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="شركة الأمل التجارية" />

            <TextView
                android:id="@+id/tvVisitTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                tools:text="2024-01-15 10:30" />

            <!-- Rejection Reason (only visible for rejected visits) -->
            <TextView
                android:id="@+id/tvRejectionReason"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/error_color"
                android:textSize="12sp"
                android:textStyle="italic"
                android:visibility="gone"
                tools:text="سبب الرفض: المسافة بعيدة جداً" />

        </LinearLayout>

        <!-- Status -->
        <TextView
            android:id="@+id/tvVisitStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="16dp"
            android:background="@drawable/status_background"
            android:padding="8dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            tools:text="مؤكدة" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
