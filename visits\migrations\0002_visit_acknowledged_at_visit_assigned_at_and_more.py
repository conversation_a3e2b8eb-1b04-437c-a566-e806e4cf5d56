# Generated by Django 5.2.4 on 2025-07-20 05:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('visits', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='visit',
            name='acknowledged_at',
            field=models.DateTimeField(blank=True, help_text='تاريخ اطلاع المندوب على المهمة', null=True),
        ),
        migrations.AddField(
            model_name='visit',
            name='assigned_at',
            field=models.DateTimeField(blank=True, help_text='تاريخ تكليف المهمة', null=True),
        ),
        migrations.AddField(
            model_name='visit',
            name='assigned_by',
            field=models.ForeignKey(blank=True, help_text='المدير الذي كلف بهذه المهمة', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_tasks', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='visit',
            name='completed_at',
            field=models.DateTimeField(blank=True, help_text='تاريخ إكمال المهمة', null=True),
        ),
        migrations.AddField(
            model_name='visit',
            name='due_date',
            field=models.DateTimeField(blank=True, help_text='الموعد المحدد لإنجاز المهمة', null=True),
        ),
        migrations.AddField(
            model_name='visit',
            name='priority',
            field=models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('urgent', 'عاجل')], default='medium', help_text='أولوية المهمة', max_length=10),
        ),
        migrations.AddField(
            model_name='visit',
            name='started_at',
            field=models.DateTimeField(blank=True, help_text='تاريخ بدء تنفيذ المهمة', null=True),
        ),
        migrations.AddField(
            model_name='visit',
            name='task_description',
            field=models.TextField(blank=True, help_text='وصف المهمة', null=True),
        ),
        migrations.AddField(
            model_name='visit',
            name='task_status',
            field=models.CharField(choices=[('assigned', 'مُكلف'), ('acknowledged', 'تم الاطلاع'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='assigned', help_text='حالة المهمة', max_length=20),
        ),
        migrations.AddField(
            model_name='visit',
            name='task_title',
            field=models.CharField(blank=True, help_text='عنوان المهمة', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='visit',
            name='task_type',
            field=models.CharField(choices=[('self_initiated', 'زيارة ذاتية'), ('manager_assigned', 'مهمة من المدير')], default='self_initiated', help_text='نوع المهمة', max_length=20),
        ),
        migrations.AlterField(
            model_name='visit',
            name='barcode_scanned',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='visit',
            name='visit_image',
            field=models.ImageField(blank=True, null=True, upload_to='visit_images/'),
        ),
        migrations.AlterField(
            model_name='visit',
            name='visit_latitude',
            field=models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='visit',
            name='visit_longitude',
            field=models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True),
        ),
    ]
