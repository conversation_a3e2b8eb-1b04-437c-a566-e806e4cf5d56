<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background_color">#F8FAFC</color>
    <color name="black">#FF000000</color>
    <color name="border_color">#E2E8F0</color>
    <color name="btn_camera_border">#E3F2FD</color>
    <color name="btn_camera_end">#1976D2</color>
    <color name="btn_camera_pressed_end">#0D47A1</color>
    <color name="btn_camera_pressed_start">#1976D2</color>
    <color name="btn_camera_start">#2196F3</color>
    <color name="btn_submit_border">#C8E6C9</color>
    <color name="btn_submit_end">#388E3C</color>
    <color name="btn_submit_pressed_end">#1B5E20</color>
    <color name="btn_submit_pressed_start">#388E3C</color>
    <color name="btn_submit_start">#4CAF50</color>
    <color name="card_background">#FFFFFF</color>
    <color name="colorAccent">#FF4081</color>
    <color name="colorPrimary">#2196F3</color>
    <color name="colorPrimaryDark">#1976D2</color>
    <color name="divider_color">#F1F5F9</color>
    <color name="error_background">#FEF2F2</color>
    <color name="error_color">#DC2626</color>
    <color name="info_background">#F0F9FF</color>
    <color name="info_color">#0EA5E9</color>
    <color name="overlay_dark">#80000000</color>
    <color name="overlay_light">#40FFFFFF</color>
    <color name="primary_color">#2563EB</color>
    <color name="primary_dark">#1D4ED8</color>
    <color name="primary_light">#3B82F6</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="secondary_color">#64748B</color>
    <color name="secondary_dark">#475569</color>
    <color name="secondary_light">#94A3B8</color>
    <color name="success_background">#ECFDF5</color>
    <color name="success_color">#059669</color>
    <color name="surface_color">#FFFFFF</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_hint">#94A3B8</color>
    <color name="text_primary">#1E293B</color>
    <color name="text_secondary">#64748B</color>
    <color name="text_white">#FFFFFF</color>
    <color name="warning_background">#FFFBEB</color>
    <color name="warning_color">#D97706</color>
    <color name="white">#FFFFFFFF</color>
    <string name="all_tasks">جميع المهام</string>
    <string name="app_name">Field Sales Tracker</string>
    <string name="barcode_detected">تم اكتشاف الباركود: %1$s</string>
    <string name="camera_permission_required">صلاحية الكاميرا مطلوبة</string>
    <string name="cancel">إلغاء</string>
    <string name="child_visits">الزيارات الفرعية</string>
    <string name="client_address">العنوان: %1$s</string>
    <string name="client_distance">المسافة: %1$s متر</string>
    <string name="client_name">اسم العميل</string>
    <string name="client_not_found">العميل غير موجود</string>
    <string name="client_phone">الهاتف: %1$s</string>
    <string name="clients_title">العملاء</string>
    <string name="enhanced_tasks">المهام المحسنة</string>
    <string name="enhanced_tasks_title">المهام المحسنة</string>
    <string name="execute_task">تنفيذ المهمة</string>
    <string name="executions_count">تم التنفيذ %d مرة</string>
    <string name="filter_by_status">فلترة حسب الحالة</string>
    <string name="filter_by_type">فلترة حسب النوع</string>
    <string name="invalid_barcode">باركود غير صحيح</string>
    <string name="loading">جاري التحميل...</string>
    <string name="loading_tasks">جاري تحميل المهام...</string>
    <string name="location_permission_required">صلاحية الموقع مطلوبة</string>
    <string name="location_too_far">الموقع بعيد جداً عن العميل</string>
    <string name="login_button">دخول</string>
    <string name="login_error">خطأ في تسجيل الدخول</string>
    <string name="login_title">تسجيل الدخول</string>
    <string name="logout">تسجيل الخروج</string>
    <string name="multi_tasks">المهام المتعددة</string>
    <string name="multi_tasks_title">المهام متعددة الزيارات</string>
    <string name="my_tasks">مهامي</string>
    <string name="my_visits">زياراتي</string>
    <string name="network_error">خطأ في الشبكة</string>
    <string name="next_execution">التنفيذ التالي</string>
    <string name="no_tasks">لا توجد مهام حالياً</string>
    <string name="no_tasks_found">لا توجد مهام</string>
    <string name="notes_hint">أضف ملاحظات حول الزيارة...</string>
    <string name="ok">موافق</string>
    <string name="one_time_tasks">المهام لمرة واحدة</string>
    <string name="password">كلمة المرور</string>
    <string name="permissions_required">الصلاحيات مطلوبة لعمل التطبيق</string>
    <string name="photo_taken">تم التقاط الصورة</string>
    <string name="progress_visits">%d/%d زيارات مكتملة</string>
    <string name="recurring_tasks">المهام المتكررة</string>
    <string name="refresh">تحديث</string>
    <string name="refresh_tasks">تحديث المهام</string>
    <string name="retry">إعادة المحاولة</string>
    <string name="scan_barcode">مسح الباركود</string>
    <string name="scanner_instruction">وجه الكاميرا نحو الباركود</string>
    <string name="scanner_title">مسح الباركود</string>
    <string name="search_tasks">البحث في المهام</string>
    <string name="server_error">خطأ في الخادم</string>
    <string name="submit_visit">إرسال الزيارة</string>
    <string name="success_rate">معدل النجاح: %.1f%%</string>
    <string name="take_photo">التقاط صورة</string>
    <string name="task_acknowledge">تأكيد الاطلاع</string>
    <string name="task_acknowledged">تم تأكيد الاطلاع على المهمة</string>
    <string name="task_assigned_at">تاريخ التكليف: %1$s</string>
    <string name="task_assigned_by">كُلف من: %1$s</string>
    <string name="task_continue">متابعة التنفيذ</string>
    <string name="task_details">تفاصيل المهمة</string>
    <string name="task_due_date">الموعد المحدد: %1$s</string>
    <string name="task_due_now">مستحقة الآن</string>
    <string name="task_due_soon">قريباً</string>
    <string name="task_executed_successfully">تم تنفيذ المهمة بنجاح</string>
    <string name="task_not_due">المهمة ليست مستحقة للتنفيذ</string>
    <string name="task_overdue">متأخرة</string>
    <string name="task_priority_high">عالي</string>
    <string name="task_priority_low">منخفض</string>
    <string name="task_priority_medium">متوسط</string>
    <string name="task_priority_urgent">عاجل</string>
    <string name="task_start">بدء التنفيذ</string>
    <string name="task_started">تم بدء تنفيذ المهمة</string>
    <string name="task_status_acknowledged">تم الاطلاع</string>
    <string name="task_status_assigned">مُكلف</string>
    <string name="task_status_cancelled">ملغي</string>
    <string name="task_status_completed">مكتمل</string>
    <string name="task_status_in_progress">قيد التنفيذ</string>
    <string name="task_type_child">زيارة فرعية</string>
    <string name="task_type_multi">مهمة متعددة</string>
    <string name="task_type_normal">مهمة عادية</string>
    <string name="task_type_recurring">مهمة متكررة</string>
    <string name="tasks_title">المهام المكلف بها</string>
    <string name="unknown_error">خطأ غير معروف</string>
    <string name="username">اسم المستخدم</string>
    <string name="visit_error">فشل في إرسال الزيارة</string>
    <string name="visit_notes">ملاحظات الزيارة</string>
    <string name="visit_submitted">تم إرسال الزيارة بنجاح</string>
    <string name="visit_success">تم تسجيل الزيارة بنجاح</string>
    <string name="visit_title">تفاصيل الزيارة</string>
    <string name="visits_created">تم إنشاء %d زيارة جديدة</string>
    <style name="ActionBarTitle" parent="TextAppearance.Material3.ActionBar.Title">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">20sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="CustomActionBar" parent="Widget.Material3.ActionBar.Solid">
        <item name="background">@color/primary_color</item>
        <item name="titleTextStyle">@style/ActionBarTitle</item>
        <item name="elevation">4dp</item>
    </style>
    <style name="CustomButton" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/primary_color</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:minHeight">48dp</item>
    </style>
    <style name="CustomCard" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="contentPadding">16dp</item>
    </style>
    <style name="CustomFAB" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="backgroundTint">@color/primary_color</item>
        <item name="tint">@color/white</item>
        <item name="elevation">6dp</item>
    </style>
    <style name="CustomTextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary_color</item>
        <item name="hintTextColor">@color/text_hint</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style>
    <style name="TextBody" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_primary</item>
    </style>
    <style name="TextCaption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/text_secondary</item>
    </style>
    <style name="TextHeadline" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextTitle" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="Theme.FieldSalesTracker" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        
        <item name="colorSecondary">@color/secondary_color</item>
        <item name="colorSecondaryVariant">@color/secondary_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        
        <item name="android:statusBarColor">@color/primary_dark</item>
        
        
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
        
        
        <item name="actionBarStyle">@style/CustomActionBar</item>
        
        
        <item name="materialButtonStyle">@style/CustomButton</item>
        
        
        <item name="materialCardViewStyle">@style/CustomCard</item>
    </style>
    <style name="Theme.FieldSalesTracker.NoActionBar" parent="Theme.FieldSalesTracker">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
    </style>
    <style name="Theme.FieldSalesTracker.Splash" parent="Theme.FieldSalesTracker.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>
</resources>