{"logs": [{"outputFile": "com.company.fieldsalestracker.app-mergeDebugResources-53:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\043eee66ca32b7ab1e056bec5c84992b\\transformed\\core-1.9.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "117", "startColumns": "4", "startOffsets": "10929", "endColumns": "100", "endOffsets": "11025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43bdbc0b1ff399fa79cb359f37c50201\\transformed\\material-1.9.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,411,496,577,682,770,871,1005,1088,1153,1247,1320,1381,1506,1572,1640,1701,1773,1833,1887,2007,2067,2129,2183,2260,2390,2477,2559,2700,2780,2865,2956,3010,3063,3129,3203,3284,3368,3441,3518,3595,3669,3762,3837,3927,4018,4090,4168,4259,4313,4381,4465,4552,4614,4678,4741,4851,4964,5067,5179,5237,5294", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "12,86,84,80,104,87,100,133,82,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,81,140,79,84,90,53,52,65,73,80,83,72,76,76,73,92,74,89,90,71,77,90,53,67,83,86,61,63,62,109,112,102,111,57,56,76", "endOffsets": "319,406,491,572,677,765,866,1000,1083,1148,1242,1315,1376,1501,1567,1635,1696,1768,1828,1882,2002,2062,2124,2178,2255,2385,2472,2554,2695,2775,2860,2951,3005,3058,3124,3198,3279,3363,3436,3513,3590,3664,3757,3832,3922,4013,4085,4163,4254,4308,4376,4460,4547,4609,4673,4736,4846,4959,5062,5174,5232,5289,5366"}, "to": {"startLines": "2,34,35,36,37,38,39,40,41,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3258,3345,3430,3511,3616,3704,3805,3939,6326,6391,6485,6558,6619,6744,6810,6878,6939,7011,7071,7125,7245,7305,7367,7421,7498,7628,7715,7797,7938,8018,8103,8194,8248,8301,8367,8441,8522,8606,8679,8756,8833,8907,9000,9075,9165,9256,9328,9406,9497,9551,9619,9703,9790,9852,9916,9979,10089,10202,10305,10417,10475,10769", "endLines": "6,34,35,36,37,38,39,40,41,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,115", "endColumns": "12,86,84,80,104,87,100,133,82,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,81,140,79,84,90,53,52,65,73,80,83,72,76,76,73,92,74,89,90,71,77,90,53,67,83,86,61,63,62,109,112,102,111,57,56,76", "endOffsets": "369,3340,3425,3506,3611,3699,3800,3934,4017,6386,6480,6553,6614,6739,6805,6873,6934,7006,7066,7120,7240,7300,7362,7416,7493,7623,7710,7792,7933,8013,8098,8189,8243,8296,8362,8436,8517,8601,8674,8751,8828,8902,8995,9070,9160,9251,9323,9401,9492,9546,9614,9698,9785,9847,9911,9974,10084,10197,10300,10412,10470,10527,10841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ce97e1d7afd464f10c84d8596a83f4ee\\transformed\\appcompat-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,494,604,713,799,903,1025,1107,1187,1297,1405,1511,1620,1731,1834,1946,2053,2158,2258,2343,2452,2563,2662,2773,2880,2985,3159,10846", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "489,599,708,794,898,1020,1102,1182,1292,1400,1506,1615,1726,1829,1941,2048,2153,2253,2338,2447,2558,2657,2768,2875,2980,3154,3253,10924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb7e328e390e7f202e781ab04bb4484f\\transformed\\jetified-play-services-base-18.1.0\\res\\values-lv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,686,824,951,1064,1166,1337,1442,1607,1738,1903,2054,2114,2178", "endColumns": "102,156,128,103,137,126,112,101,170,104,164,130,164,150,59,63,84", "endOffsets": "295,452,581,685,823,950,1063,1165,1336,1441,1606,1737,1902,2053,2113,2177,2262"}, "to": {"startLines": "42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4022,4129,4290,4423,4531,4673,4804,4921,5193,5368,5477,5646,5781,5950,6105,6169,6237", "endColumns": "106,160,132,107,141,130,116,105,174,108,168,134,168,154,63,67,88", "endOffsets": "4124,4285,4418,4526,4668,4799,4916,5022,5363,5472,5641,5776,5945,6100,6164,6232,6321"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c88fe9f048cf6eb09539000df0730205\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-lv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "161", "endOffsets": "356"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "5027", "endColumns": "165", "endOffsets": "5188"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed709288d23a486d0424a1f6be3fc698\\transformed\\navigation-ui-2.6.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,122", "endOffsets": "164,287"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "10532,10646", "endColumns": "113,122", "endOffsets": "10641,10764"}}]}]}