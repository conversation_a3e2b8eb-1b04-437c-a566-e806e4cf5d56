<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/black"
    tools:context=".BarcodeScannerActivity">

    <!-- Top Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/overlay_dark"
        android:orientation="horizontal"
        android:padding="12dp">

        <ImageButton
            android:id="@+id/btnBack"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="رجوع"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="مسح الباركود"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Camera Preview for AUTOMATIC Barcode Scanning -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.camera.view.PreviewView
            android:id="@+id/previewView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!-- Scanner Overlay -->
        <View
            android:layout_width="250dp"
            android:layout_height="250dp"
            android:layout_gravity="center"
            android:background="@drawable/scanner_overlay" />

        <!-- Instructions -->
        <LinearLayout
            android:id="@+id/instructionsPanel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_margin="16dp"
            android:background="@color/overlay_dark"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="🔍 وجه الكاميرا نحو الباركود"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center"
                android:text="⚡ المسح التلقائي نشط"
                android:textColor="@color/success_color"
                android:textSize="14sp" />

            <!-- تم حذف زر الاختبار المؤقت -->

        </LinearLayout>

    </FrameLayout>

    <!-- Bottom Panel - Appears after barcode scan -->
    <ScrollView
        android:id="@+id/bottomPanel"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/surface_color"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Client Info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/circle_background"
                android:backgroundTint="@color/surface_color"
                android:orientation="vertical"
                android:padding="16dp"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="👤 بيانات العميل"
                    android:textColor="@color/primary_color"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvClientName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textColor="@color/text_primary"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tvClientAddress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tvClientPhone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

            </LinearLayout>



            <!-- Notes Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/circle_background"
                android:backgroundTint="@color/surface_color"
                android:orientation="vertical"
                android:padding="16dp"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📝 ملاحظات الزيارة"
                    android:textColor="@color/primary_color"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <EditText
                    android:id="@+id/etVisitNotes"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:background="@drawable/circle_background"
                    android:backgroundTint="@color/background_color"
                    android:gravity="top|start"
                    android:hint="اكتب ملاحظاتك حول الزيارة..."
                    android:inputType="textMultiLine"
                    android:padding="12dp"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_hint"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- Photo Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/circle_background"
                android:backgroundTint="@color/surface_color"
                android:orientation="vertical"
                android:padding="16dp"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📸 صورة الزيارة"
                    android:textColor="@color/primary_color"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <Button
                    android:id="@+id/btnTakePhoto"
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:background="@drawable/btn_camera_background"
                    android:drawableStart="@drawable/ic_camera_enhanced"
                    android:drawablePadding="12dp"
                    android:elevation="4dp"
                    android:text="📸 التقاط صورة"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textAllCaps="false"
                    android:enabled="false"
                    android:layout_marginBottom="8dp"
                    android:stateListAnimator="@null" />

                <ImageView
                    android:id="@+id/ivVisitPhoto"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:background="@color/border_color"
                    android:scaleType="centerCrop"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- Submit Button -->
            <Button
                android:id="@+id/btnSubmitVisit"
                android:layout_width="match_parent"
                android:layout_height="68dp"
                android:background="@drawable/btn_submit_background"
                android:drawableStart="@drawable/ic_send_enhanced"
                android:drawablePadding="16dp"
                android:elevation="6dp"
                android:text="📤 إرسال البيانات للخادم"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textAllCaps="false"
                android:enabled="false"
                android:stateListAnimator="@null"
                android:layout_marginTop="8dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
