{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/management-pages.css' %}">
<style>
.role-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
}
.role-admin { background-color: #e3f2fd; color: #1976d2; }
.role-manager { background-color: #f3e5f5; color: #7b1fa2; }
.role-user { background-color: #e8f5e8; color: #388e3c; }

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
}

.user-info {
    flex: 1;
    margin-left: 1rem;
}

.user-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.user-details {
    font-size: 0.85rem;
    color: #6c757d;
}

.user-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.user-item:hover {
    background-color: #f8f9fa;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-users-cog me-2"></i>
                {{ title }}
            </h1>
            <p class="text-muted mb-0">إدارة المستخدمين والمديرين في النظام</p>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-plus me-2"></i>
                إضافة مستخدم جديد
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="stats-card">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات المستخدمين
                </h5>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.total }}</span>
                        <span class="stat-label">إجمالي المستخدمين</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.active }}</span>
                        <span class="stat-label">المستخدمين النشطين</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.admins }}</span>
                        <span class="stat-label">المديرين العامين</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.managers }}</span>
                        <span class="stat-label">مديرو المستخدمين</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.inactive }}</span>
                        <span class="stat-label">غير نشطين</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث في المستخدمين..." 
                           value="{{ search_query }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">الدور</label>
                    <select name="role" class="form-select">
                        <option value="">جميع الأدوار</option>
                        <option value="admin" {% if role_filter == 'admin' %}selected{% endif %}>إدارة (مدير عام)</option>
                        <option value="manager" {% if role_filter == 'manager' %}selected{% endif %}>مستخدم (مدير مستخدمين)</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                        <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Users List -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة المستخدمين
            </h5>
            <div>
                <button class="btn btn-success btn-sm" onclick="exportUsers()">
                    <i class="fas fa-download me-1"></i>
                    تصدير Excel
                </button>
            </div>
        </div>
        <div class="card-body">
            {% if users %}
            <div class="users-list">
                {% for user in users %}
                <div class="user-item">
                    <div class="user-avatar">
                        {{ user.first_name|first|upper }}{{ user.last_name|first|upper }}
                    </div>
                    <div class="user-info">
                        <div class="user-name">{{ user.get_full_name }}</div>
                        <div class="user-details">
                            <span class="me-3">
                                <i class="fas fa-user me-1"></i>
                                {{ user.username }}
                            </span>
                            <span class="me-3">
                                <i class="fas fa-envelope me-1"></i>
                                {{ user.email|default:"-" }}
                            </span>
                            <span class="me-3">
                                <i class="fas fa-id-card me-1"></i>
                                {{ user.employee_id|default:"-" }}
                            </span>
                            <span>
                                <i class="fas fa-calendar me-1"></i>
                                {{ user.date_joined|date:"Y-m-d" }}
                            </span>
                        </div>
                    </div>
                    <div class="user-badges me-3">
                        {% if user.role_type == 1 %}
                        <span class="badge role-badge role-admin">مدير عام</span>
                        {% elif user.role_type == 2 %}
                        <span class="badge role-badge role-manager">مدير مستخدمين</span>
                        {% else %}
                        <span class="badge role-badge role-user">مستخدم</span>
                        {% endif %}
                        
                        {% if user.is_active and user.is_active_employee %}
                        <span class="badge bg-success">نشط</span>
                        {% else %}
                        <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                    </div>
                    <div class="user-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="viewUser('{{ user.id }}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="editUser('{{ user.id }}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        {% if user != request.user %}
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteUser('{{ user.id }}')">
                            <i class="fas fa-trash"></i>
                        </button>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if users.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if users.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ users.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابق</a>
                    </li>
                    {% endif %}

                    {% for num in users.paginator.page_range %}
                    {% if users.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > users.number|add:'-3' and num < users.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if users.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ users.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مستخدمين</h4>
                <p class="text-muted">لم يتم العثور على أي مستخدمين مطابقين للبحث</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول مستخدم
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الأول</label>
                            <input type="text" name="first_name" class="form-control" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الأخير</label>
                            <input type="text" name="last_name" class="form-control" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" name="username" class="form-control" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" name="email" class="form-control">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="text" name="phone_number" class="form-control">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الموظف</label>
                            <input type="text" name="employee_id" class="form-control">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الدور</label>
                            <select name="role_type" class="form-select" required>
                                <option value="">اختر الدور</option>
                                <option value="1">مدير عام</option>
                                <option value="2">مدير مستخدمين</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" name="password" class="form-control" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add User Form
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{% url "users:add_user" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('خطأ: ' + (data.error || 'حدث خطأ غير متوقع'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
});

function viewUser(id) {
    window.location.href = `/users/${id}/`;
}

function editUser(id) {
    window.location.href = `/users/${id}/edit/`;
}

function deleteUser(id) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        // سيتم تطبيق الحذف لاحقاً
        alert('وظيفة الحذف ستكون متاحة قريباً');
    }
}

function exportUsers() {
    // سيتم تطبيق التصدير لاحقاً
    alert('وظيفة التصدير ستكون متاحة قريباً');
}
</script>
{% endblock %}
