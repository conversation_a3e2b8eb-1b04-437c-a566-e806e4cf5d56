# Generated by Django 5.2.4 on 2025-07-21 02:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('visits', '0003_notification'),
    ]

    operations = [
        migrations.AddField(
            model_name='visit',
            name='is_recurring_task',
            field=models.BooleanField(default=False, help_text='هل هذه مهمة متكررة تلقائياً', verbose_name='مهمة متكررة'),
        ),
        migrations.AddField(
            model_name='visit',
            name='max_visits_per_execution',
            field=models.PositiveIntegerField(default=1, verbose_name='عدد الزيارات لكل تنفيذ'),
        ),
        migrations.AddField(
            model_name='visit',
            name='next_execution',
            field=models.DateTimeField(blank=True, null=True, verbose_name='التنفيذ التالي'),
        ),
        migrations.AddField(
            model_name='visit',
            name='parent_task',
            field=models.ForeignKey(blank=True, help_text='المهمة الرئيسية التي تنتمي إليها هذه الزيارة', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_visits', to='visits.visit', verbose_name='المهمة الأب'),
        ),
        migrations.AddField(
            model_name='visit',
            name='recurrence_end_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء التكرار'),
        ),
        migrations.AddField(
            model_name='visit',
            name='recurrence_interval',
            field=models.PositiveIntegerField(blank=True, default=1, help_text='كل كم (يوم/أسبوع/شهر) يتم التكرار', null=True, verbose_name='فترة التكرار'),
        ),
        migrations.AddField(
            model_name='visit',
            name='recurrence_type',
            field=models.CharField(blank=True, choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('custom', 'مخصص')], max_length=20, null=True, verbose_name='نوع التكرار'),
        ),
        migrations.AddField(
            model_name='visit',
            name='successful_executions',
            field=models.PositiveIntegerField(default=0, verbose_name='التنفيذات الناجحة'),
        ),
        migrations.AddField(
            model_name='visit',
            name='target_clients_list',
            field=models.TextField(blank=True, help_text='معرفات العملاء مفصولة بفواصل (للمهام متعددة العملاء)', null=True, verbose_name='قائمة العملاء المستهدفين'),
        ),
        migrations.AddField(
            model_name='visit',
            name='total_executions',
            field=models.PositiveIntegerField(default=0, verbose_name='إجمالي التنفيذات'),
        ),
    ]
