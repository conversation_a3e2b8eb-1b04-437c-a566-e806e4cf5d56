<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".VisitDetailsActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_color"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Client Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="معلومات العميل"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tvClientName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:layout_marginBottom="8dp"
                        tools:text="شركة الأمل التجارية" />

                    <TextView
                        android:id="@+id/tvClientAddress"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="8dp"
                        tools:text="الرياض، حي الملك فهد" />

                    <TextView
                        android:id="@+id/tvClientBarcode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="@color/info_color"
                        android:background="@color/divider_color"
                        android:padding="8dp"
                        tools:text="الباركود: AMC001" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Visit Details Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="تفاصيل الزيارة"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <!-- Take Photo Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnTakePhoto"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="@string/take_photo"
                        android:textColor="@color/primary_color"
                        app:strokeColor="@color/primary_color"
                        app:icon="@drawable/ic_camera"
                        app:iconTint="@color/primary_color" />

                    <!-- Notes Input -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/CustomTextInputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/visit_notes">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etNotes"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:lines="4"
                            android:gravity="top" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Submit Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSubmitVisit"
                style="@style/CustomButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/submit_visit"
                app:icon="@drawable/ic_send"
                app:iconTint="@color/white" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
