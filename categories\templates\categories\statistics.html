{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 5px solid;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-card.primary { border-left-color: #007bff; }
.stats-card.success { border-left-color: #28a745; }
.stats-card.warning { border-left-color: #ffc107; }
.stats-card.danger { border-left-color: #dc3545; }
.stats-card.info { border-left-color: #17a2b8; }

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.category-item {
    background: white;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.category-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.category-progress {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
    margin-top: 8px;
}

.category-progress-bar {
    height: 100%;
    background: linear-gradient(45deg, #28a745, #20c997);
    transition: width 0.5s ease;
}

.type-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid;
}

.type-card.type-1 { border-left-color: #dc3545; }
.type-card.type-2 { border-left-color: #007bff; }
.type-card.type-3 { border-left-color: #28a745; }
.type-card.type-4 { border-left-color: #ffc107; }

.empty-category {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 8px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-chart-pie me-2"></i>
                {{ title }}
            </h1>
            <p class="text-muted mb-0">إحصائيات شاملة لاستخدام التصنيفات في النظام</p>
        </div>
        <div>
            <a href="{% url 'categories:dashboard' %}" class="btn btn-primary me-2">
                <i class="fas fa-tags me-2"></i>
                إدارة التصنيفات
            </a>
            <button class="btn btn-outline-primary" onclick="exportStats()">
                <i class="fas fa-download me-2"></i>
                تصدير التقرير
            </button>
        </div>
    </div>

    <!-- Main Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card primary">
                <h2 class="stats-number text-primary">{{ total_categories }}</h2>
                <p class="stats-label">إجمالي التصنيفات</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card success">
                <h2 class="stats-number text-success">{{ users_with_categories }}</h2>
                <p class="stats-label">مستخدمين مع تصنيفات</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card warning">
                <h2 class="stats-number text-warning">{{ users_without_categories }}</h2>
                <p class="stats-label">مستخدمين بدون تصنيفات</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card info">
                <h2 class="stats-number text-info">{{ empty_categories|length }}</h2>
                <p class="stats-label">تصنيفات فارغة</p>
            </div>
        </div>
    </div>

    <!-- Type Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card danger">
                <h2 class="stats-number text-danger">{{ super_categories }}</h2>
                <p class="stats-label">تصنيفات المدير العام</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card primary">
                <h2 class="stats-number text-primary">{{ user_categories }}</h2>
                <p class="stats-label">تصنيفات المستخدمين</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card success">
                <h2 class="stats-number text-success">{{ sales_categories }}</h2>
                <p class="stats-label">تصنيفات المناديب</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card warning">
                <h2 class="stats-number text-warning">{{ client_categories }}</h2>
                <p class="stats-label">تصنيفات العملاء</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Top Categories -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        أكثر التصنيفات استخداماً
                    </h5>
                </div>
                <div class="card-body">
                    {% if top_categories %}
                        {% for item in top_categories %}
                        <div class="category-item">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="fw-bold">{{ item.category.name }}</span>
                                <span class="text-muted">{{ item.users_count }} مستخدم ({{ item.percentage|floatformat:1 }}%)</span>
                            </div>
                            <small class="text-muted">{{ item.category.get_category_type_display }}</small>
                            <div class="category-progress">
                                <div class="category-progress-bar" style="width: {{ item.percentage }}%"></div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد تصنيفات مستخدمة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Type Statistics Details -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>
                        إحصائيات حسب النوع
                    </h5>
                </div>
                <div class="card-body">
                    {% for stat in type_stats %}
                    <div class="type-card type-{{ stat.type }}">
                        <h6 class="mb-2">{{ stat.type_name }}</h6>
                        <div class="row">
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="fw-bold">{{ stat.categories_count }}</div>
                                    <small class="text-muted">تصنيف</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="fw-bold">{{ stat.users_count }}</div>
                                    <small class="text-muted">مستخدم</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="fw-bold">{{ stat.avg_users_per_category|floatformat:1 }}</div>
                                    <small class="text-muted">متوسط/تصنيف</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-pie-chart me-2"></i>
                        توزيع التصنيفات حسب النوع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="typesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        توزيع المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="usersChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Empty Categories -->
    {% if empty_categories %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        التصنيفات الفارغة ({{ empty_categories|length }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for category in empty_categories %}
                        <div class="col-md-4 mb-2">
                            <div class="empty-category">
                                <strong>{{ category.name }}</strong>
                                <br><small class="text-muted">{{ category.get_category_type_display }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // رسم بياني لتوزيع التصنيفات حسب النوع
    const typesCtx = document.getElementById('typesChart').getContext('2d');
    new Chart(typesCtx, {
        type: 'doughnut',
        data: {
            labels: ['المدير العام', 'المستخدمين', 'المناديب', 'العملاء'],
            datasets: [{
                data: [{{ super_categories }}, {{ user_categories }}, {{ sales_categories }}, {{ client_categories }}],
                backgroundColor: [
                    '#dc3545',
                    '#007bff',
                    '#28a745',
                    '#ffc107'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // رسم بياني لتوزيع المستخدمين
    const usersCtx = document.getElementById('usersChart').getContext('2d');
    new Chart(usersCtx, {
        type: 'pie',
        data: {
            labels: ['مع تصنيفات', 'بدون تصنيفات'],
            datasets: [{
                data: [{{ users_with_categories }}, {{ users_without_categories }}],
                backgroundColor: [
                    '#28a745',
                    '#dc3545'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // تأثيرات الحركة للبطاقات
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// تصدير الإحصائيات
function exportStats() {
    alert('سيتم إضافة وظيفة التصدير قريباً');
}
</script>
{% endblock %}
