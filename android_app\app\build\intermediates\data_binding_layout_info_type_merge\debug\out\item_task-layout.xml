<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_task" modulePackage="com.company.fieldsalestracker" filePath="app\src\main\res\layout\item_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/cardView"><Targets><Target id="@+id/cardView" tag="layout/item_task_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="175" endOffset="35"/></Target><Target id="@+id/textTitle" view="TextView"><Expressions/><location startLine="25" startOffset="12" endLine="35" endOffset="41"/></Target><Target id="@+id/textPriority" view="TextView"><Expressions/><location startLine="38" startOffset="12" endLine="48" endOffset="50"/></Target><Target id="@+id/textClient" view="TextView"><Expressions/><location startLine="53" startOffset="8" endLine="63" endOffset="47"/></Target><Target id="@+id/textDescription" view="TextView"><Expressions/><location startLine="66" startOffset="8" endLine="76" endOffset="39"/></Target><Target id="@+id/textStatus" view="TextView"><Expressions/><location startLine="92" startOffset="12" endLine="98" endOffset="42"/></Target><Target id="@+id/textAssignedAt" view="TextView"><Expressions/><location startLine="103" startOffset="8" endLine="113" endOffset="47"/></Target><Target id="@+id/textDueDate" view="TextView"><Expressions/><location startLine="115" startOffset="8" endLine="126" endOffset="39"/></Target><Target id="@+id/textAssignedBy" view="TextView"><Expressions/><location startLine="129" startOffset="8" endLine="139" endOffset="47"/></Target><Target id="@+id/btnAcknowledge" view="Button"><Expressions/><location startLine="148" startOffset="12" endLine="158" endOffset="43"/></Target><Target id="@+id/btnStart" view="Button"><Expressions/><location startLine="160" startOffset="12" endLine="169" endOffset="43"/></Target></Targets></Layout>