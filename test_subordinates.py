#!/usr/bin/env python
"""
اختبار method get_subordinates في User model
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'field_sales_tracker.settings')
django.setup()

from users.models import User

def test_get_subordinates():
    """اختبار method get_subordinates"""
    print("🧪 اختبار method get_subordinates")
    print("=" * 50)
    
    # اختبار جميع المستخدمين
    users = User.objects.all()
    
    for user in users:
        print(f"\n👤 المستخدم: {user.username} ({user.get_role_type_display()})")
        
        try:
            # اختبار get_subordinates
            subordinates = user.get_subordinates()
            print(f"   👥 عدد المرؤوسين: {subordinates.count()}")
            
            for subordinate in subordinates:
                print(f"      - {subordinate.username} ({subordinate.get_role_type_display()})")
            
            # اختبار get_all_subordinates
            all_subordinates = user.get_all_subordinates()
            print(f"   🌐 إجمالي المرؤوسين (مباشر وغير مباشر): {len(all_subordinates)}")
            
            print("   ✅ method get_subordinates يعمل بشكل صحيح")
            
        except Exception as e:
            print(f"   ❌ خطأ في get_subordinates: {e}")

def test_can_manage_user():
    """اختبار method can_manage_user"""
    print("\n🛡️ اختبار method can_manage_user")
    print("=" * 50)
    
    # اختبار المستخدمين المختلفين
    super_manager = User.objects.filter(role_type=1).first()
    user_manager = User.objects.filter(role_type=2).first()
    sales_rep = User.objects.filter(role_type=3).first()
    
    test_cases = [
        ("مدير عام", super_manager),
        ("مدير مستخدمين", user_manager),
        ("مندوب مبيعات", sales_rep),
    ]
    
    for manager_role, manager in test_cases:
        if manager:
            print(f"\n🔍 اختبار {manager_role}: {manager.username}")
            
            # اختبار إدارة مستخدمين مختلفين
            for target_role, target in test_cases:
                if target and target != manager:
                    try:
                        can_manage = manager.can_manage_user(target)
                        print(f"   👥 يمكنه إدارة {target_role} ({target.username}): {can_manage}")
                    except Exception as e:
                        print(f"   ❌ خطأ في can_manage_user: {e}")

def test_direct_managers_system():
    """اختبار نظام المدراء المباشرين"""
    print("\n👔 اختبار نظام المدراء المباشرين")
    print("=" * 50)
    
    # عرض المستخدمين الذين لديهم مدراء مباشرين
    users_with_managers = User.objects.exclude(direct_managers=[])
    print(f"المستخدمين الذين لديهم مدراء مباشرين: {users_with_managers.count()}")
    
    for user in users_with_managers:
        print(f"\n👤 {user.username} ({user.get_role_type_display()}):")
        print(f"   📋 معرفات المدراء المباشرين: {user.direct_managers}")
        
        try:
            # الحصول على المدراء المباشرين
            direct_managers = user.get_direct_managers()
            print(f"   👔 المدراء المباشرين: {direct_managers.count()}")
            
            for manager in direct_managers:
                print(f"      - {manager.username} ({manager.get_role_type_display()})")
                
        except Exception as e:
            print(f"   ❌ خطأ في get_direct_managers: {e}")

def test_hierarchy_consistency():
    """اختبار تناسق النظام الهرمي"""
    print("\n🏗️ اختبار تناسق النظام الهرمي")
    print("=" * 50)
    
    # إحصائيات عامة
    total_users = User.objects.count()
    super_managers = User.objects.filter(role_type=1).count()
    user_managers = User.objects.filter(role_type=2).count()
    sales_reps = User.objects.filter(role_type=3).count()
    
    print(f"📊 إحصائيات المستخدمين:")
    print(f"   إجمالي المستخدمين: {total_users}")
    print(f"   المديرين العامين: {super_managers}")
    print(f"   مديرو المستخدمين: {user_managers}")
    print(f"   مناديب المبيعات: {sales_reps}")
    
    # التحقق من التناسق
    print(f"\n🔍 فحص التناسق:")
    
    # فحص المدراء العامين
    for super_manager in User.objects.filter(role_type=1):
        subordinates_count = super_manager.get_subordinates().count()
        print(f"   👑 {super_manager.username}: {subordinates_count} مرؤوس")
    
    # فحص مديري المستخدمين
    for user_manager in User.objects.filter(role_type=2):
        subordinates_count = user_manager.get_subordinates().count()
        print(f"   👥 {user_manager.username}: {subordinates_count} مرؤوس")
    
    # فحص مناديب المبيعات (يجب ألا يكون لديهم مرؤوسين)
    sales_with_subordinates = 0
    for sales_rep in User.objects.filter(role_type=3):
        subordinates_count = sales_rep.get_subordinates().count()
        if subordinates_count > 0:
            sales_with_subordinates += 1
            print(f"   ⚠️ {sales_rep.username} (مندوب) لديه {subordinates_count} مرؤوس!")
    
    if sales_with_subordinates == 0:
        print("   ✅ جميع مناديب المبيعات لا يديرون أحداً (صحيح)")
    else:
        print(f"   ⚠️ {sales_with_subordinates} مندوب لديهم مرؤوسين (غير طبيعي)")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار methods المرؤوسين والإدارة")
    print("=" * 60)
    
    try:
        # اختبار get_subordinates
        test_get_subordinates()
        
        # اختبار can_manage_user
        test_can_manage_user()
        
        # اختبار نظام المدراء المباشرين
        test_direct_managers_system()
        
        # اختبار تناسق النظام الهرمي
        test_hierarchy_consistency()
        
        print("\n🎉 تم إنجاز جميع الاختبارات بنجاح!")
        print("✅ method get_subordinates يعمل بشكل صحيح")
        print("✅ method can_manage_user يعمل بشكل صحيح")
        print("✅ النظام الهرمي متناسق")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
