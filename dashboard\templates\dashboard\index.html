{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة التحكم - نظام مراقبة المناديب الميدانيين{% endblock %}

{% block page_title %}لوحة التحكم{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item active">لوحة التحكم</li>
{% endblock %}

{% block content %}
<!-- KPI Cards Row -->
<div class="row">
    <!-- Total Visits -->
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3 id="total-visits">{{ total_visits|default:0 }}</h3>
                <p>إجمالي الزيارات</p>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-light" style="width: 70%"></div>
                </div>
                <span class="progress-description">
                    <i class="bi bi-arrow-up text-success"></i> 12% زيادة عن الشهر الماضي
                </span>
            </div>
            <div class="icon">
                <i class="bi bi-geo-alt"></i>
            </div>
            <a href="{% url 'visits:visits_list' %}" class="small-box-footer">
                عرض التفاصيل <i class="bi bi-arrow-left"></i>
            </a>
        </div>
    </div>

    <!-- Verified Visits -->
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3 id="verified-visits">{{ verified_visits|default:0 }}</h3>
                <p>الزيارات المؤكدة</p>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-light" style="width: 85%"></div>
                </div>
                <span class="progress-description">
                    <i class="bi bi-arrow-up text-success"></i> معدل التأكيد: <span id="success-rate">85%</span>
                </span>
            </div>
            <div class="icon">
                <i class="bi bi-check-circle"></i>
            </div>
            <a href="{% url 'visits:visits_list' %}?status=verified" class="small-box-footer">
                عرض المؤكدة <i class="bi bi-arrow-left"></i>
            </a>
        </div>
    </div>

    <!-- Pending Visits -->
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3 id="pending-visits">{{ pending_visits|default:0 }}</h3>
                <p>الزيارات المعلقة</p>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-light" style="width: 15%"></div>
                </div>
                <span class="progress-description">
                    <i class="bi bi-clock text-warning"></i> تحتاج مراجعة فورية
                </span>
            </div>
            <div class="icon">
                <i class="bi bi-hourglass-split"></i>
            </div>
            <a href="{% url 'visits:pending_visits' %}" class="small-box-footer">
                مراجعة المعلقة <i class="bi bi-arrow-left"></i>
            </a>
        </div>
    </div>

    <!-- Active Sales Reps -->
    <div class="col-lg-3 col-6">
        <div class="small-box bg-primary">
            <div class="inner">
                <h3 id="active-reps">{{ active_reps|default:0 }}</h3>
                <p>المناديب النشطين</p>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-light" style="width: 90%"></div>
                </div>
                <span class="progress-description">
                    <i class="bi bi-people text-primary"></i> من إجمالي {{ total_reps|default:0 }} مندوب
                </span>
            </div>
            <div class="icon">
                <i class="bi bi-people"></i>
            </div>
            <a href="{% url 'users:users_list' %}" class="small-box-footer">
                إدارة المناديب <i class="bi bi-arrow-left"></i>
            </a>
        </div>
    </div>
</div>

<!-- Performance Metrics Row -->
<div class="row">
    <!-- Average Distance -->
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box">
            <span class="info-box-icon bg-info elevation-1">
                <i class="bi bi-rulers"></i>
            </span>
            <div class="info-box-content">
                <span class="info-box-text">متوسط المسافة</span>
                <span class="info-box-number" id="avg-distance">{{ avg_distance|default:0 }} متر</span>
                <div class="progress">
                    <div class="progress-bar bg-info" style="width: 70%"></div>
                </div>
                <span class="progress-description">ضمن المعدل المقبول</span>
            </div>
        </div>
    </div>

    <!-- Today's Visits -->
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-danger elevation-1">
                <i class="bi bi-calendar-day"></i>
            </span>
            <div class="info-box-content">
                <span class="info-box-text">زيارات اليوم</span>
                <span class="info-box-number" id="today-visits">{{ today_visits|default:0 }}</span>
                <div class="progress">
                    <div class="progress-bar bg-danger" style="width: 60%"></div>
                </div>
                <span class="progress-description">من هدف 50 زيارة</span>
            </div>
        </div>
    </div>

    <!-- This Week -->
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-success elevation-1">
                <i class="bi bi-calendar-week"></i>
            </span>
            <div class="info-box-content">
                <span class="info-box-text">زيارات الأسبوع</span>
                <span class="info-box-number" id="week-visits">{{ week_visits|default:0 }}</span>
                <div class="progress">
                    <div class="progress-bar bg-success" style="width: 80%"></div>
                </div>
                <span class="progress-description">تقدم ممتاز</span>
            </div>
        </div>
    </div>

    <!-- Total Clients -->
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-warning elevation-1">
                <i class="bi bi-building"></i>
            </span>
            <div class="info-box-content">
                <span class="info-box-text">إجمالي العملاء</span>
                <span class="info-box-number" id="total-clients">{{ total_clients|default:0 }}</span>
                <div class="progress">
                    <div class="progress-bar bg-warning" style="width: 100%"></div>
                </div>
                <span class="progress-description">عملاء نشطين</span>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics Row -->
<div class="row">
    <!-- Visit Trends Chart -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-graph-up me-1"></i>
                    اتجاهات الزيارات (آخر 30 يوم)
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="bi bi-dash"></i>
                    </button>
                    <div class="btn-group">
                        <button type="button" class="btn btn-tool dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="bi bi-gear"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end" role="menu">
                            <a href="#" class="dropdown-item" onclick="updateChart('7days')">آخر 7 أيام</a>
                            <a href="#" class="dropdown-item" onclick="updateChart('30days')">آخر 30 يوم</a>
                            <a href="#" class="dropdown-item" onclick="updateChart('90days')">آخر 3 أشهر</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="position-relative mb-4">
                    <canvas id="visitTrendsChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Visit Status Distribution -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-pie-chart me-1"></i>
                    توزيع حالات الزيارات
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="bi bi-dash"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="visitStatusChart" height="300"></canvas>
                <div class="mt-3">
                    <div class="row">
                        <div class="col-6">
                            <div class="description-block border-end">
                                <span class="description-percentage text-success">
                                    <i class="bi bi-arrow-up"></i> 17%
                                </span>
                                <h5 class="description-header" id="verified-percentage">85%</h5>
                                <span class="description-text">مؤكدة</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="description-block">
                                <span class="description-percentage text-warning">
                                    <i class="bi bi-arrow-down"></i> 3%
                                </span>
                                <h5 class="description-header" id="pending-percentage">15%</h5>
                                <span class="description-text">معلقة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Analytics Row -->
<div class="row">
    <!-- Sales Rep Performance -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-trophy me-1"></i>
                    أداء المناديب (أفضل 10)
                </h3>
                <div class="card-tools">
                    <span class="badge bg-primary" id="performance-period">هذا الشهر</span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المندوب</th>
                                <th>الزيارات</th>
                                <th>المؤكدة</th>
                                <th>معدل النجاح</th>
                                <th>متوسط المسافة</th>
                            </tr>
                        </thead>
                        <tbody id="rep-performance-table">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Geographic Distribution -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-map me-1"></i>
                    التوزيع الجغرافي للزيارات
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="bi bi-dash"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="geographicChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Analytics Row -->
<div class="row">
    <!-- Time-based Analysis -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-clock-history me-1"></i>
                    تحليل الزيارات حسب الوقت
                </h3>
                <div class="card-tools">
                    <ul class="nav nav-pills ms-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="#hourly-chart" data-bs-toggle="tab">ساعي</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#daily-chart" data-bs-toggle="tab">يومي</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#monthly-chart" data-bs-toggle="tab">شهري</a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="tab-content p-0">
                    <div class="chart tab-pane active" id="hourly-chart">
                        <canvas id="hourlyVisitsChart" height="300"></canvas>
                    </div>
                    <div class="chart tab-pane" id="daily-chart">
                        <canvas id="dailyVisitsChart" height="300"></canvas>
                    </div>
                    <div class="chart tab-pane" id="monthly-chart">
                        <canvas id="monthlyVisitsChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics Summary -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-speedometer2 me-1"></i>
                    مؤشرات الأداء الرئيسية
                </h3>
            </div>
            <div class="card-body">
                <!-- Efficiency Score -->
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-success">
                        <i class="bi bi-award"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">نقاط الكفاءة</span>
                        <span class="info-box-number" id="efficiency-score">92</span>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 92%"></div>
                        </div>
                        <span class="progress-description">ممتاز</span>
                    </div>
                </div>

                <!-- Response Time -->
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-info">
                        <i class="bi bi-stopwatch"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">متوسط وقت الاستجابة</span>
                        <span class="info-box-number" id="response-time">2.4</span>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: 80%"></div>
                        </div>
                        <span class="progress-description">ساعة</span>
                    </div>
                </div>

                <!-- Quality Score -->
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-warning">
                        <i class="bi bi-star"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">نقاط الجودة</span>
                        <span class="info-box-number" id="quality-score">4.7</span>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: 94%"></div>
                        </div>
                        <span class="progress-description">من 5</span>
                    </div>
                </div>

                <!-- Productivity Index -->
                <div class="info-box">
                    <span class="info-box-icon bg-danger">
                        <i class="bi bi-graph-up-arrow"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">مؤشر الإنتاجية</span>
                        <span class="info-box-number" id="productivity-index">127%</span>
                        <div class="progress">
                            <div class="progress-bar bg-danger" style="width: 100%"></div>
                        </div>
                        <span class="progress-description">فوق المتوقع</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Reports Row -->
<div class="row">
    <!-- Distance Analysis -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-rulers me-1"></i>
                    تحليل المسافات
                </h3>
                <div class="card-tools">
                    <span class="badge bg-info" id="distance-analysis-period">آخر 30 يوم</span>
                </div>
            </div>
            <div class="card-body">
                <canvas id="distanceAnalysisChart" height="250"></canvas>
                <div class="row mt-3">
                    <div class="col-4 text-center">
                        <div class="description-block">
                            <h5 class="description-header text-success" id="within-range">78%</h5>
                            <span class="description-text">ضمن النطاق</span>
                        </div>
                    </div>
                    <div class="col-4 text-center">
                        <div class="description-block">
                            <h5 class="description-header text-warning" id="acceptable-range">18%</h5>
                            <span class="description-text">مقبول</span>
                        </div>
                    </div>
                    <div class="col-4 text-center">
                        <div class="description-block">
                            <h5 class="description-header text-danger" id="out-of-range">4%</h5>
                            <span class="description-text">خارج النطاق</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Client Activity Heatmap -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-thermometer-half me-1"></i>
                    خريطة نشاط العملاء
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="bi bi-dash"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="clientActivityChart" height="250"></canvas>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="bi bi-info-circle"></i>
                        الألوان الداكنة تشير إلى نشاط أكثر
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Real-time Updates and Alerts -->
<div class="row">
    <!-- Recent Activities -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-activity me-1"></i>
                    الأنشطة الحديثة
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="bi bi-dash"></i>
                    </button>
                    <button type="button" class="btn btn-tool" onclick="refreshActivities()">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الوقت</th>
                                <th>النشاط</th>
                                <th>المستخدم</th>
                                <th>التفاصيل</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody id="recent-activities">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts and Notifications -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-bell me-1"></i>
                    التنبيهات والإشعارات
                </h3>
                <div class="card-tools">
                    <span class="badge bg-danger" id="alerts-count">5</span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush" id="alerts-list">
                    <!-- Critical Alerts -->
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1 text-danger">
                                <i class="bi bi-exclamation-triangle"></i>
                                زيارات معلقة تحتاج مراجعة
                            </h6>
                            <small class="text-danger">عاجل</small>
                        </div>
                        <p class="mb-1">15 زيارة معلقة منذ أكثر من 24 ساعة</p>
                        <small class="text-muted">منذ ساعتين</small>
                    </div>

                    <!-- Warning Alerts -->
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1 text-warning">
                                <i class="bi bi-geo-alt"></i>
                                زيارات خارج النطاق المسموح
                            </h6>
                            <small class="text-warning">تحذير</small>
                        </div>
                        <p class="mb-1">8 زيارات تجاوزت المسافة المسموحة</p>
                        <small class="text-muted">منذ 4 ساعات</small>
                    </div>

                    <!-- Info Alerts -->
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1 text-info">
                                <i class="bi bi-people"></i>
                                مندوب جديد انضم للفريق
                            </h6>
                            <small class="text-info">معلومة</small>
                        </div>
                        <p class="mb-1">خالد أحمد انضم كمندوب مبيعات</p>
                        <small class="text-muted">منذ يوم</small>
                    </div>

                    <!-- Success Alerts -->
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1 text-success">
                                <i class="bi bi-check-circle"></i>
                                تحقيق هدف الشهر
                            </h6>
                            <small class="text-success">إنجاز</small>
                        </div>
                        <p class="mb-1">تم تحقيق 105% من هدف الزيارات الشهري</p>
                        <small class="text-muted">منذ يومين</small>
                    </div>

                    <!-- System Alerts -->
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1 text-secondary">
                                <i class="bi bi-gear"></i>
                                تحديث النظام
                            </h6>
                            <small class="text-secondary">نظام</small>
                        </div>
                        <p class="mb-1">تم تحديث النظام إلى الإصدار 2.1.0</p>
                        <small class="text-muted">منذ 3 أيام</small>
                    </div>
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="#" class="btn btn-sm btn-primary">عرض جميع التنبيهات</a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Row -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-lightning me-1"></i>
                    إجراءات سريعة
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="d-grid">
                            <a href="{% url 'visits:pending_visits' %}" class="btn btn-warning btn-lg">
                                <i class="bi bi-hourglass-split"></i><br>
                                مراجعة الزيارات المعلقة
                                <span class="badge bg-light text-dark ms-2" id="pending-count">15</span>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="d-grid">
                            <a href="{% url 'users:add_user' %}" class="btn btn-success btn-lg">
                                <i class="bi bi-person-plus"></i><br>
                                إضافة مندوب جديد
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="d-grid">
                            <a href="{% url 'clients:add_client' %}" class="btn btn-info btn-lg">
                                <i class="bi bi-building-add"></i><br>
                                إضافة عميل جديد
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="d-grid">
                            <a href="{% url 'users:hierarchical_reports' %}" class="btn btn-primary btn-lg">
                                <i class="bi bi-file-earmark-bar-graph"></i><br>
                                تصدير التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict'

    // Global chart variables
    let visitTrendsChart, visitStatusChart, hourlyVisitsChart, dailyVisitsChart, monthlyVisitsChart;
    let geographicChart, distanceAnalysisChart, clientActivityChart;

    // Initialize all charts
    initializeCharts();

    // Load initial data
    loadDashboardData();

    // Set up auto-refresh
    setInterval(loadDashboardData, 300000); // Refresh every 5 minutes

    function initializeCharts() {
        // Visit Trends Chart (Line Chart)
        const visitTrendsCtx = document.getElementById('visitTrendsChart');
        if (visitTrendsCtx) {
            visitTrendsChart = new Chart(visitTrendsCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'الزيارات المؤكدة',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.4
                    }, {
                        label: 'الزيارات المعلقة',
                        data: [],
                        borderColor: 'rgb(255, 205, 86)',
                        backgroundColor: 'rgba(255, 205, 86, 0.2)',
                        tension: 0.4
                    }, {
                        label: 'الزيارات المرفوضة',
                        data: [],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'اتجاهات الزيارات اليومية'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Visit Status Chart (Doughnut Chart)
        const visitStatusCtx = document.getElementById('visitStatusChart');
        if (visitStatusCtx) {
            visitStatusChart = new Chart(visitStatusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['مؤكدة', 'معلقة', 'مرفوضة'],
                    datasets: [{
                        data: [0, 0, 0],
                        backgroundColor: [
                            'rgb(75, 192, 192)',
                            'rgb(255, 205, 86)',
                            'rgb(255, 99, 132)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Hourly Visits Chart
        const hourlyCtx = document.getElementById('hourlyVisitsChart');
        if (hourlyCtx) {
            hourlyVisitsChart = new Chart(hourlyCtx, {
                type: 'bar',
                data: {
                    labels: ['6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18'],
                    datasets: [{
                        label: 'عدد الزيارات',
                        data: [],
                        backgroundColor: 'rgba(54, 162, 235, 0.8)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'توزيع الزيارات حسب الساعة'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Geographic Distribution Chart
        const geoCtx = document.getElementById('geographicChart');
        if (geoCtx) {
            geographicChart = new Chart(geoCtx, {
                type: 'polarArea',
                data: {
                    labels: ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة'],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 205, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Distance Analysis Chart
        const distanceCtx = document.getElementById('distanceAnalysisChart');
        if (distanceCtx) {
            distanceAnalysisChart = new Chart(distanceCtx, {
                type: 'histogram',
                data: {
                    labels: ['0-50م', '50-100م', '100-200م', '200-500م', '500م+'],
                    datasets: [{
                        label: 'عدد الزيارات',
                        data: [],
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 205, 86, 0.8)',
                            'rgba(255, 159, 64, 0.8)',
                            'rgba(255, 99, 132, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'توزيع المسافات'
                        }
                    }
                }
            });
        }
    }

    // Load dashboard data from API
    async function loadDashboardData() {
        try {
            const response = await fetch('/dashboard/api/advanced-stats/');
            const data = await response.json();

            updateKPIs(data);
            updateCharts(data);
            updateRecentActivities(data);
            updateRepPerformance(data);

        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    // Update KPI cards
    function updateKPIs(data) {
        document.getElementById('total-visits').textContent = data.total_visits || 0;
        document.getElementById('verified-visits').textContent = data.verified_visits || 0;
        document.getElementById('pending-visits').textContent = data.pending_visits || 0;
        document.getElementById('active-reps').textContent = data.active_reps || 0;
        document.getElementById('avg-distance').textContent = (data.avg_distance || 0) + ' متر';
        document.getElementById('today-visits').textContent = data.today_visits || 0;
        document.getElementById('week-visits').textContent = data.week_visits || 0;
        document.getElementById('total-clients').textContent = data.total_clients || 0;

        // Update success rate
        const successRate = data.total_visits > 0 ?
            Math.round((data.verified_visits / data.total_visits) * 100) : 0;
        document.getElementById('success-rate').textContent = successRate + '%';

        // Update percentages
        const totalVisits = data.total_visits || 1;
        document.getElementById('verified-percentage').textContent =
            Math.round((data.verified_visits / totalVisits) * 100) + '%';
        document.getElementById('pending-percentage').textContent =
            Math.round((data.pending_visits / totalVisits) * 100) + '%';
    }

    // Update charts with new data
    function updateCharts(data) {
        // Update visit trends chart
        if (visitTrendsChart && data.daily_trends) {
            visitTrendsChart.data.labels = data.daily_trends.labels;
            visitTrendsChart.data.datasets[0].data = data.daily_trends.verified;
            visitTrendsChart.data.datasets[1].data = data.daily_trends.pending;
            visitTrendsChart.data.datasets[2].data = data.daily_trends.rejected;
            visitTrendsChart.update();
        }

        // Update visit status chart
        if (visitStatusChart && data.status_breakdown) {
            visitStatusChart.data.datasets[0].data = [
                data.status_breakdown.verified || 0,
                data.status_breakdown.pending || 0,
                data.status_breakdown.rejected || 0
            ];
            visitStatusChart.update();
        }

        // Update hourly chart
        if (hourlyVisitsChart && data.hourly_distribution) {
            hourlyVisitsChart.data.datasets[0].data = data.hourly_distribution;
            hourlyVisitsChart.update();
        }

        // Update geographic chart
        if (geographicChart && data.geographic_distribution) {
            geographicChart.data.datasets[0].data = data.geographic_distribution;
            geographicChart.update();
        }

        // Update distance analysis chart
        if (distanceAnalysisChart && data.distance_distribution) {
            distanceAnalysisChart.data.datasets[0].data = data.distance_distribution;
            distanceAnalysisChart.update();
        }
    }

    // Update recent activities table
    function updateRecentActivities(data) {
        const tbody = document.getElementById('recent-activities');
        if (!tbody || !data.recent_activities) return;

        tbody.innerHTML = '';
        data.recent_activities.forEach(activity => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><small>${formatTime(activity.timestamp)}</small></td>
                <td>
                    <i class="bi ${getActivityIcon(activity.type)}"></i>
                    ${activity.action}
                </td>
                <td>${activity.user}</td>
                <td>${activity.details}</td>
                <td>
                    <span class="badge bg-${getStatusColor(activity.status)}">
                        ${activity.status}
                    </span>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Update sales rep performance table
    function updateRepPerformance(data) {
        const tbody = document.getElementById('rep-performance-table');
        if (!tbody || !data.rep_performance) return;

        tbody.innerHTML = '';
        data.rep_performance.slice(0, 10).forEach((rep, index) => {
            const row = document.createElement('tr');
            const successRate = rep.total_visits > 0 ?
                Math.round((rep.verified_visits / rep.total_visits) * 100) : 0;

            row.innerHTML = `
                <td>
                    <div class="d-flex align-items-center">
                        <div class="me-2">
                            <span class="badge bg-${index < 3 ? 'success' : 'secondary'}">${index + 1}</span>
                        </div>
                        <div>
                            <strong>${rep.name}</strong>
                        </div>
                    </div>
                </td>
                <td><span class="badge bg-primary">${rep.total_visits}</span></td>
                <td><span class="badge bg-success">${rep.verified_visits}</span></td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-${successRate >= 80 ? 'success' : successRate >= 60 ? 'warning' : 'danger'}"
                             style="width: ${successRate}%">
                            ${successRate}%
                        </div>
                    </div>
                </td>
                <td><small>${Math.round(rep.avg_distance || 0)} متر</small></td>
            `;
            tbody.appendChild(row);
        });
    }

    // Utility functions
    function formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return 'الآن';
        if (diff < 3600000) return Math.floor(diff / 60000) + ' دقيقة';
        if (diff < 86400000) return Math.floor(diff / 3600000) + ' ساعة';
        return Math.floor(diff / 86400000) + ' يوم';
    }

    function getActivityIcon(type) {
        const icons = {
            'visit_added': 'bi-geo-alt',
            'visit_verified': 'bi-check-circle',
            'visit_rejected': 'bi-x-circle',
            'user_added': 'bi-person-plus',
            'client_added': 'bi-building-add'
        };
        return icons[type] || 'bi-activity';
    }

    function getStatusColor(status) {
        const colors = {
            'verified': 'success',
            'pending': 'warning',
            'rejected': 'danger',
            'completed': 'success',
            'active': 'primary'
        };
        return colors[status] || 'secondary';
    }

    // Chart update functions
    window.updateChart = function(period) {
        // Update chart based on selected period
        loadDashboardData();
    };

    window.refreshActivities = function() {
        loadDashboardData();
    };

    // Card widget functionality
    document.querySelectorAll('[data-card-widget="collapse"]').forEach(button => {
        button.addEventListener('click', function() {
            const card = this.closest('.card');
            const cardBody = card.querySelector('.card-body');

            if (cardBody.style.display === 'none') {
                cardBody.style.display = 'block';
                this.innerHTML = '<i class="bi bi-dash"></i>';
            } else {
                cardBody.style.display = 'none';
                this.innerHTML = '<i class="bi bi-plus"></i>';
            }
        });
    });

    console.log('Advanced dashboard initialized successfully');
});
</script>
{% endblock %}
