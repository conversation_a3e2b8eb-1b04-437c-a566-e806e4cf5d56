-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:114:9-122:20
	android:grantUriPermissions
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:118:13-47
	android:authorities
		INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:116:13-64
	android:exported
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:117:13-37
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:115:13-62
manifest
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:2:1-131:12
INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:2:1-131:12
INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:2:1-131:12
INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:2:1-131:12
MERGED from [androidx.databinding:viewbinding:8.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebcfeab8dd95b3e6908255e0b4963053\transformed\jetified-viewbinding-8.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8a82cff2f1ab97063b798098bcf5a0a\transformed\navigation-common-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b8d1236ccc7564ddfca9ff463affbdc\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\8353293c32e7ef893c776d8beea4fcd8\transformed\navigation-fragment-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed709288d23a486d0424a1f6be3fc698\transformed\navigation-ui-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43bdbc0b1ff399fa79cb359f37c50201\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\17e9e82599c380c0803d13247222e61b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\cadbc62921d7c0c332c3cf76cee37b29\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:2:1-19:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\427e0b1bfe4c3f7afe4eed74d903206a\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ce97e1d7afd464f10c84d8596a83f4ee\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\040c0719a8c6659c6357b7fa4eb4747b\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\e06bb3f60dc069d265ca7ea605337944\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5f6d62587af06d33f6018db5385ab72\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ec94684057faf25ef2923fb8325b906\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d5a78c10bb960df73d318627b37a879\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b024e65b5c5ed406eed20fb23f97dec\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\c1d99ae8cacfe21a5401fc1b0bd05831\transformed\jetified-glide-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\042c34128901b68c1f5bb82f76ca90ba\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f59c18b68069eb97966bfed2835db495\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e4dc34d830da05d68a52e9f6f0da317\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2268c738b294d6d0c9606f1465299bff\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\73403e34556b2f7de2ab3466d11b77ae\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:17:1-44:12
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d8815f33a71d77a2e1e2a841b403ff3\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.mlkit:barcode-scanning:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ec10575b7e5a955f9bbf783d4a53c5cb\transformed\jetified-barcode-scanning-17.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8f7b99313d00ffa0e5957f14e1eaa6b\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\587c574719e0f06ae73b0e5dd1612c53\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\46d39d4adf2b661b4b16c950129d1e12\transformed\jetified-vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\38cf3516018706f2afc8af3d110ee0bb\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88fe9f048cf6eb09539000df0730205\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\cec7d9aacc5a291433419b4c781eccf7\transformed\fragment-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7fc7441777454cca68fc7203288c6f8e\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6da8102af9d5ea5e735dc025a80e303e\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\33b70541a17216f4c07d3991e0cd952e\transformed\jetified-activity-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\de2c2128d801ad9e4ba788dd516a069c\transformed\jetified-activity-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\3a7e89d43ddf32f3c88466201a2251eb\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7190447740cdd28f93dc634a8d0ee430\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7cfad0b008b01f927d8c910d1c8ce27\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\4b75c8fae9c0ebb6214f7c15b04b2413\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6505d3cac7a082f94facaa079603e435\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\03b447b94ec627a11b45a9b03445bd36\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a52b08d3537b6bb87dbe44e3d082ccb\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\cab17ae5a37c6384cd8b8fcac641f2ed\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\1b6547633fa38404c576c9bed4b09268\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\14fa0875fe10918c8df7544ad0da4270\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\616a8b0999e34fc259308883e3f26266\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\e561ab605087b9acf89112cb497627b6\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\a6f5b1b7d87c6c7f8971ba6beb3f681e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c6f27a4a1a1313ca6912eb852d3ed5cd\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d9b4798596e32c5d4636120b0d5cb66f\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8baa007051162a17a66be4cba9e94a4f\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8a5c0f2708dc2af5313156139aa2976\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5105026adaf3195736ecd99468e38dc6\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\c33252708c9e6948a1ecfdd0e2ff6218\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7746c533f3a18984276561eabd5e611\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9aee89c7ff8d8387b412bb14ae45e7ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3da6ffd5a6ca419218f982dc6c9984aa\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\efb78d9812bcf29ede3f891e16dc3953\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfed8e7915e6a0e0c6b7aa14a5f6d470\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f8111fcdf22859f92aa276b965636b64\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8da7d1ae8edc5bf80920aa352488a800\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0aff00af094c93927323c803eeb05fa\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\265d54a24d79eb1615a6b6530680ff6a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0362196cae6eab7d3aaa0d0af0c07a85\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a751e07659ba7309f7de6627062c5c4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\45942d32e9a99b258da5ab3e9b60b978\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\f48eb3581a608b734652029f7baa922c\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.CAMERA
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:8:5-65
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:8:22-62
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:9:5-79
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:13:5-14:38
	android:maxSdkVersion
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:14:9-35
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:13:22-77
uses-feature#android.hardware.camera
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:17:5-19:35
	android:required
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:19:9-32
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:18:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:20:5-22:36
	android:required
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:22:9-33
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:21:9-57
uses-feature#android.hardware.location
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:25:5-27:35
	android:required
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:27:9-32
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:26:9-49
uses-feature#android.hardware.location.gps
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:30:9-33
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:29:9-53
application
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:32:5-129:19
INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:32:5-129:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43bdbc0b1ff399fa79cb359f37c50201\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43bdbc0b1ff399fa79cb359f37c50201\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\17e9e82599c380c0803d13247222e61b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\17e9e82599c380c0803d13247222e61b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:11:5-17:19
MERGED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:11:5-17:19
MERGED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\e06bb3f60dc069d265ca7ea605337944\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\e06bb3f60dc069d265ca7ea605337944\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\c1d99ae8cacfe21a5401fc1b0bd05831\transformed\jetified-glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\c1d99ae8cacfe21a5401fc1b0bd05831\transformed\jetified-glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8f7b99313d00ffa0e5957f14e1eaa6b\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8f7b99313d00ffa0e5957f14e1eaa6b\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\38cf3516018706f2afc8af3d110ee0bb\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\38cf3516018706f2afc8af3d110ee0bb\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88fe9f048cf6eb09539000df0730205\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88fe9f048cf6eb09539000df0730205\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7746c533f3a18984276561eabd5e611\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7746c533f3a18984276561eabd5e611\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9aee89c7ff8d8387b412bb14ae45e7ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9aee89c7ff8d8387b412bb14ae45e7ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\efb78d9812bcf29ede3f891e16dc3953\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\efb78d9812bcf29ede3f891e16dc3953\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\f48eb3581a608b734652029f7baa922c\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\f48eb3581a608b734652029f7baa922c\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml
	tools:ignore
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:44:9-42
	android:roundIcon
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:38:9-54
	android:icon
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:36:9-43
	android:networkSecurityConfig
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:42:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:39:9-35
	android:label
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:37:9-41
	android:fullBackupContent
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:35:9-54
	android:debuggable
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:43:9-35
	tools:targetApi
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:45:9-29
	android:allowBackup
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:33:9-35
	android:theme
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:40:9-55
	android:dataExtractionRules
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:34:9-65
	android:usesCleartextTraffic
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:41:9-44
activity#com.company.fieldsalestracker.SplashActivity
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:48:9-57:20
	android:label
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:51:13-45
	android:exported
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:52:13-66
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:49:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:53:13-56:29
action#android.intent.action.MAIN
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:54:17-69
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:54:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:55:17-77
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:55:27-74
activity#com.company.fieldsalestracker.MainActivity
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:60:9-64:62
	android:label
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:63:13-45
	android:exported
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:62:13-37
	android:theme
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:64:13-59
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:61:13-41
activity#com.company.fieldsalestracker.LoginActivity
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:67:9-70:74
	android:exported
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:69:13-37
	android:theme
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:70:13-71
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:68:13-42
activity#com.company.fieldsalestracker.MyVisitsActivity
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:73:9-77:74
	android:parentActivityName
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:76:13-55
	android:exported
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:75:13-37
	android:theme
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:77:13-71
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:74:13-45
activity#com.company.fieldsalestracker.TasksActivity
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:80:9-84:74
	android:parentActivityName
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:83:13-55
	android:exported
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:82:13-37
	android:theme
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:84:13-71
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:81:13-42
activity#com.company.fieldsalestracker.TaskDetailsActivity
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:87:9-91:74
	android:parentActivityName
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:90:13-56
	android:exported
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:89:13-37
	android:theme
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:91:13-71
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:88:13-48
activity#com.company.fieldsalestracker.BarcodeScannerActivity
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:94:9-98:74
	android:screenOrientation
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:97:13-49
	android:exported
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:96:13-37
	android:theme
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:98:13-71
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:95:13-51
activity#com.company.fieldsalestracker.VisitDetailsActivity
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:101:9-105:74
	android:parentActivityName
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:104:13-55
	android:exported
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:103:13-37
	android:theme
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:105:13-71
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:102:13-49
activity#com.company.fieldsalestracker.ClientListActivity
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:108:9-111:58
	android:parentActivityName
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:111:13-55
	android:exported
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:110:13-37
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:109:13-47
meta-data#com.google.mlkit.vision.DEPENDENCIES
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:125:9-127:39
	android:value
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:127:13-36
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:126:13-64
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:119:13-121:54
	android:resource
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:121:17-51
	android:name
		ADDED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:120:17-67
uses-sdk
INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml
INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebcfeab8dd95b3e6908255e0b4963053\transformed\jetified-viewbinding-8.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebcfeab8dd95b3e6908255e0b4963053\transformed\jetified-viewbinding-8.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8a82cff2f1ab97063b798098bcf5a0a\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8a82cff2f1ab97063b798098bcf5a0a\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b8d1236ccc7564ddfca9ff463affbdc\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b8d1236ccc7564ddfca9ff463affbdc\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\8353293c32e7ef893c776d8beea4fcd8\transformed\navigation-fragment-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\8353293c32e7ef893c776d8beea4fcd8\transformed\navigation-fragment-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed709288d23a486d0424a1f6be3fc698\transformed\navigation-ui-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed709288d23a486d0424a1f6be3fc698\transformed\navigation-ui-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43bdbc0b1ff399fa79cb359f37c50201\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43bdbc0b1ff399fa79cb359f37c50201\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\17e9e82599c380c0803d13247222e61b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\17e9e82599c380c0803d13247222e61b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\cadbc62921d7c0c332c3cf76cee37b29\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\cadbc62921d7c0c332c3cf76cee37b29\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\427e0b1bfe4c3f7afe4eed74d903206a\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\427e0b1bfe4c3f7afe4eed74d903206a\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ce97e1d7afd464f10c84d8596a83f4ee\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ce97e1d7afd464f10c84d8596a83f4ee\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\040c0719a8c6659c6357b7fa4eb4747b\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\040c0719a8c6659c6357b7fa4eb4747b\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\e06bb3f60dc069d265ca7ea605337944\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\e06bb3f60dc069d265ca7ea605337944\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5f6d62587af06d33f6018db5385ab72\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5f6d62587af06d33f6018db5385ab72\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ec94684057faf25ef2923fb8325b906\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ec94684057faf25ef2923fb8325b906\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d5a78c10bb960df73d318627b37a879\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d5a78c10bb960df73d318627b37a879\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b024e65b5c5ed406eed20fb23f97dec\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b024e65b5c5ed406eed20fb23f97dec\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\c1d99ae8cacfe21a5401fc1b0bd05831\transformed\jetified-glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\c1d99ae8cacfe21a5401fc1b0bd05831\transformed\jetified-glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\042c34128901b68c1f5bb82f76ca90ba\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\042c34128901b68c1f5bb82f76ca90ba\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f59c18b68069eb97966bfed2835db495\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f59c18b68069eb97966bfed2835db495\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e4dc34d830da05d68a52e9f6f0da317\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e4dc34d830da05d68a52e9f6f0da317\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2268c738b294d6d0c9606f1465299bff\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2268c738b294d6d0c9606f1465299bff\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\73403e34556b2f7de2ab3466d11b77ae\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\73403e34556b2f7de2ab3466d11b77ae\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d8815f33a71d77a2e1e2a841b403ff3\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d8815f33a71d77a2e1e2a841b403ff3\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ec10575b7e5a955f9bbf783d4a53c5cb\transformed\jetified-barcode-scanning-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ec10575b7e5a955f9bbf783d4a53c5cb\transformed\jetified-barcode-scanning-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8f7b99313d00ffa0e5957f14e1eaa6b\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8f7b99313d00ffa0e5957f14e1eaa6b\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\587c574719e0f06ae73b0e5dd1612c53\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\587c574719e0f06ae73b0e5dd1612c53\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\46d39d4adf2b661b4b16c950129d1e12\transformed\jetified-vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\46d39d4adf2b661b4b16c950129d1e12\transformed\jetified-vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\38cf3516018706f2afc8af3d110ee0bb\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\38cf3516018706f2afc8af3d110ee0bb\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88fe9f048cf6eb09539000df0730205\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88fe9f048cf6eb09539000df0730205\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\cec7d9aacc5a291433419b4c781eccf7\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\cec7d9aacc5a291433419b4c781eccf7\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7fc7441777454cca68fc7203288c6f8e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7fc7441777454cca68fc7203288c6f8e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6da8102af9d5ea5e735dc025a80e303e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6da8102af9d5ea5e735dc025a80e303e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\33b70541a17216f4c07d3991e0cd952e\transformed\jetified-activity-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\33b70541a17216f4c07d3991e0cd952e\transformed\jetified-activity-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\de2c2128d801ad9e4ba788dd516a069c\transformed\jetified-activity-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\de2c2128d801ad9e4ba788dd516a069c\transformed\jetified-activity-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\3a7e89d43ddf32f3c88466201a2251eb\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\3a7e89d43ddf32f3c88466201a2251eb\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7190447740cdd28f93dc634a8d0ee430\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7190447740cdd28f93dc634a8d0ee430\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7cfad0b008b01f927d8c910d1c8ce27\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7cfad0b008b01f927d8c910d1c8ce27\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\4b75c8fae9c0ebb6214f7c15b04b2413\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\4b75c8fae9c0ebb6214f7c15b04b2413\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6505d3cac7a082f94facaa079603e435\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6505d3cac7a082f94facaa079603e435\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\03b447b94ec627a11b45a9b03445bd36\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\03b447b94ec627a11b45a9b03445bd36\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a52b08d3537b6bb87dbe44e3d082ccb\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a52b08d3537b6bb87dbe44e3d082ccb\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\cab17ae5a37c6384cd8b8fcac641f2ed\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\cab17ae5a37c6384cd8b8fcac641f2ed\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\1b6547633fa38404c576c9bed4b09268\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\1b6547633fa38404c576c9bed4b09268\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\14fa0875fe10918c8df7544ad0da4270\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\14fa0875fe10918c8df7544ad0da4270\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\616a8b0999e34fc259308883e3f26266\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\616a8b0999e34fc259308883e3f26266\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\e561ab605087b9acf89112cb497627b6\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\e561ab605087b9acf89112cb497627b6\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\a6f5b1b7d87c6c7f8971ba6beb3f681e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\a6f5b1b7d87c6c7f8971ba6beb3f681e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c6f27a4a1a1313ca6912eb852d3ed5cd\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c6f27a4a1a1313ca6912eb852d3ed5cd\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d9b4798596e32c5d4636120b0d5cb66f\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d9b4798596e32c5d4636120b0d5cb66f\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8baa007051162a17a66be4cba9e94a4f\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8baa007051162a17a66be4cba9e94a4f\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8a5c0f2708dc2af5313156139aa2976\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8a5c0f2708dc2af5313156139aa2976\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5105026adaf3195736ecd99468e38dc6\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5105026adaf3195736ecd99468e38dc6\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\c33252708c9e6948a1ecfdd0e2ff6218\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\c33252708c9e6948a1ecfdd0e2ff6218\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7746c533f3a18984276561eabd5e611\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7746c533f3a18984276561eabd5e611\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9aee89c7ff8d8387b412bb14ae45e7ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9aee89c7ff8d8387b412bb14ae45e7ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3da6ffd5a6ca419218f982dc6c9984aa\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3da6ffd5a6ca419218f982dc6c9984aa\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\efb78d9812bcf29ede3f891e16dc3953\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\efb78d9812bcf29ede3f891e16dc3953\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfed8e7915e6a0e0c6b7aa14a5f6d470\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfed8e7915e6a0e0c6b7aa14a5f6d470\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f8111fcdf22859f92aa276b965636b64\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f8111fcdf22859f92aa276b965636b64\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8da7d1ae8edc5bf80920aa352488a800\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8da7d1ae8edc5bf80920aa352488a800\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0aff00af094c93927323c803eeb05fa\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0aff00af094c93927323c803eeb05fa\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\265d54a24d79eb1615a6b6530680ff6a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\265d54a24d79eb1615a6b6530680ff6a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0362196cae6eab7d3aaa0d0af0c07a85\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0362196cae6eab7d3aaa0d0af0c07a85\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a751e07659ba7309f7de6627062c5c4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a751e07659ba7309f7de6627062c5c4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\45942d32e9a99b258da5ab3e9b60b978\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\45942d32e9a99b258da5ab3e9b60b978\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\f48eb3581a608b734652029f7baa922c\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\f48eb3581a608b734652029f7baa922c\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml
activity#pub.devrel.easypermissions.AppSettingsDialogHolderActivity
ADDED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:12:9-16:66
	android:label
		ADDED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:15:13-29
	android:exported
		ADDED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:14:13-37
	android:theme
		ADDED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:16:13-63
	android:name
		ADDED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:13:13-86
queries
ADDED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:22:5-26:15
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:30:5-34:15
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\e06bb3f60dc069d265ca7ea605337944\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\e06bb3f60dc069d265ca7ea605337944\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9aee89c7ff8d8387b412bb14ae45e7ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9aee89c7ff8d8387b412bb14ae45e7ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:40:13-50
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88fe9f048cf6eb09539000df0730205\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88fe9f048cf6eb09539000df0730205\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88fe9f048cf6eb09539000df0730205\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.company.fieldsalestracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.company.fieldsalestracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
