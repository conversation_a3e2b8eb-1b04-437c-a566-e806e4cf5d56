// Generated by view binder compiler. Do not edit!
package com.company.fieldsalestracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.company.fieldsalestracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTaskDetailsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnAcknowledge;

  @NonNull
  public final Button btnOpenScanner;

  @NonNull
  public final Button btnStart;

  @NonNull
  public final CardView cardPriority;

  @NonNull
  public final CardView cardStatus;

  @NonNull
  public final TextView tvAssignedAt;

  @NonNull
  public final TextView tvAssignedBy;

  @NonNull
  public final TextView tvClientAddress;

  @NonNull
  public final TextView tvClientName;

  @NonNull
  public final TextView tvDueDate;

  @NonNull
  public final TextView tvPriority;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvTaskDescription;

  @NonNull
  public final TextView tvTaskTitle;

  private ActivityTaskDetailsBinding(@NonNull ScrollView rootView, @NonNull Button btnAcknowledge,
      @NonNull Button btnOpenScanner, @NonNull Button btnStart, @NonNull CardView cardPriority,
      @NonNull CardView cardStatus, @NonNull TextView tvAssignedAt, @NonNull TextView tvAssignedBy,
      @NonNull TextView tvClientAddress, @NonNull TextView tvClientName,
      @NonNull TextView tvDueDate, @NonNull TextView tvPriority, @NonNull TextView tvStatus,
      @NonNull TextView tvTaskDescription, @NonNull TextView tvTaskTitle) {
    this.rootView = rootView;
    this.btnAcknowledge = btnAcknowledge;
    this.btnOpenScanner = btnOpenScanner;
    this.btnStart = btnStart;
    this.cardPriority = cardPriority;
    this.cardStatus = cardStatus;
    this.tvAssignedAt = tvAssignedAt;
    this.tvAssignedBy = tvAssignedBy;
    this.tvClientAddress = tvClientAddress;
    this.tvClientName = tvClientName;
    this.tvDueDate = tvDueDate;
    this.tvPriority = tvPriority;
    this.tvStatus = tvStatus;
    this.tvTaskDescription = tvTaskDescription;
    this.tvTaskTitle = tvTaskTitle;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTaskDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTaskDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_task_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTaskDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAcknowledge;
      Button btnAcknowledge = ViewBindings.findChildViewById(rootView, id);
      if (btnAcknowledge == null) {
        break missingId;
      }

      id = R.id.btnOpenScanner;
      Button btnOpenScanner = ViewBindings.findChildViewById(rootView, id);
      if (btnOpenScanner == null) {
        break missingId;
      }

      id = R.id.btnStart;
      Button btnStart = ViewBindings.findChildViewById(rootView, id);
      if (btnStart == null) {
        break missingId;
      }

      id = R.id.cardPriority;
      CardView cardPriority = ViewBindings.findChildViewById(rootView, id);
      if (cardPriority == null) {
        break missingId;
      }

      id = R.id.cardStatus;
      CardView cardStatus = ViewBindings.findChildViewById(rootView, id);
      if (cardStatus == null) {
        break missingId;
      }

      id = R.id.tvAssignedAt;
      TextView tvAssignedAt = ViewBindings.findChildViewById(rootView, id);
      if (tvAssignedAt == null) {
        break missingId;
      }

      id = R.id.tvAssignedBy;
      TextView tvAssignedBy = ViewBindings.findChildViewById(rootView, id);
      if (tvAssignedBy == null) {
        break missingId;
      }

      id = R.id.tvClientAddress;
      TextView tvClientAddress = ViewBindings.findChildViewById(rootView, id);
      if (tvClientAddress == null) {
        break missingId;
      }

      id = R.id.tvClientName;
      TextView tvClientName = ViewBindings.findChildViewById(rootView, id);
      if (tvClientName == null) {
        break missingId;
      }

      id = R.id.tvDueDate;
      TextView tvDueDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDueDate == null) {
        break missingId;
      }

      id = R.id.tvPriority;
      TextView tvPriority = ViewBindings.findChildViewById(rootView, id);
      if (tvPriority == null) {
        break missingId;
      }

      id = R.id.tvStatus;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tvTaskDescription;
      TextView tvTaskDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskDescription == null) {
        break missingId;
      }

      id = R.id.tvTaskTitle;
      TextView tvTaskTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskTitle == null) {
        break missingId;
      }

      return new ActivityTaskDetailsBinding((ScrollView) rootView, btnAcknowledge, btnOpenScanner,
          btnStart, cardPriority, cardStatus, tvAssignedAt, tvAssignedBy, tvClientAddress,
          tvClientName, tvDueDate, tvPriority, tvStatus, tvTaskDescription, tvTaskTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
