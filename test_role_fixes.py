#!/usr/bin/env python
"""
اختبار إصلاحات مراجع user.role في النظام
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'field_sales_tracker.settings')
django.setup()

from users.models import User
from users.permissions import HierarchicalPermission
from django.test import RequestFactory
from django.contrib.auth.models import AnonymousUser

def test_user_attributes():
    """اختبار خصائص المستخدم الجديدة"""
    print("🧪 اختبار خصائص المستخدم")
    print("=" * 50)
    
    # اختبار المستخدمين المختلفين
    users = User.objects.all()[:5]
    
    for user in users:
        print(f"\n👤 المستخدم: {user.username}")
        print(f"   🏷️ نوع الدور: {user.role_type} ({user.get_role_type_display()})")
        
        # اختبار الخصائص الجديدة
        try:
            print(f"   🔑 مدير عام: {user.is_super_manager}")
            print(f"   👥 مدير مستخدمين: {user.is_user_manager}")
            print(f"   🏃 مندوب مبيعات: {user.is_sales_rep}")
            print("   ✅ جميع الخصائص تعمل بشكل صحيح")
        except AttributeError as e:
            print(f"   ❌ خطأ في الخصائص: {e}")

def test_permissions():
    """اختبار نظام الصلاحيات"""
    print("\n🛡️ اختبار نظام الصلاحيات")
    print("=" * 50)
    
    # اختبار المستخدمين المختلفين
    super_manager = User.objects.filter(role_type=1).first()
    user_manager = User.objects.filter(role_type=2).first()
    sales_rep = User.objects.filter(role_type=3).first()
    
    users_to_test = [
        ("مدير عام", super_manager),
        ("مدير مستخدمين", user_manager),
        ("مندوب مبيعات", sales_rep),
    ]
    
    for role_name, user in users_to_test:
        if user:
            print(f"\n🔍 اختبار {role_name}: {user.username}")
            
            try:
                # اختبار can_manage_user
                if super_manager and user != super_manager:
                    can_manage = HierarchicalPermission.can_manage_user(super_manager, user)
                    print(f"   👥 يمكن للمدير العام إدارته: {can_manage}")
                
                # اختبار can_view_visits
                can_view_visits = HierarchicalPermission.can_view_visits(user)
                print(f"   👁️ يمكنه رؤية الزيارات: {can_view_visits}")
                
                print("   ✅ اختبار الصلاحيات نجح")
                
            except Exception as e:
                print(f"   ❌ خطأ في الصلاحيات: {e}")

def test_anonymous_user():
    """اختبار المستخدم المجهول"""
    print("\n👻 اختبار المستخدم المجهول")
    print("=" * 50)
    
    anonymous = AnonymousUser()
    
    try:
        # اختبار الصلاحيات مع المستخدم المجهول
        can_view = HierarchicalPermission.can_view_visits(anonymous)
        print(f"   👁️ يمكن للمجهول رؤية الزيارات: {can_view}")
        
        if User.objects.exists():
            target_user = User.objects.first()
            can_manage = HierarchicalPermission.can_manage_user(anonymous, target_user)
            print(f"   👥 يمكن للمجهول إدارة المستخدمين: {can_manage}")
        
        print("   ✅ اختبار المستخدم المجهول نجح")
        
    except Exception as e:
        print(f"   ❌ خطأ مع المستخدم المجهول: {e}")

def test_template_compatibility():
    """اختبار توافق Templates"""
    print("\n🎨 اختبار توافق Templates")
    print("=" * 50)
    
    users = User.objects.all()[:3]
    
    for user in users:
        print(f"\n👤 المستخدم: {user.username}")
        
        try:
            # اختبار الخصائص المستخدمة في Templates
            role_display = user.get_role_type_display()
            print(f"   🏷️ عرض الدور: {role_display}")
            
            # اختبار الشروط المستخدمة في Templates
            if user.role_type == 3:
                print("   🏃 هذا مندوب مبيعات (role_type == 3)")
            else:
                print("   👥 هذا مدير أو مستخدم")
            
            # اختبار الصلاحيات في Templates
            if user.is_super_manager or user.is_user_manager:
                print("   🔑 لديه صلاحيات إدارية")
            else:
                print("   👤 مستخدم عادي")
            
            print("   ✅ اختبار Template نجح")
            
        except Exception as e:
            print(f"   ❌ خطأ في Template: {e}")

def test_database_integrity():
    """اختبار سلامة قاعدة البيانات"""
    print("\n🗄️ اختبار سلامة قاعدة البيانات")
    print("=" * 50)
    
    # إحصائيات المستخدمين
    total_users = User.objects.count()
    super_managers = User.objects.filter(role_type=1).count()
    user_managers = User.objects.filter(role_type=2).count()
    sales_reps = User.objects.filter(role_type=3).count()
    
    print(f"📊 إحصائيات المستخدمين:")
    print(f"   إجمالي المستخدمين: {total_users}")
    print(f"   المديرين العامين: {super_managers}")
    print(f"   مديرو المستخدمين: {user_managers}")
    print(f"   مناديب المبيعات: {sales_reps}")
    
    # التحقق من البيانات
    users_with_invalid_role = User.objects.exclude(role_type__in=[1, 2, 3]).count()
    print(f"   مستخدمين بأدوار غير صحيحة: {users_with_invalid_role}")
    
    if users_with_invalid_role == 0:
        print("   ✅ جميع المستخدمين لديهم أدوار صحيحة")
    else:
        print("   ⚠️ يوجد مستخدمين بأدوار غير صحيحة")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاحات user.role")
    print("=" * 60)
    
    try:
        # اختبار خصائص المستخدم
        test_user_attributes()
        
        # اختبار نظام الصلاحيات
        test_permissions()
        
        # اختبار المستخدم المجهول
        test_anonymous_user()
        
        # اختبار توافق Templates
        test_template_compatibility()
        
        # اختبار سلامة قاعدة البيانات
        test_database_integrity()
        
        print("\n🎉 تم إنجاز جميع الاختبارات بنجاح!")
        print("✅ جميع مراجع user.role تم إصلاحها")
        print("✅ النظام يعمل بشكل صحيح")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
