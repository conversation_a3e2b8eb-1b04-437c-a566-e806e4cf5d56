[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-mergeDebugResources-53:\\layout\\item_visit.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\item_visit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-mergeDebugResources-53:\\layout\\item_task.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\item_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-mergeDebugResources-53:\\layout\\activity_barcode_scanner.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_barcode_scanner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-mergeDebugResources-53:\\layout\\activity_visit_details.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_visit_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-mergeDebugResources-53:\\layout\\activity_splash.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_splash.xml"}, {"merged": "com.company.fieldsalestracker.app-mergeDebugResources-53:/layout/activity_enhanced_tasks.xml", "source": "com.company.fieldsalestracker.app-main-56:/layout/activity_enhanced_tasks.xml"}, {"merged": "com.company.fieldsalestracker.app-mergeDebugResources-53:/layout/item_enhanced_task.xml", "source": "com.company.fieldsalestracker.app-main-56:/layout/item_enhanced_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-mergeDebugResources-53:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_main.xml"}, {"merged": "com.company.fieldsalestracker.app-mergeDebugResources-53:/layout/item_multi_task.xml", "source": "com.company.fieldsalestracker.app-main-56:/layout/item_multi_task.xml"}, {"merged": "com.company.fieldsalestracker.app-mergeDebugResources-53:/layout/activity_multi_tasks.xml", "source": "com.company.fieldsalestracker.app-main-56:/layout/activity_multi_tasks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-mergeDebugResources-53:\\layout\\activity_tasks.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_tasks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-mergeDebugResources-53:\\layout\\activity_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-mergeDebugResources-53:\\layout\\activity_task_details.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_task_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-mergeDebugResources-53:\\layout\\item_client.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\item_client.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-mergeDebugResources-53:\\layout\\activity_my_visits.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_my_visits.xml"}]