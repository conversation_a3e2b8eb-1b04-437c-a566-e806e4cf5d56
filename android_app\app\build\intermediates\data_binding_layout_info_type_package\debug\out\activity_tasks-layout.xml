<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_tasks" modulePackage="com.company.fieldsalestracker" filePath="app\src\main\res\layout\activity_tasks.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_tasks_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="132" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="66"/></Target><Target id="@+id/swipeRefreshLayout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="23" startOffset="4" endLine="119" endOffset="59"/></Target><Target id="@+id/recyclerViewTasks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="71" startOffset="12" endLine="78" endOffset="52"/></Target><Target id="@+id/emptyState" view="LinearLayout"><Expressions/><location startLine="81" startOffset="12" endLine="115" endOffset="26"/></Target><Target id="@+id/fabRefresh" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="122" startOffset="4" endLine="130" endOffset="41"/></Target></Targets></Layout>