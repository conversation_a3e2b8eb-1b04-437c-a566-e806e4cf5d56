1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.company.fieldsalestracker"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="33" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.CAMERA" />
14-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:8:5-65
14-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:8:22-62
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:9:5-79
15-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:10:5-81
16-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:10:22-78
17    <uses-permission
17-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:11:22-78
19        android:maxSdkVersion="28" />
19-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:12:9-35
20    <uses-permission
20-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:13:5-14:38
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:13:22-77
22        android:maxSdkVersion="32" />
22-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:14:9-35
23
24    <!-- Camera features -->
25    <uses-feature
25-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:17:5-19:35
26        android:name="android.hardware.camera"
26-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:18:9-47
27        android:required="true" />
27-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:19:9-32
28    <uses-feature
28-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:20:5-22:36
29        android:name="android.hardware.camera.autofocus"
29-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:21:9-57
30        android:required="false" />
30-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:22:9-33
31
32    <!-- Location features -->
33    <uses-feature
33-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:25:5-27:35
34        android:name="android.hardware.location"
34-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:26:9-49
35        android:required="true" />
35-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:27:9-32
36    <uses-feature
36-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:28:5-30:36
37        android:name="android.hardware.location.gps"
37-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:29:9-53
38        android:required="false" />
38-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:30:9-33
39
40    <queries>
40-->[androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:22:5-26:15
41        <intent>
41-->[androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:23:9-25:18
42            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
42-->[androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:24:13-86
42-->[androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:24:21-83
43        </intent>
44        <!-- Needs to be explicitly declared on Android R+ -->
45        <package android:name="com.google.android.apps.maps" />
45-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:9-64
45-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:18-61
46    </queries>
47
48    <uses-feature
48-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:26:5-28:35
49        android:glEsVersion="0x00020000"
49-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:27:9-41
50        android:required="true" />
50-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:28:9-32
51
52    <permission
52-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
53        android:name="com.company.fieldsalestracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
53-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
54        android:protectionLevel="signature" />
54-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
55
56    <uses-permission android:name="com.company.fieldsalestracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
56-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
56-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
57
58    <application
58-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:32:5-154:19
59        android:allowBackup="true"
59-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:33:9-35
60        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
60-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\043eee66ca32b7ab1e056bec5c84992b\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
61        android:dataExtractionRules="@xml/data_extraction_rules"
61-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:34:9-65
62        android:debuggable="true"
62-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:43:9-35
63        android:extractNativeLibs="false"
64        android:fullBackupContent="@xml/backup_rules"
64-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:35:9-54
65        android:icon="@mipmap/ic_launcher"
65-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:36:9-43
66        android:label="@string/app_name"
66-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:37:9-41
67        android:networkSecurityConfig="@xml/network_security_config"
67-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:42:9-69
68        android:roundIcon="@mipmap/ic_launcher_round"
68-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:38:9-54
69        android:supportsRtl="true"
69-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:39:9-35
70        android:testOnly="true"
71        android:theme="@style/Theme.FieldSalesTracker"
71-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:40:9-55
72        android:usesCleartextTraffic="true" >
72-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:41:9-44
73
74        <!-- Splash Activity (Main Launcher) -->
75        <activity
75-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:48:9-57:20
76            android:name="com.company.fieldsalestracker.SplashActivity"
76-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:49:13-43
77            android:exported="true"
77-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:50:13-36
78            android:label="@string/app_name"
78-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:51:13-45
79            android:theme="@style/Theme.FieldSalesTracker.Splash" >
79-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:52:13-66
80            <intent-filter>
80-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:53:13-56:29
81                <action android:name="android.intent.action.MAIN" />
81-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:54:17-69
81-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:54:25-66
82
83                <category android:name="android.intent.category.LAUNCHER" />
83-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:55:17-77
83-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:55:27-74
84            </intent-filter>
85        </activity>
86
87        <!-- Main Activity -->
88        <activity
88-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:60:9-64:62
89            android:name="com.company.fieldsalestracker.MainActivity"
89-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:61:13-41
90            android:exported="false"
90-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:62:13-37
91            android:label="@string/app_name"
91-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:63:13-45
92            android:theme="@style/Theme.FieldSalesTracker" />
92-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:64:13-59
93
94        <!-- Login Activity -->
95        <activity
95-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:67:9-70:74
96            android:name="com.company.fieldsalestracker.LoginActivity"
96-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:68:13-42
97            android:exported="false"
97-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:69:13-37
98            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
98-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:70:13-71
99
100        <!-- My Visits Activity -->
101        <activity
101-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:73:9-77:74
102            android:name="com.company.fieldsalestracker.MyVisitsActivity"
102-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:74:13-45
103            android:exported="false"
103-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:75:13-37
104            android:parentActivityName="com.company.fieldsalestracker.MainActivity"
104-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:76:13-55
105            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
105-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:77:13-71
106
107        <!-- Tasks Activity -->
108        <activity
108-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:80:9-84:74
109            android:name="com.company.fieldsalestracker.TasksActivity"
109-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:81:13-42
110            android:exported="false"
110-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:82:13-37
111            android:parentActivityName="com.company.fieldsalestracker.MainActivity"
111-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:83:13-55
112            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
112-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:84:13-71
113
114        <!-- Task Details Activity -->
115        <activity
115-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:87:9-91:74
116            android:name="com.company.fieldsalestracker.TaskDetailsActivity"
116-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:88:13-48
117            android:exported="false"
117-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:89:13-37
118            android:parentActivityName="com.company.fieldsalestracker.TasksActivity"
118-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:90:13-56
119            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
119-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:91:13-71
120
121        <!-- Barcode Scanner Activity -->
122        <activity
122-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:94:9-98:74
123            android:name="com.company.fieldsalestracker.BarcodeScannerActivity"
123-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:95:13-51
124            android:exported="false"
124-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:96:13-37
125            android:screenOrientation="portrait"
125-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:97:13-49
126            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
126-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:98:13-71
127
128        <!-- Visit Details Activity -->
129        <activity
129-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:101:9-105:74
130            android:name="com.company.fieldsalestracker.VisitDetailsActivity"
130-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:102:13-49
131            android:exported="false"
131-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:103:13-37
132            android:parentActivityName="com.company.fieldsalestracker.MainActivity"
132-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:104:13-55
133            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
133-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:105:13-71
134
135        <!-- Client List Activity -->
136        <activity
136-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:108:9-111:58
137            android:name="com.company.fieldsalestracker.ClientListActivity"
137-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:109:13-47
138            android:exported="false"
138-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:110:13-37
139            android:parentActivityName="com.company.fieldsalestracker.MainActivity" />
139-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:111:13-55
140
141        <!-- Enhanced Tasks Activities -->
142        <activity
142-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:114:9-118:74
143            android:name="com.company.fieldsalestracker.EnhancedTasksActivity"
143-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:115:13-50
144            android:exported="false"
144-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:116:13-37
145            android:parentActivityName="com.company.fieldsalestracker.MainActivity"
145-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:117:13-55
146            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
146-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:118:13-71
147        <activity
147-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:120:9-124:74
148            android:name="com.company.fieldsalestracker.EnhancedTaskDetailsActivity"
148-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:121:13-56
149            android:exported="false"
149-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:122:13-37
150            android:parentActivityName="com.company.fieldsalestracker.EnhancedTasksActivity"
150-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:123:13-64
151            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
151-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:124:13-71
152        <activity
152-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:126:9-130:74
153            android:name="com.company.fieldsalestracker.MultiTasksActivity"
153-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:127:13-47
154            android:exported="false"
154-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:128:13-37
155            android:parentActivityName="com.company.fieldsalestracker.MainActivity"
155-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:129:13-55
156            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
156-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:130:13-71
157        <activity
157-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:132:9-136:74
158            android:name="com.company.fieldsalestracker.MultiTaskDetailsActivity"
158-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:133:13-53
159            android:exported="false"
159-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:134:13-37
160            android:parentActivityName="com.company.fieldsalestracker.MultiTasksActivity"
160-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:135:13-61
161            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
161-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:136:13-71
162
163        <!-- File Provider for camera -->
164        <provider
165            android:name="androidx.core.content.FileProvider"
165-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:140:13-62
166            android:authorities="com.company.fieldsalestracker.fileprovider"
166-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:141:13-64
167            android:exported="false"
167-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:142:13-37
168            android:grantUriPermissions="true" >
168-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:143:13-47
169            <meta-data
169-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:144:13-146:54
170                android:name="android.support.FILE_PROVIDER_PATHS"
170-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:145:17-67
171                android:resource="@xml/file_paths" />
171-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:146:17-51
172        </provider>
173
174        <!-- ML Kit metadata -->
175        <meta-data
175-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:150:9-152:39
176            android:name="com.google.mlkit.vision.DEPENDENCIES"
176-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:151:13-64
177            android:value="barcode" />
177-->D:\django_project\TrackCustomer\android_app\app\src\main\AndroidManifest.xml:152:13-36
178
179        <activity
179-->[pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:12:9-16:66
180            android:name="pub.devrel.easypermissions.AppSettingsDialogHolderActivity"
180-->[pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:13:13-86
181            android:exported="false"
181-->[pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:14:13-37
182            android:label=""
182-->[pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:15:13-29
183            android:theme="@style/EasyPermissions.Transparent" />
183-->[pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8c1a298d56c343cd17ff04a8ecd13cf\transformed\jetified-easypermissions-3.0.0\AndroidManifest.xml:16:13-63
184
185        <uses-library
185-->[androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:29:9-31:40
186            android:name="androidx.camera.extensions.impl"
186-->[androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:30:13-59
187            android:required="false" />
187-->[androidx.camera:camera-extensions:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\00e77d8f2f137c213e975e30a115a215\transformed\jetified-camera-extensions-1.2.3\AndroidManifest.xml:31:13-37
188
189        <service
189-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
190            android:name="androidx.camera.core.impl.MetadataHolderService"
190-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
191            android:enabled="false"
191-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
192            android:exported="false" >
192-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
193            <meta-data
193-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
194                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
194-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
195                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
195-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\86e48ff059f3e29f2a70b283d9f8e234\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
196        </service>
197
198        <provider
198-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
199            android:name="androidx.startup.InitializationProvider"
199-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
200            android:authorities="com.company.fieldsalestracker.androidx-startup"
200-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
201            android:exported="false" >
201-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
202            <meta-data
202-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
203                android:name="androidx.emoji2.text.EmojiCompatInitializer"
203-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
204                android:value="androidx.startup" />
204-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c41707dd5668a7075bd02add64cb109\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
205            <meta-data
205-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
206                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
206-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
207                android:value="androidx.startup" />
207-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0bf96eb60c0f7d983b0aa463da1ca589\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
208            <meta-data
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
209                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
210                android:value="androidx.startup" />
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
211        </provider> <!-- Needs to be explicitly declared on P+ -->
212        <uses-library
212-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:39:9-41:40
213            android:name="org.apache.http.legacy"
213-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:40:13-50
214            android:required="false" />
214-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af897f74738ab4a609d785d92f70b926\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:41:13-37
215
216        <service
216-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:9:9-15:19
217            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
217-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:10:13-91
218            android:directBootAware="true"
218-->[com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:17:13-43
219            android:exported="false" >
219-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:11:13-37
220            <meta-data
220-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:12:13-14:85
221                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
221-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:13:17-120
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d506f28adca68235227081337589756\transformed\jetified-play-services-mlkit-barcode-scanning-18.2.0\AndroidManifest.xml:14:17-82
223            <meta-data
223-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
224                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
224-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cee7f25532a41b688e4cb861a49786\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
226            <meta-data
226-->[com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:20:13-22:85
227                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
227-->[com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:21:17-120
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:22:17-82
229        </service>
230
231        <provider
231-->[com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:9:9-13:38
232            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
232-->[com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:10:13-78
233            android:authorities="com.company.fieldsalestracker.mlkitinitprovider"
233-->[com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:11:13-69
234            android:exported="false"
234-->[com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:12:13-37
235            android:initOrder="99" />
235-->[com.google.mlkit:common:18.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a14cdf64d4a636d4a388a7bc9fd6b2\transformed\jetified-common-18.7.0\AndroidManifest.xml:13:13-35
236
237        <activity
237-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
238            android:name="com.google.android.gms.common.api.GoogleApiActivity"
238-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
239            android:exported="false"
239-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
240            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
240-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb7e328e390e7f202e781ab04bb4484f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
241
242        <meta-data
242-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88fe9f048cf6eb09539000df0730205\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
243            android:name="com.google.android.gms.version"
243-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88fe9f048cf6eb09539000df0730205\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
244            android:value="@integer/google_play_services_version" />
244-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88fe9f048cf6eb09539000df0730205\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
245
246        <uses-library
246-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
247            android:name="androidx.window.extensions"
247-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
248            android:required="false" />
248-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
249        <uses-library
249-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
250            android:name="androidx.window.sidecar"
250-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
251            android:required="false" />
251-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3367e49b86ee5f1662ec718d32a030cb\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
252
253        <receiver
253-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
254            android:name="androidx.profileinstaller.ProfileInstallReceiver"
254-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
255            android:directBootAware="false"
255-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
256            android:enabled="true"
256-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
257            android:exported="true"
257-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
258            android:permission="android.permission.DUMP" >
258-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
259            <intent-filter>
259-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
260                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
260-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
260-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
261            </intent-filter>
262            <intent-filter>
262-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
263                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
263-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
263-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
264            </intent-filter>
265            <intent-filter>
265-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
266                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
266-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
266-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
267            </intent-filter>
268            <intent-filter>
268-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
269                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
269-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
269-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\71c46b5a1941241b2eb82e2606f4cf7c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
270            </intent-filter>
271        </receiver>
272
273        <service
273-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
274            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
274-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
275            android:exported="false" >
275-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
276            <meta-data
276-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
277                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
277-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
278                android:value="cct" />
278-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4a12bce6c0245eb4283ef2e5a40e8cb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
279        </service>
280        <service
280-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
281            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
281-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
282            android:exported="false"
282-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
283            android:permission="android.permission.BIND_JOB_SERVICE" >
283-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
284        </service>
285
286        <receiver
286-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
287            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
287-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
288            android:exported="false" />
288-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\d0fbefc7b5eb0b94d7b92f8b6a914ba4\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
289    </application>
290
291</manifest>
