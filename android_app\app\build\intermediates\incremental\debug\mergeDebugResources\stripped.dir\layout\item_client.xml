<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:rippleColor="@color/primary_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Client Icon -->
        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/circle_background"
            android:padding="12dp"
            android:src="@drawable/ic_business"
            app:tint="@color/white" />

        <!-- Client Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvClientName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="شركة الأمل التجارية" />

            <TextView
                android:id="@+id/tvClientAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                tools:text="الرياض، حي الملك فهد" />

            <TextView
                android:id="@+id/tvClientDistance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/info_color"
                android:textSize="12sp"
                tools:text="المسافة: 150 متر" />

        </LinearLayout>

        <!-- Action Button -->
        <ImageButton
            android:id="@+id/btnVisitClient"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="زيارة العميل"
            android:src="@drawable/ic_arrow_forward"
            app:tint="@color/primary_color" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
