#Sat Jul 26 03:18:33 AST 2025
com.company.fieldsalestracker.app-main-5\:/anim/button_scale_down.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\anim\\button_scale_down.xml
com.company.fieldsalestracker.app-main-5\:/anim/button_scale_up.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\anim\\button_scale_up.xml
com.company.fieldsalestracker.app-main-5\:/drawable/badge_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\badge_background.xml
com.company.fieldsalestracker.app-main-5\:/drawable/btn_camera_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\btn_camera_background.xml
com.company.fieldsalestracker.app-main-5\:/drawable/btn_submit_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\btn_submit_background.xml
com.company.fieldsalestracker.app-main-5\:/drawable/button_acknowledge.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\button_acknowledge.xml
com.company.fieldsalestracker.app-main-5\:/drawable/button_scanner.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\button_scanner.xml
com.company.fieldsalestracker.app-main-5\:/drawable/button_start.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\button_start.xml
com.company.fieldsalestracker.app-main-5\:/drawable/circle_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\circle_background.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_alarm.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_alarm.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_arrow_back.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_arrow_back.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_arrow_forward.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_arrow_forward.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_assignment.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_assignment.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_business.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_business.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_camera.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_camera.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_camera_enhanced.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_camera_enhanced.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_check_circle.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_check_circle.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_enhanced_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_enhanced_task.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_event.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_event.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_group.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_group.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_help.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_help.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_launcher_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_background.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_launcher_foreground.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_foreground.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_launcher_simple.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_simple.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_list.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_list.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_location.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_location.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_location_on.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_location_on.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_lock.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_lock.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_logout.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_logout.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_multi_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_multi_task.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_person.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_person.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_play_arrow.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_play_arrow.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_priority_high.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_priority_high.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_priority_low.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_priority_low.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_priority_medium.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_priority_medium.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_qr_code_scanner.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_qr_code_scanner.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_refresh.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_refresh.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_repeat.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_repeat.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_schedule.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_schedule.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_send.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_send.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_send_enhanced.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_send_enhanced.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_subdirectory_arrow_right.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_subdirectory_arrow_right.xml
com.company.fieldsalestracker.app-main-5\:/drawable/ic_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_task.xml
com.company.fieldsalestracker.app-main-5\:/drawable/login_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\login_background.xml
com.company.fieldsalestracker.app-main-5\:/drawable/scanner_overlay.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\scanner_overlay.xml
com.company.fieldsalestracker.app-main-5\:/drawable/splash_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\splash_background.xml
com.company.fieldsalestracker.app-main-5\:/drawable/status_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\status_background.xml
com.company.fieldsalestracker.app-main-5\:/menu/main_menu.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\menu\\main_menu.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\ic_launcher.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-hdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-hdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher_round.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-mdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-mdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher_round.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-xhdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-xhdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher_round.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-xxhdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher_round.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-xxxhdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher.xml
com.company.fieldsalestracker.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher_round.xml
com.company.fieldsalestracker.app-main-5\:/xml/backup_rules.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\xml\\backup_rules.xml
com.company.fieldsalestracker.app-main-5\:/xml/data_extraction_rules.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\xml\\data_extraction_rules.xml
com.company.fieldsalestracker.app-main-5\:/xml/file_paths.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\xml\\file_paths.xml
com.company.fieldsalestracker.app-main-5\:/xml/network_security_config.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\xml\\network_security_config.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/activity_barcode_scanner.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_barcode_scanner.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/activity_enhanced_tasks.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_enhanced_tasks.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/activity_login.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_login.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/activity_main.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_main.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/activity_multi_tasks.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_multi_tasks.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/activity_my_visits.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_my_visits.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/activity_splash.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_splash.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/activity_task_details.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_task_details.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/activity_tasks.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_tasks.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/activity_visit_details.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_visit_details.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/item_client.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_client.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/item_enhanced_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_enhanced_task.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/item_multi_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_multi_task.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/item_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_task.xml
com.company.fieldsalestracker.app-packageDebugResources-2\:/layout/item_visit.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_visit.xml
