{"logs": [{"outputFile": "com.company.fieldsalestracker.app-mergeDebugResources-53:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed709288d23a486d0424a1f6be3fc698\\transformed\\navigation-ui-2.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "112,113", "startColumns": "4,4", "startOffsets": "10340,10452", "endColumns": "111,119", "endOffsets": "10447,10567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb7e328e390e7f202e781ab04bb4484f\\transformed\\jetified-play-services-base-18.1.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3796,3901,4064,4192,4300,4468,4596,4718,4972,5160,5268,5438,5569,5728,5906,5974,6043", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "3896,4059,4187,4295,4463,4591,4713,4822,5155,5263,5433,5564,5723,5901,5969,6038,6125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c88fe9f048cf6eb09539000df0730205\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4827", "endColumns": "144", "endOffsets": "4967"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ce97e1d7afd464f10c84d8596a83f4ee\\transformed\\appcompat-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,10658", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,10739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\043eee66ca32b7ab1e056bec5c84992b\\transformed\\core-1.9.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "116", "startColumns": "4", "startOffsets": "10744", "endColumns": "100", "endOffsets": "10840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43bdbc0b1ff399fa79cb359f37c50201\\transformed\\material-1.9.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1079,1171,1250,1315,1405,1469,1537,1599,1672,1736,1790,1916,1974,2036,2090,2166,2309,2396,2478,2617,2699,2781,2868,2924,2975,3041,3116,3196,3283,3356,3433,3506,3580,3673,3750,3843,3941,4015,4096,4195,4248,4314,4403,4491,4553,4617,4680,4796,4899,5006,5110,5170,5225", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,86,55,50,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,59,54,85", "endOffsets": "268,349,429,511,610,706,809,929,1010,1074,1166,1245,1310,1400,1464,1532,1594,1667,1731,1785,1911,1969,2031,2085,2161,2304,2391,2473,2612,2694,2776,2863,2919,2970,3036,3111,3191,3278,3351,3428,3501,3575,3668,3745,3838,3936,4010,4091,4190,4243,4309,4398,4486,4548,4612,4675,4791,4894,5001,5105,5165,5220,5306"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3054,3135,3215,3297,3396,3492,3595,3715,6130,6194,6286,6365,6430,6520,6584,6652,6714,6787,6851,6905,7031,7089,7151,7205,7281,7424,7511,7593,7732,7814,7896,7983,8039,8090,8156,8231,8311,8398,8471,8548,8621,8695,8788,8865,8958,9056,9130,9211,9310,9363,9429,9518,9606,9668,9732,9795,9911,10014,10121,10225,10285,10572", "endLines": "5,33,34,35,36,37,38,39,40,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,86,55,50,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,59,54,85", "endOffsets": "318,3130,3210,3292,3391,3487,3590,3710,3791,6189,6281,6360,6425,6515,6579,6647,6709,6782,6846,6900,7026,7084,7146,7200,7276,7419,7506,7588,7727,7809,7891,7978,8034,8085,8151,8226,8306,8393,8466,8543,8616,8690,8783,8860,8953,9051,9125,9206,9305,9358,9424,9513,9601,9663,9727,9790,9906,10009,10116,10220,10280,10335,10653"}}]}]}