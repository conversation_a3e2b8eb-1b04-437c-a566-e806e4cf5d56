{"logs": [{"outputFile": "com.company.fieldsalestracker.app-mergeDebugResources-53:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43bdbc0b1ff399fa79cb359f37c50201\\transformed\\material-1.9.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1055,1147,1216,1275,1360,1423,1485,1543,1607,1668,1722,1836,1894,1954,2008,2078,2205,2286,2365,2500,2576,2653,2737,2792,2847,2913,2982,3059,3145,3213,3289,3359,3424,3519,3592,3686,3779,3853,3922,4016,4072,4139,4223,4311,4373,4437,4500,4597,4692,4783,4879,4938,4997", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,83,54,54,65,68,76,85,67,75,69,64,94,72,93,92,73,68,93,55,66,83,87,61,63,62,96,94,90,95,58,58,76", "endOffsets": "258,335,414,495,594,683,791,903,986,1050,1142,1211,1270,1355,1418,1480,1538,1602,1663,1717,1831,1889,1949,2003,2073,2200,2281,2360,2495,2571,2648,2732,2787,2842,2908,2977,3054,3140,3208,3284,3354,3419,3514,3587,3681,3774,3848,3917,4011,4067,4134,4218,4306,4368,4432,4495,4592,4687,4778,4874,4933,4992,5069"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2975,3052,3131,3212,3311,3400,3508,3620,5968,6032,6124,6193,6252,6337,6400,6462,6520,6584,6645,6699,6813,6871,6931,6985,7055,7182,7263,7342,7477,7553,7630,7714,7769,7824,7890,7959,8036,8122,8190,8266,8336,8401,8496,8569,8663,8756,8830,8899,8993,9049,9116,9200,9288,9350,9414,9477,9574,9669,9760,9856,9915,10207", "endLines": "5,33,34,35,36,37,38,39,40,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,83,54,54,65,68,76,85,67,75,69,64,94,72,93,92,73,68,93,55,66,83,87,61,63,62,96,94,90,95,58,58,76", "endOffsets": "308,3047,3126,3207,3306,3395,3503,3615,3698,6027,6119,6188,6247,6332,6395,6457,6515,6579,6640,6694,6808,6866,6926,6980,7050,7177,7258,7337,7472,7548,7625,7709,7764,7819,7885,7954,8031,8117,8185,8261,8331,8396,8491,8564,8658,8751,8825,8894,8988,9044,9111,9195,9283,9345,9409,9472,9569,9664,9755,9851,9910,9969,10279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c88fe9f048cf6eb09539000df0730205\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4714", "endColumns": "150", "endOffsets": "4860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed709288d23a486d0424a1f6be3fc698\\transformed\\navigation-ui-2.6.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,117", "endOffsets": "165,283"}, "to": {"startLines": "112,113", "startColumns": "4,4", "startOffsets": "9974,10089", "endColumns": "114,117", "endOffsets": "10084,10202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb7e328e390e7f202e781ab04bb4484f\\transformed\\jetified-play-services-base-18.1.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3703,3810,3983,4113,4222,4369,4498,4611,4865,5027,5136,5309,5441,5594,5755,5820,5886", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "3805,3978,4108,4217,4364,4493,4606,4709,5022,5131,5304,5436,5589,5750,5815,5881,5963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ce97e1d7afd464f10c84d8596a83f4ee\\transformed\\appcompat-1.6.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,10284", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,10359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\043eee66ca32b7ab1e056bec5c84992b\\transformed\\core-1.9.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "116", "startColumns": "4", "startOffsets": "10364", "endColumns": "100", "endOffsets": "10460"}}]}]}