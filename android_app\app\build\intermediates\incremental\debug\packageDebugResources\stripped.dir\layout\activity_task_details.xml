<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    tools:context=".TaskDetailsActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Task Title Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/titleText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="عنوان المهمة"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/taskIdText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="رقم المهمة"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/taskTypeText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="نوع المهمة"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvTaskTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="عنوان المهمة"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvTaskDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="وصف المهمة"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:visibility="gone" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Status and Priority Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <!-- Status Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardStatus"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الحالة"
                        android:textSize="12sp"
                        android:textColor="#999999"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tvStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="مُكلف"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Priority Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardPriority"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الأولوية"
                        android:textSize="12sp"
                        android:textColor="#999999"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tvPriority"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="متوسط"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- Client Information Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🏢 معلومات العميل"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tvClientName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="اسم العميل"
                    android:textSize="14sp"
                    android:textColor="#333333"
                    android:layout_marginBottom="4dp"
                    android:drawableStart="@drawable/ic_business"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical" />

                <TextView
                    android:id="@+id/tvClientAddress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="عنوان العميل"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:drawableStart="@drawable/ic_location_on"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Task Information Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📋 تفاصيل المهمة"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tvAssignedAt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="تاريخ التكليف: 2025-01-20 10:00"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:layout_marginBottom="4dp"
                    android:drawableStart="@drawable/ic_schedule"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical" />

                <TextView
                    android:id="@+id/tvDueDate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="الموعد المحدد: 2025-01-21 17:00"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:layout_marginBottom="4dp"
                    android:drawableStart="@drawable/ic_alarm"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvAssignedBy"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="كُلف من: اسم المدير"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:drawableStart="@drawable/ic_person"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/btnAcknowledge"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="✓ تأكيد الاطلاع على المهمة"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/button_acknowledge"
                android:textColor="@android:color/white"
                android:layout_marginBottom="8dp"
                android:visibility="gone" />

            <Button
                android:id="@+id/btnStart"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="▶ بدء تنفيذ المهمة"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/button_start"
                android:textColor="@android:color/white"
                android:layout_marginBottom="8dp"
                android:visibility="gone" />

            <Button
                android:id="@+id/btnOpenScanner"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="📷 فتح ماسح الباركود"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/button_scanner"
                android:textColor="@android:color/white"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
