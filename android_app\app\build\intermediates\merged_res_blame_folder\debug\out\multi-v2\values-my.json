{"logs": [{"outputFile": "com.company.fieldsalestracker.app-mergeDebugResources-53:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed709288d23a486d0424a1f6be3fc698\\transformed\\navigation-ui-2.6.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,125", "endOffsets": "163,289"}, "to": {"startLines": "112,113", "startColumns": "4,4", "startOffsets": "10365,10478", "endColumns": "112,125", "endOffsets": "10473,10599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ce97e1d7afd464f10c84d8596a83f4ee\\transformed\\appcompat-1.6.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,448,555,671,758,867,990,1069,1147,1238,1331,1426,1520,1620,1713,1808,1902,1993,2084,2169,2284,2393,2492,2618,2725,2833,2993,10686", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "443,550,666,753,862,985,1064,1142,1233,1326,1421,1515,1615,1708,1803,1897,1988,2079,2164,2279,2388,2487,2613,2720,2828,2988,3091,10767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c88fe9f048cf6eb09539000df0730205\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-my\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "149", "endOffsets": "344"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4895", "endColumns": "153", "endOffsets": "5044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\043eee66ca32b7ab1e056bec5c84992b\\transformed\\core-1.9.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "116", "startColumns": "4", "startOffsets": "10772", "endColumns": "100", "endOffsets": "10868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb7e328e390e7f202e781ab04bb4484f\\transformed\\jetified-play-services-base-18.1.0\\res\\values-my\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,586,693,836,964,1083,1189,1361,1463,1629,1768,1922,2105,2171,2240", "endColumns": "102,159,129,106,142,127,118,105,171,101,165,138,153,182,65,68,84", "endOffsets": "295,455,585,692,835,963,1082,1188,1360,1462,1628,1767,1921,2104,2170,2239,2324"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3867,3974,4138,4272,4383,4530,4662,4785,5049,5225,5331,5501,5644,5802,5989,6059,6132", "endColumns": "106,163,133,110,146,131,122,109,175,105,169,142,157,186,69,72,88", "endOffsets": "3969,4133,4267,4378,4525,4657,4780,4890,5220,5326,5496,5639,5797,5984,6054,6127,6216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43bdbc0b1ff399fa79cb359f37c50201\\transformed\\material-1.9.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,652,736,842,971,1056,1121,1211,1286,1345,1436,1499,1564,1623,1694,1756,1813,1932,1990,2051,2106,2179,2311,2402,2491,2632,2710,2787,2879,2937,2988,3054,3126,3208,3290,3365,3439,3511,3590,3687,3768,3854,3946,4020,4099,4185,4239,4307,4390,4471,4533,4597,4660,4772,4875,4979,5084,5145,5200", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,91,57,50,65,71,81,81,74,73,71,78,96,80,85,91,73,78,85,53,67,82,80,61,63,62,111,102,103,104,60,54,81", "endOffsets": "280,381,480,556,647,731,837,966,1051,1116,1206,1281,1340,1431,1494,1559,1618,1689,1751,1808,1927,1985,2046,2101,2174,2306,2397,2486,2627,2705,2782,2874,2932,2983,3049,3121,3203,3285,3360,3434,3506,3585,3682,3763,3849,3941,4015,4094,4180,4234,4302,4385,4466,4528,4592,4655,4767,4870,4974,5079,5140,5195,5277"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3096,3197,3296,3372,3463,3547,3653,3782,6221,6286,6376,6451,6510,6601,6664,6729,6788,6859,6921,6978,7097,7155,7216,7271,7344,7476,7567,7656,7797,7875,7952,8044,8102,8153,8219,8291,8373,8455,8530,8604,8676,8755,8852,8933,9019,9111,9185,9264,9350,9404,9472,9555,9636,9698,9762,9825,9937,10040,10144,10249,10310,10604", "endLines": "5,33,34,35,36,37,38,39,40,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,91,57,50,65,71,81,81,74,73,71,78,96,80,85,91,73,78,85,53,67,82,80,61,63,62,111,102,103,104,60,54,81", "endOffsets": "330,3192,3291,3367,3458,3542,3648,3777,3862,6281,6371,6446,6505,6596,6659,6724,6783,6854,6916,6973,7092,7150,7211,7266,7339,7471,7562,7651,7792,7870,7947,8039,8097,8148,8214,8286,8368,8450,8525,8599,8671,8750,8847,8928,9014,9106,9180,9259,9345,9399,9467,9550,9631,9693,9757,9820,9932,10035,10139,10244,10305,10360,10681"}}]}]}