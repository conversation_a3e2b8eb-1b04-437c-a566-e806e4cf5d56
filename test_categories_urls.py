#!/usr/bin/env python
"""
اختبار URLs التصنيفات بعد الإصلاحات
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'field_sales_tracker.settings')
django.setup()

from categories.models import Category
from users.models import User
from django.urls import reverse
from django.test import Client
from django.contrib.auth import get_user_model

def test_category_urls():
    """اختبار URLs التصنيفات باستخدام Django Test Client"""
    print("🧪 اختبار URLs التصنيفات")
    print("=" * 50)

    # إنشاء client للاختبار
    client = Client()

    # تسجيل دخول مستخدم للاختبار
    User = get_user_model()
    user = User.objects.filter(role_type=1).first()  # مدير عام
    if user:
        client.force_login(user)
        print(f"👤 تم تسجيل الدخول كـ: {user.username}")
    else:
        print("⚠️ لا يوجد مدير عام للاختبار")

    # الحصول على تصنيف للاختبار
    category = Category.objects.first()
    if not category:
        print("❌ لا توجد تصنيفات للاختبار")
        return []

    print(f"📋 اختبار التصنيف: {category.name} ({category.id})")

    # URLs للاختبار
    urls_to_test = [
        # URLs الأساسية
        ("لوحة التحكم", "/categories/"),
        ("قائمة التصنيفات", "/categories/list/"),

        # URLs مع UUID
        ("تعديل التصنيف", f"/categories/edit/{category.id}/"),
        ("حذف التصنيف", f"/categories/delete/{category.id}/"),
        ("تفاصيل التصنيف", f"/categories/{category.id}/details/"),
        ("إضافة مستخدمين", f"/categories/{category.id}/assign-users/"),
        ("مستخدمي التصنيف", f"/categories/{category.id}/users/"),

        # APIs
        ("API قائمة التصنيفات", "/categories/api/list/"),
        ("API إحصائيات", "/categories/api/stats/"),
    ]

    results = []

    for name, url in urls_to_test:
        print(f"\n🔍 اختبار {name}: {url}")

        try:
            response = client.get(url)
            status_code = response.status_code

            if status_code == 200:
                print(f"   ✅ نجح: {status_code}")
                results.append((name, url, "نجح", status_code))
            elif status_code == 302:
                print(f"   🔄 إعادة توجيه: {status_code}")
                results.append((name, url, "إعادة توجيه", status_code))
            elif status_code == 403:
                print(f"   🚫 ممنوع: {status_code}")
                results.append((name, url, "ممنوع", status_code))
            elif status_code == 404:
                print(f"   ❌ غير موجود: {status_code}")
                results.append((name, url, "غير موجود", status_code))
            elif status_code == 500:
                print(f"   💥 خطأ خادم: {status_code}")
                results.append((name, url, "خطأ خادم", status_code))
            else:
                print(f"   ⚠️ حالة غير متوقعة: {status_code}")
                results.append((name, url, "غير متوقع", status_code))

        except Exception as e:
            print(f"   ❌ خطأ في الطلب: {e}")
            results.append((name, url, "خطأ طلب", "N/A"))

    return results

def test_django_urls():
    """اختبار URLs باستخدام Django reverse"""
    print("\n🔗 اختبار Django URLs")
    print("=" * 50)
    
    # الحصول على تصنيف للاختبار
    category = Category.objects.first()
    if not category:
        print("❌ لا توجد تصنيفات للاختبار")
        return
    
    # URLs للاختبار
    django_urls = [
        ("categories:dashboard", {}),
        ("categories:list", {}),
        ("categories:edit", {"pk": category.id}),
        ("categories:delete", {"pk": category.id}),
        ("categories:get_category_details", {"pk": category.id}),
        ("categories:assign_users_to_category", {"pk": category.id}),
        ("categories:users", {"pk": category.id}),
        ("categories:api_list", {}),
        ("categories:stats", {}),
    ]
    
    for url_name, kwargs in django_urls:
        try:
            url = reverse(url_name, kwargs=kwargs)
            print(f"✅ {url_name}: {url}")
        except Exception as e:
            print(f"❌ {url_name}: خطأ - {e}")

def test_category_operations():
    """اختبار عمليات التصنيفات"""
    print("\n⚙️ اختبار عمليات التصنيفات")
    print("=" * 50)
    
    # إحصائيات التصنيفات
    total_categories = Category.objects.count()
    active_categories = Category.objects.filter(is_active=True).count()
    
    print(f"📊 إحصائيات التصنيفات:")
    print(f"   إجمالي التصنيفات: {total_categories}")
    print(f"   التصنيفات النشطة: {active_categories}")
    
    # توزيع التصنيفات حسب النوع
    for type_id, type_name in Category.CATEGORY_TYPES:
        count = Category.objects.filter(category_type=type_id).count()
        print(f"   {type_name}: {count}")
    
    # اختبار التصنيفات مع UUID
    print(f"\n🔍 اختبار UUIDs:")
    categories_sample = Category.objects.all()[:3]
    
    for category in categories_sample:
        print(f"   📋 {category.name}:")
        print(f"      UUID: {category.id}")
        print(f"      النوع: {category.get_category_type_display()}")
        print(f"      نشط: {category.is_active}")

def generate_report(results):
    """إنشاء تقرير النتائج"""
    print("\n📊 تقرير النتائج")
    print("=" * 50)
    
    success_count = len([r for r in results if r[2] in ["نجح", "إعادة توجيه"]])
    total_count = len(results)
    
    print(f"إجمالي URLs المختبرة: {total_count}")
    print(f"URLs الناجحة: {success_count}")
    print(f"معدل النجاح: {(success_count/total_count)*100:.1f}%")
    
    print(f"\n📋 تفاصيل النتائج:")
    for name, url, status, code in results:
        status_icon = "✅" if status in ["نجح", "إعادة توجيه"] else "❌"
        print(f"   {status_icon} {name}: {status} ({code})")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار URLs التصنيفات")
    print("=" * 60)
    
    try:
        # اختبار Django URLs
        test_django_urls()
        
        # اختبار عمليات التصنيفات
        test_category_operations()
        
        # اختبار URLs عبر HTTP
        results = test_category_urls()
        
        # إنشاء تقرير
        if results:
            generate_report(results)
        
        print("\n🎉 تم إنجاز جميع الاختبارات!")
        print("✅ URLs التصنيفات تم إصلاحها")
        print("✅ Template edit.html تم إنشاؤه")
        print("✅ UUID patterns تعمل بشكل صحيح")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
