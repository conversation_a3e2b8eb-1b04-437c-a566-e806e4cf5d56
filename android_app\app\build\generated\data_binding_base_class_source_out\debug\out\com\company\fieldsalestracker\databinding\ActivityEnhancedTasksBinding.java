// Generated by view binder compiler. Do not edit!
package com.company.fieldsalestracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.company.fieldsalestracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEnhancedTasksBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCreateTask;

  @NonNull
  public final Button btnMultiTasks;

  @NonNull
  public final TextView emptyView;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewTasks;

  @NonNull
  public final TextView statsText;

  @NonNull
  public final SwipeRefreshLayout swipeRefresh;

  private ActivityEnhancedTasksBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnCreateTask, @NonNull Button btnMultiTasks, @NonNull TextView emptyView,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView recyclerViewTasks,
      @NonNull TextView statsText, @NonNull SwipeRefreshLayout swipeRefresh) {
    this.rootView = rootView;
    this.btnCreateTask = btnCreateTask;
    this.btnMultiTasks = btnMultiTasks;
    this.emptyView = emptyView;
    this.progressBar = progressBar;
    this.recyclerViewTasks = recyclerViewTasks;
    this.statsText = statsText;
    this.swipeRefresh = swipeRefresh;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEnhancedTasksBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEnhancedTasksBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_enhanced_tasks, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEnhancedTasksBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCreateTask;
      Button btnCreateTask = ViewBindings.findChildViewById(rootView, id);
      if (btnCreateTask == null) {
        break missingId;
      }

      id = R.id.btnMultiTasks;
      Button btnMultiTasks = ViewBindings.findChildViewById(rootView, id);
      if (btnMultiTasks == null) {
        break missingId;
      }

      id = R.id.emptyView;
      TextView emptyView = ViewBindings.findChildViewById(rootView, id);
      if (emptyView == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerViewTasks;
      RecyclerView recyclerViewTasks = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewTasks == null) {
        break missingId;
      }

      id = R.id.statsText;
      TextView statsText = ViewBindings.findChildViewById(rootView, id);
      if (statsText == null) {
        break missingId;
      }

      id = R.id.swipeRefresh;
      SwipeRefreshLayout swipeRefresh = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefresh == null) {
        break missingId;
      }

      return new ActivityEnhancedTasksBinding((LinearLayout) rootView, btnCreateTask, btnMultiTasks,
          emptyView, progressBar, recyclerViewTasks, statsText, swipeRefresh);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
