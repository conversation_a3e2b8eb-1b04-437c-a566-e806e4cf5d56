# Generated by Django 5.2.4 on 2025-07-20 06:29

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='اسم التصنيف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف التصنيف')),
                ('category_type', models.IntegerField(choices=[(0, 'تصنيف عام للنظام'), (1, 'تصنيف المدير العام'), (2, 'تصنيف المستخدمين'), (3, 'تصنيف المناديب'), (4, 'تصنيف العملاء')], help_text='تحديد نوع التصنيف حسب الاستخدام', verbose_name='نوع التصنيف')),
                ('code', models.CharField(blank=True, help_text='رمز فريد للتصنيف (اختياري)', max_length=50, null=True, unique=True, verbose_name='رمز التصنيف')),
                ('color', models.CharField(default='#007bff', help_text='لون التصنيف في الواجهة (hex color)', max_length=7, verbose_name='لون التصنيف')),
                ('icon', models.CharField(default='fas fa-folder', help_text='أيقونة FontAwesome للتصنيف', max_length=50, verbose_name='أيقونة التصنيف')),
                ('sort_order', models.IntegerField(default=0, help_text='ترتيب التصنيف في القوائم', verbose_name='ترتيب العرض')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_system', models.BooleanField(default=False, help_text='تصنيف نظام لا يمكن حذفه', verbose_name='تصنيف نظام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_categories', to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('parent_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='categories.category', verbose_name='التصنيف الأب')),
            ],
            options={
                'verbose_name': 'تصنيف',
                'verbose_name_plural': 'التصنيفات',
                'db_table': 'categories',
                'ordering': ['category_type', 'sort_order', 'name'],
                'unique_together': {('name', 'category_type', 'parent_category')},
            },
        ),
    ]
