{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
<style>
.visit-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.visit-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.visit-card.verified { border-left-color: #28a745; }
.visit-card.pending { border-left-color: #ffc107; }
.visit-card.rejected { border-left-color: #dc3545; }

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    color: #495057;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 12px 8px;
    text-align: center;
}

.table tbody td {
    padding: 12px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
    font-size: 0.875rem;
    text-align: center;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.search-filters {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.view-toggle {
    background: white;
    border-radius: 8px;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.view-toggle .btn {
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    margin: 0 2px;
}

.view-toggle .btn.active {
    background: #007bff;
    color: white;
}

/* إصلاح الأزرار */
.btn {
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
    min-width: auto;
    white-space: nowrap;
}

.btn i {
    display: inline-block !important;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    margin-right: 0.25rem;
}

.btn-sm {
    min-width: 32px;
    min-height: 32px;
    padding: 0.25rem 0.5rem;
}

.btn-group .btn {
    margin: 0 1px;
}

.btn-action {
    min-width: 80px;
}

.visit-status {
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.status-verified { background: #d4edda; color: #155724; }
.status-pending { background: #fff3cd; color: #856404; }
.status-rejected { background: #f8d7da; color: #721c24; }

.task-badge {
    font-size: 0.75rem;
    padding: 3px 8px;
    border-radius: 12px;
    margin: 2px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-map-marked-alt me-2"></i>
                {{ title }}
            </h1>
            <p class="text-muted mb-0">إدارة شاملة للزيارات مع فلاتر متقدمة وتفاصيل كاملة</p>
        </div>
        <div>
            <a href="{% url 'visits:create_unified_task' %}" class="btn btn-primary me-2">
                <i class="fas fa-plus me-2"></i>
                إنشاء مهمة زيارة
            </a>
            <a href="{% url 'visits:visits_statistics' %}" class="btn btn-outline-primary">
                <i class="fas fa-chart-bar me-2"></i>
                الإحصائيات
            </a>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stats-card">
                <h3 class="mb-1">{{ stats.total_visits }}</h3>
                <p class="mb-0">إجمالي الزيارات</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <h3 class="mb-1">{{ stats.verified_visits }}</h3>
                <p class="mb-0">زيارات مؤكدة</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <h3 class="mb-1">{{ stats.pending_visits }}</h3>
                <p class="mb-0">زيارات معلقة</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <h3 class="mb-1">{{ stats.rejected_visits }}</h3>
                <p class="mb-0">زيارات مرفوضة</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <h3 class="mb-1">{{ stats.task_visits }}</h3>
                <p class="mb-0">مهام زيارات</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <h3 class="mb-1">
                    {% if stats.total_visits > 0 %}
                    {% widthratio stats.verified_visits 1 stats.total_visits as rate %}
                    {% widthratio rate 100 1 %}%
                    {% else %}
                    0%
                    {% endif %}
                </h3>
                <p class="mb-0">معدل التأكيد</p>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <form method="get" class="row g-3">
            <input type="hidden" name="view" value="{{ view_mode }}">
            <div class="col-md-3">
                <label class="form-label">البحث الشامل</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="البحث في العميل، المندوب، المهمة..." 
                       value="{{ search_query }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">حالة الزيارة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    {% for value, display in status_choices %}
                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ display }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">حالة المهمة</label>
                <select name="task_status" class="form-select">
                    <option value="">جميع حالات المهام</option>
                    {% for value, display in task_status_choices %}
                    <option value="{{ value }}" {% if task_status_filter == value %}selected{% endif %}>{{ display }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">المندوب</label>
                <select name="sales_rep" class="form-select">
                    <option value="">جميع المناديب</option>
                    {% for rep in sales_reps %}
                    <option value="{{ rep.id }}" {% if sales_rep_filter == rep.id|stringformat:"s" %}selected{% endif %}>
                        {{ rep.get_full_name|default:rep.username }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">العميل</label>
                <select name="client" class="form-select">
                    <option value="">جميع العملاء</option>
                    {% for client in clients %}
                    <option value="{{ client.id }}" {% if client_filter == client.id|stringformat:"s" %}selected{% endif %}>
                        {{ client.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary d-block w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
        
        <!-- Date Range Filter -->
        <div class="row g-3 mt-2">
            <div class="col-md-2">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">طريقة العرض</label>
                <div class="view-toggle">
                    <a href="?view=cards{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if task_status_filter %}&task_status={{ task_status_filter }}{% endif %}{% if sales_rep_filter %}&sales_rep={{ sales_rep_filter }}{% endif %}{% if client_filter %}&client={{ client_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" 
                       class="btn {% if view_mode == 'cards' %}active{% endif %}">
                        <i class="fas fa-th-large"></i> بطاقات
                    </a>
                    <a href="?view=table{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if task_status_filter %}&task_status={{ task_status_filter }}{% endif %}{% if sales_rep_filter %}&sales_rep={{ sales_rep_filter }}{% endif %}{% if client_filter %}&client={{ client_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" 
                       class="btn {% if view_mode == 'table' %}active{% endif %}">
                        <i class="fas fa-table"></i> جدول
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Visits List -->
    {% if view_mode == 'cards' %}
    <!-- Cards View -->
    <div class="row">
        {% if visits %}
            {% for visit in visits %}
            <div class="col-md-6 col-lg-4">
                <div class="visit-card {{ visit.status }}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <h6 class="mb-1">{{ visit.client.name }}</h6>
                            <small class="text-muted">{{ visit.sales_rep.get_full_name|default:visit.sales_rep.username }}</small>
                        </div>
                        <div>
                            <span class="visit-status status-{{ visit.status }}">{{ visit.get_status_display }}</span>
                        </div>
                    </div>
                    
                    {% if visit.task_title %}
                    <div class="mb-2">
                        <small class="text-primary fw-bold">{{ visit.task_title }}</small>
                    </div>
                    {% endif %}
                    
                    <p class="mb-1">
                        <i class="fas fa-calendar me-2 text-muted"></i>
                        <small>{{ visit.visit_datetime|date:"Y-m-d H:i" }}</small>
                    </p>
                    
                    {% if visit.client.address %}
                    <p class="mb-1">
                        <i class="fas fa-map-marker-alt me-2 text-muted"></i>
                        <small>{{ visit.client.address|truncatechars:30 }}</small>
                    </p>
                    {% endif %}
                    
                    {% if visit.notes %}
                    <p class="mb-2">
                        <i class="fas fa-sticky-note me-2 text-muted"></i>
                        <small>{{ visit.notes|truncatechars:50 }}</small>
                    </p>
                    {% endif %}
                    
                    <div class="d-flex flex-wrap gap-1">
                        <button class="btn btn-outline-primary btn-action btn-sm" onclick="viewVisitDetails('{{ visit.id }}')">
                            <i class="fas fa-eye me-1"></i>
                            تفاصيل
                        </button>
                        {% if visit.visit_image %}
                        <button class="btn btn-outline-info btn-action btn-sm" onclick="viewVisitImage('{{ visit.visit_image.url }}')">
                            <i class="fas fa-image me-1"></i>
                            صورة
                        </button>
                        {% endif %}
                        {% if visit.visit_latitude and visit.visit_longitude %}
                        <a href="https://maps.google.com/?q={{ visit.visit_latitude }},{{ visit.visit_longitude }}" 
                           target="_blank" class="btn btn-outline-success btn-action btn-sm">
                            <i class="fas fa-map me-1"></i>
                            موقع
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-map-marked-alt fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد زيارات</h4>
                <p class="text-muted">لم يتم العثور على زيارات مطابقة للبحث</p>
            </div>
        </div>
        {% endif %}
    </div>
    
    {% else %}
    <!-- Table View -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-table me-2"></i>
                قائمة الزيارات - عرض الجدول
            </h5>
        </div>
        <div class="card-body">
            {% if visits %}
            <div class="table-responsive">
                <table id="visitsTable" class="table">
                    <thead>
                        <tr>
                            <th>رقم الزيارة</th>
                            <th>العميل</th>
                            <th>المندوب</th>
                            <th>المهمة</th>
                            <th>تاريخ الزيارة</th>
                            <th>حالة الزيارة</th>
                            <th>حالة المهمة</th>
                            <th>المسافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for visit in visits %}
                        <tr>
                            <td>
                                <small class="text-muted">{{ visit.id|slice:":8" }}...</small>
                            </td>
                            <td>
                                <strong>{{ visit.client.name }}</strong>
                                {% if visit.client.address %}
                                <br><small class="text-muted">{{ visit.client.address|truncatechars:30 }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex align-items-center justify-content-center">
                                    <div class="me-2">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                            {{ visit.sales_rep.first_name|first|default:visit.sales_rep.username|first }}
                                        </div>
                                    </div>
                                    <div>
                                        <strong>{{ visit.sales_rep.get_full_name|default:visit.sales_rep.username }}</strong>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if visit.task_title %}
                                <strong>{{ visit.task_title }}</strong>
                                {% if visit.task_description %}
                                <br><small class="text-muted">{{ visit.task_description|truncatechars:30 }}</small>
                                {% endif %}
                                {% else %}
                                <span class="text-muted">زيارة عادية</span>
                                {% endif %}
                            </td>
                            <td>
                                {{ visit.visit_datetime|date:"Y-m-d" }}
                                <br><small class="text-muted">{{ visit.visit_datetime|date:"H:i" }}</small>
                            </td>
                            <td>
                                {% if visit.status == 'verified' %}
                                <span class="badge bg-success">{{ visit.get_status_display }}</span>
                                {% elif visit.status == 'pending' %}
                                <span class="badge bg-warning">{{ visit.get_status_display }}</span>
                                {% elif visit.status == 'rejected' %}
                                <span class="badge bg-danger">{{ visit.get_status_display }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ visit.get_status_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if visit.task_status %}
                                {% if visit.task_status == 'completed' %}
                                <span class="badge bg-success">{{ visit.get_task_status_display }}</span>
                                {% elif visit.task_status == 'in_progress' %}
                                <span class="badge bg-primary">{{ visit.get_task_status_display }}</span>
                                {% elif visit.task_status == 'assigned' %}
                                <span class="badge bg-warning">{{ visit.get_task_status_display }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ visit.get_task_status_display }}</span>
                                {% endif %}
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if visit.distance_from_client %}
                                {{ visit.distance_from_client|floatformat:0 }} م
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewVisitDetails('{{ visit.id }}')" title="تفاصيل">
                                        <i class="fas fa-eye me-1"></i>
                                        تفاصيل
                                    </button>
                                    {% if visit.visit_image %}
                                    <button class="btn btn-sm btn-outline-info" onclick="viewVisitImage('{{ visit.visit_image.url }}')" title="صورة">
                                        <i class="fas fa-image me-1"></i>
                                        صورة
                                    </button>
                                    {% endif %}
                                    {% if visit.visit_latitude and visit.visit_longitude %}
                                    <a href="https://maps.google.com/?q={{ visit.visit_latitude }},{{ visit.visit_longitude }}"
                                       target="_blank" class="btn btn-sm btn-outline-success" title="موقع">
                                        <i class="fas fa-map me-1"></i>
                                        موقع
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-map-marked-alt fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد زيارات</h4>
                <p class="text-muted">لم يتم العثور على زيارات مطابقة للبحث</p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if task_status_filter %}&task_status={{ task_status_filter }}{% endif %}{% if sales_rep_filter %}&sales_rep={{ sales_rep_filter }}{% endif %}{% if client_filter %}&client={{ client_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if view_mode %}&view={{ view_mode }}{% endif %}">السابق</a>
            </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
            <li class="page-item active">
                <span class="page-link">{{ num }}</span>
            </li>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
            <li class="page-item">
                <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if task_status_filter %}&task_status={{ task_status_filter }}{% endif %}{% if sales_rep_filter %}&sales_rep={{ sales_rep_filter }}{% endif %}{% if client_filter %}&client={{ client_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if view_mode %}&view={{ view_mode }}{% endif %}">{{ num }}</a>
            </li>
            {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if task_status_filter %}&task_status={{ task_status_filter }}{% endif %}{% if sales_rep_filter %}&sales_rep={{ sales_rep_filter }}{% endif %}{% if client_filter %}&client={{ client_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if view_mode %}&view={{ view_mode }}{% endif %}">التالي</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- Visit Details Modal -->
<div class="modal fade" id="visitDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الزيارة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="visitDetailsContent">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
        </div>
    </div>
</div>

<!-- Visit Image Modal -->
<div class="modal fade" id="visitImageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">صورة الزيارة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="visitImageContent" src="" class="img-fluid" alt="صورة الزيارة">
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل DataTable
    {% if view_mode == 'table' %}
    if (document.getElementById('visitsTable')) {
        $('#visitsTable').DataTable({
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
            },
            responsive: true,
            pageLength: 25,
            order: [[4, 'desc']], // ترتيب حسب تاريخ الزيارة
            columnDefs: [
                { orderable: false, targets: [8] } // عمود الإجراءات غير قابل للترتيب
            ],
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'colvis',
                    text: 'إظهار/إخفاء الأعمدة',
                    className: 'btn btn-outline-secondary btn-sm'
                }
            ]
        });
    }
    {% endif %}
});

function viewVisitDetails(visitId) {
    // عرض تفاصيل الزيارة
    $('#visitDetailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>');
    $('#visitDetailsModal').modal('show');

    // جلب التفاصيل من الخادم
    $.ajax({
        url: `/visits/api/visit-details/${visitId}/`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const visit = response.visit;

                // تحديد لون الحالة
                let statusBadge = '';
                switch(visit.status) {
                    case 'verified':
                        statusBadge = '<span class="badge bg-success">مؤكدة</span>';
                        break;
                    case 'pending':
                        statusBadge = '<span class="badge bg-warning">معلقة</span>';
                        break;
                    case 'rejected':
                        statusBadge = '<span class="badge bg-danger">مرفوضة</span>';
                        break;
                    default:
                        statusBadge = '<span class="badge bg-secondary">' + (visit.status_display || 'غير محدد') + '</span>';
                }

                // تحديد لون الأولوية
                let priorityBadge = '';
                if (visit.priority) {
                    switch(visit.priority) {
                        case 'urgent':
                            priorityBadge = '<span class="badge bg-danger">عاجل</span>';
                            break;
                        case 'high':
                            priorityBadge = '<span class="badge bg-warning">عالي</span>';
                            break;
                        case 'medium':
                            priorityBadge = '<span class="badge bg-info">متوسط</span>';
                            break;
                        case 'low':
                            priorityBadge = '<span class="badge bg-success">منخفض</span>';
                            break;
                        default:
                            priorityBadge = '<span class="badge bg-secondary">' + (visit.priority_display || 'غير محدد') + '</span>';
                    }
                }

                let detailsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات الزيارة</h6>
                            <div class="mb-3">
                                <p><strong>رقم الزيارة:</strong> ${visit.id.substring(0, 8)}...</p>
                                <p><strong>التاريخ والوقت:</strong> ${visit.visit_datetime || 'غير محدد'}</p>
                                <p><strong>الحالة:</strong> ${statusBadge}</p>
                                ${visit.barcode_scanned ? '<p><strong>الباركود:</strong> ' + visit.barcode_scanned + '</p>' : ''}
                                ${visit.distance_from_client ? '<p><strong>المسافة من العميل:</strong> ' + visit.distance_from_client + ' متر</p>' : ''}
                                ${priorityBadge ? '<p><strong>الأولوية:</strong> ' + priorityBadge + '</p>' : ''}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-building me-2"></i>معلومات العميل</h6>
                            <div class="mb-3">
                                ${visit.client ? `
                                    <p><strong>اسم العميل:</strong> ${visit.client.name}</p>
                                    <p><strong>العنوان:</strong> ${visit.client.address}</p>
                                    ${visit.client.phone_number ? '<p><strong>الهاتف:</strong> ' + visit.client.phone_number + '</p>' : ''}
                                    ${visit.client.email ? '<p><strong>البريد:</strong> ' + visit.client.email + '</p>' : ''}
                                ` : '<p class="text-muted">لا توجد معلومات عميل</p>'}
                            </div>
                        </div>
                    </div>

                    ${visit.sales_rep ? `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-user me-2"></i>معلومات المندوب</h6>
                            <div class="mb-3">
                                <p><strong>اسم المندوب:</strong> ${visit.sales_rep.name}</p>
                                <p><strong>اسم المستخدم:</strong> ${visit.sales_rep.username}</p>
                                ${visit.sales_rep.phone_number ? '<p><strong>الهاتف:</strong> ' + visit.sales_rep.phone_number + '</p>' : ''}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-clock me-2"></i>تواريخ مهمة</h6>
                            <div class="mb-3">
                                ${visit.assigned_at ? '<p><strong>تاريخ التكليف:</strong> ' + visit.assigned_at + '</p>' : ''}
                                ${visit.due_date ? '<p><strong>تاريخ الاستحقاق:</strong> ' + visit.due_date + '</p>' : ''}
                                ${visit.verified_at ? '<p><strong>تاريخ التحقق:</strong> ' + visit.verified_at + '</p>' : ''}
                                ${visit.verified_by ? '<p><strong>تم التحقق بواسطة:</strong> ' + visit.verified_by + '</p>' : ''}
                            </div>
                        </div>
                    </div>
                    ` : ''}

                    ${visit.task_title || visit.task_description ? `
                    <hr>
                    <h6><i class="fas fa-tasks me-2"></i>تفاصيل المهمة</h6>
                    <div class="mb-3">
                        ${visit.task_title ? '<p><strong>عنوان المهمة:</strong> ' + visit.task_title + '</p>' : ''}
                        ${visit.task_description ? '<p><strong>وصف المهمة:</strong> ' + visit.task_description + '</p>' : ''}
                        ${visit.task_status_display ? '<p><strong>حالة المهمة:</strong> <span class="badge bg-info">' + visit.task_status_display + '</span></p>' : ''}
                    </div>
                    ` : ''}

                    <hr>
                    <h6><i class="fas fa-sticky-note me-2"></i>ملاحظات الزيارة</h6>
                    <div class="mb-3">
                        <p class="border p-3 rounded bg-light">${visit.notes || 'لا توجد ملاحظات'}</p>
                    </div>

                    ${visit.rejection_reason ? `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>سبب الرفض</h6>
                        <p class="mb-0">${visit.rejection_reason}</p>
                    </div>
                    ` : ''}

                    ${visit.visit_image ? `
                    <hr>
                    <h6><i class="fas fa-image me-2"></i>صورة الزيارة</h6>
                    <div class="text-center mb-3">
                        <img src="${visit.visit_image}" class="img-fluid rounded" style="max-height: 300px;" alt="صورة الزيارة">
                    </div>
                    ` : ''}

                    ${visit.visit_latitude && visit.visit_longitude ? `
                    <hr>
                    <h6><i class="fas fa-map-marker-alt me-2"></i>موقع الزيارة</h6>
                    <div class="mb-3">
                        <p><strong>الإحداثيات:</strong> ${visit.visit_latitude}, ${visit.visit_longitude}</p>
                        <a href="https://maps.google.com/?q=${visit.visit_latitude},${visit.visit_longitude}"
                           target="_blank" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-external-link-alt me-1"></i>
                            عرض على الخريطة
                        </a>
                    </div>
                    ` : ''}
                `;

                $('#visitDetailsContent').html(detailsHtml);
            } else {
                $('#visitDetailsContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        خطأ في تحميل التفاصيل: ${response.error || 'خطأ غير معروف'}
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            $('#visitDetailsContent').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    خطأ في الاتصال بالخادم: ${error}
                </div>
            `);
        }
    });
}

function viewVisitImage(imageUrl) {
    $('#visitImageContent').attr('src', imageUrl);
    $('#visitImageModal').modal('show');
}
</script>
{% endblock %}
