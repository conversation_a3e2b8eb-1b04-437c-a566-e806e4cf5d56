<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <!-- Task Title -->
            <TextView
                android:id="@+id/textTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="عنوان المهمة"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:maxLines="2"
                android:ellipsize="end" />

            <!-- Priority Badge -->
            <TextView
                android:id="@+id/textPriority"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="متوسط"
                android:textSize="12sp"
                android:textStyle="bold"
                android:background="@drawable/badge_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- Client Name -->
        <TextView
            android:id="@+id/textClient"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="العميل: اسم العميل"
            android:textSize="14sp"
            android:textColor="#666666"
            android:layout_marginBottom="4dp"
            android:drawableStart="@drawable/ic_business"
            android:drawablePadding="8dp"
            android:gravity="center_vertical" />

        <!-- Description -->
        <TextView
            android:id="@+id/textDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="وصف المهمة"
            android:textSize="14sp"
            android:textColor="#666666"
            android:layout_marginBottom="8dp"
            android:maxLines="3"
            android:ellipsize="end"
            android:visibility="gone" />

        <!-- Status Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="الحالة: "
                android:textSize="12sp"
                android:textColor="#999999" />

            <TextView
                android:id="@+id/textStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="مُكلف"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Dates -->
        <TextView
            android:id="@+id/textAssignedAt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="تاريخ التكليف: 2025-01-15 10:30"
            android:textSize="12sp"
            android:textColor="#999999"
            android:layout_marginBottom="2dp"
            android:drawableStart="@drawable/ic_schedule"
            android:drawablePadding="4dp"
            android:gravity="center_vertical" />

        <TextView
            android:id="@+id/textDueDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="الموعد المحدد: 2025-01-16 17:00"
            android:textSize="12sp"
            android:textColor="#999999"
            android:layout_marginBottom="2dp"
            android:drawableStart="@drawable/ic_alarm"
            android:drawablePadding="4dp"
            android:gravity="center_vertical"
            android:visibility="gone" />

        <!-- Assigned By -->
        <TextView
            android:id="@+id/textAssignedBy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="كُلف من: اسم المدير"
            android:textSize="12sp"
            android:textColor="#999999"
            android:layout_marginBottom="12dp"
            android:drawableStart="@drawable/ic_person"
            android:drawablePadding="4dp"
            android:gravity="center_vertical" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <Button
                android:id="@+id/btnAcknowledge"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="تأكيد الاطلاع"
                android:textSize="12sp"
                android:background="@drawable/button_acknowledge"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp"
                android:paddingHorizontal="16dp"
                android:visibility="gone" />

            <Button
                android:id="@+id/btnStart"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="بدء التنفيذ"
                android:textSize="12sp"
                android:background="@drawable/button_start"
                android:textColor="@android:color/white"
                android:paddingHorizontal="16dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
