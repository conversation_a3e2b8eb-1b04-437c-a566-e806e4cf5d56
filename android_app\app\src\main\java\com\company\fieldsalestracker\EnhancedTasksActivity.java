package com.company.fieldsalestracker;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import java.util.ArrayList;
import java.util.List;

public class EnhancedTasksActivity extends AppCompatActivity {
    private static final String TAG = "EnhancedTasksActivity";
    
    private RecyclerView recyclerView;
    private EnhancedTaskAdapter adapter;
    private ProgressBar progressBar;
    private TextView emptyView;
    private SwipeRefreshLayout swipeRefresh;
    private TextView statsText;
    private Button btnMultiTasks;
    private Button btnCreateTask;
    
    private ApiService apiService;
    private SharedPreferences sharedPreferences;
    private List<ApiService.EnhancedVisit> tasksList = new ArrayList<>();
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_enhanced_tasks);
        
        initViews();
        setupRecyclerView();
        setupApiService();
        loadTasks();
    }
    
    private void initViews() {
        recyclerView = findViewById(R.id.recyclerViewTasks);
        progressBar = findViewById(R.id.progressBar);
        emptyView = findViewById(R.id.emptyView);
        swipeRefresh = findViewById(R.id.swipeRefresh);
        statsText = findViewById(R.id.statsText);
        btnMultiTasks = findViewById(R.id.btnMultiTasks);
        btnCreateTask = findViewById(R.id.btnCreateTask);
        
        // Setup toolbar
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("المهام المحسنة");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        // Setup click listeners
        swipeRefresh.setOnRefreshListener(this::loadTasks);
        
        btnMultiTasks.setOnClickListener(v -> {
            Intent intent = new Intent(this, MultiTasksActivity.class);
            startActivity(intent);
        });
        
        btnCreateTask.setOnClickListener(v -> {
            // يمكن إضافة Activity لإنشاء المهام لاحقاً
            Toast.makeText(this, "إنشاء المهام متاح من الويب فقط حالياً", Toast.LENGTH_SHORT).show();
        });
    }
    
    private void setupRecyclerView() {
        adapter = new EnhancedTaskAdapter(tasksList, this::onTaskClick);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }
    
    private void setupApiService() {
        apiService = ApiClient.getClient().create(ApiService.class);
        sharedPreferences = getSharedPreferences("app_prefs", MODE_PRIVATE);
    }
    
    private void loadTasks() {
        showLoading(true);
        
        Call<ApiService.EnhancedVisitsResponse> call = apiService.getEnhancedVisits(
            null, // search
            null, // status
            "assigned,in_progress", // task_status - المهام النشطة فقط
            null, // sales_rep
            null, // client
            null, // date_from
            null, // date_to
            "table", // view
            1 // page
        );
        
        call.enqueue(new Callback<ApiService.EnhancedVisitsResponse>() {
            @Override
            public void onResponse(Call<ApiService.EnhancedVisitsResponse> call, 
                                 Response<ApiService.EnhancedVisitsResponse> response) {
                showLoading(false);
                
                if (response.isSuccessful() && response.body() != null) {
                    ApiService.EnhancedVisitsResponse tasksResponse = response.body();
                    
                    if (tasksResponse.success) {
                        tasksList.clear();
                        
                        // فلترة المهام فقط (ليس الزيارات العادية)
                        for (ApiService.EnhancedVisit visit : tasksResponse.visits) {
                            if (visit.task_title != null && !visit.task_title.isEmpty()) {
                                tasksList.add(visit);
                            }
                        }
                        
                        adapter.notifyDataSetChanged();
                        updateUI();
                        updateStats(tasksResponse.stats);
                        
                        Log.d(TAG, "تم تحميل " + tasksList.size() + " مهمة");
                    } else {
                        showError("فشل في تحميل المهام");
                    }
                } else {
                    showError("خطأ في الاتصال: " + response.code());
                }
            }
            
            @Override
            public void onFailure(Call<ApiService.EnhancedVisitsResponse> call, Throwable t) {
                showLoading(false);
                showError("خطأ في الشبكة: " + t.getMessage());
                Log.e(TAG, "خطأ في تحميل المهام", t);
            }
        });
    }
    
    private void onTaskClick(ApiService.EnhancedVisit task) {
        Intent intent = new Intent(this, EnhancedTaskDetailsActivity.class);
        intent.putExtra("task_id", task.id);
        intent.putExtra("task_title", task.task_title);
        intent.putExtra("is_parent_task", task.is_parent_task);
        intent.putExtra("is_recurring", task.is_recurring_task);
        startActivity(intent);
    }
    
    private void showLoading(boolean show) {
        progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        swipeRefresh.setRefreshing(false);
    }
    
    private void updateUI() {
        if (tasksList.isEmpty()) {
            recyclerView.setVisibility(View.GONE);
            emptyView.setVisibility(View.VISIBLE);
            emptyView.setText("لا توجد مهام حالياً");
        } else {
            recyclerView.setVisibility(View.VISIBLE);
            emptyView.setVisibility(View.GONE);
        }
    }
    
    private void updateStats(ApiService.VisitStats stats) {
        if (stats != null) {
            String statsMessage = String.format(
                "إجمالي المهام: %d | مهام الزيارات: %d",
                stats.total_visits,
                stats.task_visits
            );
            statsText.setText(statsMessage);
            statsText.setVisibility(View.VISIBLE);
        } else {
            statsText.setVisibility(View.GONE);
        }
    }
    
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
        Log.e(TAG, message);
    }
    
    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // إعادة تحميل المهام عند العودة للنشاط
        loadTasks();
    }
}
