package com.company.fieldsalestracker;

import android.util.Log;

import com.google.android.datatransport.backend.cct.BuildConfig;

public class AppLogger {
    private static final boolean isDebug = BuildConfig.DEBUG;

    private AppLogger() {
        // Private constructor to prevent instantiation
    }

    public static void d(String tag, String message) {
        if (isDebug) {
            Log.d(tag, message);
        }
    }

    public static void v(String tag, String message) {
        if (isDebug) {
            Log.v(tag, message);
        }
    }

    public static void i(String tag, String message) {
        if (isDebug) {
            Log.i(tag, message);
        }
    }

    public static void w(String tag, String message) {
        if (isDebug) {
            Log.w(tag, message);
        }
    }

    public static void e(String tag, String message) {
        if (isDebug) {
            Log.e(tag, message);
        }
    }

    // Generic log method for backward compatibility
    public static void log(String message) {
        if (isDebug) {
            Log.d("FieldSalesTracker", message);
        }
    }

    public static void log(String tag, String message) {
        if (isDebug) {
            Log.d(tag, message);
        }
    }
}