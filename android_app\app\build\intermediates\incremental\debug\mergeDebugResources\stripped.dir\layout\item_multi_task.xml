<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <!-- Task Type Icon -->
            <ImageView
                android:id="@+id/taskTypeIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_multi_task"
                android:layout_marginEnd="8dp" />

            <!-- Title -->
            <TextView
                android:id="@+id/titleText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="عنوان المهمة"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black" />

            <!-- Status Icon -->
            <ImageView
                android:id="@+id/statusIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_help"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- Sales Rep -->
        <TextView
            android:id="@+id/salesRepText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="المندوب"
            android:textSize="14sp"
            android:textColor="@color/colorPrimary"
            android:layout_marginBottom="4dp"
            android:drawableStart="@drawable/ic_person"
            android:drawablePadding="8dp" />

        <!-- Task Type -->
        <TextView
            android:id="@+id/taskTypeText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="نوع المهمة"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginBottom="4dp" />

        <!-- Status -->
        <TextView
            android:id="@+id/statusText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="الحالة"
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <!-- Progress Bar -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/progressText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="التقدم"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="4dp" />

            <ProgressBar
                android:id="@+id/progressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:max="100"
                android:progress="0" />

        </LinearLayout>

        <!-- Next Execution -->
        <TextView
            android:id="@+id/nextExecutionText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="التنفيذ التالي"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginBottom="4dp"
            android:drawableStart="@drawable/ic_schedule"
            android:drawablePadding="8dp" />

        <!-- Executions Count -->
        <TextView
            android:id="@+id/executionsText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="عدد التنفيذات"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginBottom="12dp" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <Button
                android:id="@+id/detailsButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="تفاصيل"
                android:textSize="12sp"
                android:background="@drawable/button_acknowledge"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/executeButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="تنفيذ"
                android:textSize="12sp"
                android:background="@drawable/button_start"
                android:textColor="@android:color/white" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
