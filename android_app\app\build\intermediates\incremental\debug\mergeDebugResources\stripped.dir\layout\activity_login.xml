<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/login_background"
    tools:context=".LoginActivity">

    <!-- Logo and Title -->
    <LinearLayout
        android:id="@+id/logoContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/loginCard"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@drawable/ic_launcher"
            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/app_name"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/login_title"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Login Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/loginCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="32dp">

            <!-- Username Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilUsername"
                style="@style/CustomTextInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/username"
                app:startIconDrawable="@drawable/ic_person">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etUsername"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Password Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilPassword"
                style="@style/CustomTextInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="@string/password"
                app:endIconMode="password_toggle"
                app:startIconDrawable="@drawable/ic_lock">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Login Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnLogin"
                style="@style/CustomButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/login_button" />

            <!-- Demo Accounts Info -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:gravity="center"
                android:text="حسابات تجريبية:\nadmin / admin123\nrep1 / rep123"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>
