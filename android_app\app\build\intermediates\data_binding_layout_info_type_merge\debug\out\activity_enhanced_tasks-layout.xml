<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_enhanced_tasks" modulePackage="com.company.fieldsalestracker" filePath="app\src\main\res\layout\activity_enhanced_tasks.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_enhanced_tasks_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="100" endOffset="14"/></Target><Target id="@+id/btnMultiTasks" view="Button"><Expressions/><location startLine="27" startOffset="8" endLine="34" endOffset="44"/></Target><Target id="@+id/btnCreateTask" view="Button"><Expressions/><location startLine="36" startOffset="8" endLine="42" endOffset="57"/></Target><Target id="@+id/statsText" view="TextView"><Expressions/><location startLine="47" startOffset="4" endLine="56" endOffset="35"/></Target><Target id="@+id/swipeRefresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="59" startOffset="4" endLine="98" endOffset="59"/></Target><Target id="@+id/recyclerViewTasks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="70" startOffset="12" endLine="75" endOffset="47"/></Target><Target id="@+id/emptyView" view="TextView"><Expressions/><location startLine="78" startOffset="12" endLine="86" endOffset="43"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="89" startOffset="12" endLine="94" endOffset="43"/></Target></Targets></Layout>