from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import User, UserHierarchy
from categories.models import Category
import uuid


class Command(BaseCommand):
    help = 'ترحيل المستخدمين الموجودين للنظام الهرمي الجديد'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='عرض التغييرات بدون تطبيقها',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('تشغيل تجريبي - لن يتم حفظ التغييرات'))
        
        self.stdout.write(self.style.SUCCESS('بدء ترحيل المستخدمين للنظام الجديد...'))
        
        with transaction.atomic():
            # إنشاء التصنيفات الافتراضية إذا لم تكن موجودة
            self.create_default_categories(dry_run)
            
            # ترحيل المستخدمين الموجودين
            self.migrate_existing_users(dry_run)
            
            # إنشاء العلاقات الهرمية
            self.create_hierarchy_relationships(dry_run)
            
            if dry_run:
                # إلغاء المعاملة في حالة التشغيل التجريبي
                transaction.set_rollback(True)
        
        self.stdout.write(self.style.SUCCESS('تم الانتهاء من الترحيل بنجاح!'))

    def create_default_categories(self, dry_run):
        """إنشاء التصنيفات الافتراضية"""
        self.stdout.write('إنشاء التصنيفات الافتراضية...')
        
        if not dry_run:
            categories = Category.create_default_categories()
            self.stdout.write(f'تم إنشاء {len(categories)} تصنيف افتراضي')
        else:
            self.stdout.write('سيتم إنشاء التصنيفات الافتراضية')

    def migrate_existing_users(self, dry_run):
        """ترحيل المستخدمين الموجودين"""
        self.stdout.write('ترحيل المستخدمين الموجودين...')
        
        users = User.objects.all()
        migrated_count = 0
        
        for user in users:
            old_role_type = None
            
            # تحديد نوع الدور الجديد بناءً على الخصائص الحالية
            if user.is_superuser:
                new_role_type = 1  # مدير عام
            elif hasattr(user, 'role') and user.role:
                if user.role.name in ['super_manager']:
                    new_role_type = 1  # مدير عام
                elif user.role.name in ['regional_manager', 'branch_manager', 'sales_supervisor']:
                    new_role_type = 2  # مستخدم/مدير
                elif user.role.name == 'sales_rep':
                    new_role_type = 3  # مندوب مبيعات
                else:
                    new_role_type = 3  # افتراضي: مندوب مبيعات
            elif user.is_staff:
                new_role_type = 2  # مستخدم/مدير
            else:
                new_role_type = 3  # مندوب مبيعات
            
            if not dry_run:
                # تحديث نوع الدور
                user.role_type = new_role_type
                
                # تطبيق الصلاحيات الافتراضية
                user.apply_default_permissions()
                
                # تحديث صلاحيات الموقع
                if new_role_type == 3:  # مندوب مبيعات
                    user.is_staff = False
                    user.is_superuser = False
                elif new_role_type == 1:  # مدير عام
                    user.is_staff = True
                    user.is_superuser = True
                else:  # مستخدم/مدير
                    user.is_staff = True
                    user.is_superuser = False
                
                user.save()
                migrated_count += 1
            
            self.stdout.write(f'  - {user.username}: {user.get_role_type_display()}')
        
        if not dry_run:
            self.stdout.write(f'تم ترحيل {migrated_count} مستخدم')
        else:
            self.stdout.write(f'سيتم ترحيل {users.count()} مستخدم')

    def create_hierarchy_relationships(self, dry_run):
        """إنشاء العلاقات الهرمية الأساسية"""
        self.stdout.write('إنشاء العلاقات الهرمية...')
        
        if dry_run:
            self.stdout.write('سيتم إنشاء العلاقات الهرمية الأساسية')
            return
        
        # الحصول على التصنيفات
        try:
            region_mgr_category = Category.objects.get(code='REGION_MGR')
            branch_mgr_category = Category.objects.get(code='BRANCH_MGR')
            senior_reps_category = Category.objects.get(code='SENIOR_REPS')
            regular_reps_category = Category.objects.get(code='REGULAR_REPS')
            new_reps_category = Category.objects.get(code='NEW_REPS')
        except Category.DoesNotExist:
            self.stdout.write(self.style.ERROR('التصنيفات الافتراضية غير موجودة'))
            return
        
        # الحصول على المدير العام
        super_managers = User.objects.filter(role_type=1)
        if not super_managers.exists():
            self.stdout.write(self.style.WARNING('لا يوجد مدير عام'))
            return
        
        super_manager = super_managers.first()
        
        # تعيين المدراء للتصنيفات
        user_managers = User.objects.filter(role_type=2)
        for i, manager in enumerate(user_managers):
            if i == 0:
                # المدير الأول يدير تصنيف مدراء المناطق
                manager.add_category(region_mgr_category.id)
                UserHierarchy.create_category_management(
                    manager=super_manager,
                    category=region_mgr_category
                )
            elif i == 1:
                # المدير الثاني يدير تصنيف مدراء الفروع
                manager.add_category(branch_mgr_category.id)
                UserHierarchy.create_category_management(
                    manager=super_manager,
                    category=branch_mgr_category
                )
        
        # تعيين المناديب للتصنيفات
        sales_reps = User.objects.filter(role_type=3)
        for i, rep in enumerate(sales_reps):
            if i % 3 == 0:
                rep.add_category(senior_reps_category.id)
            elif i % 3 == 1:
                rep.add_category(regular_reps_category.id)
            else:
                rep.add_category(new_reps_category.id)
        
        # إنشاء علاقات إدارة التصنيفات
        for manager in user_managers:
            # استخدام get_or_create لتجنب التكرار
            UserHierarchy.objects.get_or_create(
                manager=manager,
                category=senior_reps_category,
                relationship_type='category_management',
                defaults={'is_active': True}
            )
            UserHierarchy.objects.get_or_create(
                manager=manager,
                category=regular_reps_category,
                relationship_type='category_management',
                defaults={'is_active': True}
            )
            UserHierarchy.objects.get_or_create(
                manager=manager,
                category=new_reps_category,
                relationship_type='category_management',
                defaults={'is_active': True}
            )
        
        self.stdout.write('تم إنشاء العلاقات الهرمية الأساسية')

    def display_migration_summary(self):
        """عرض ملخص الترحيل"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write('ملخص الترحيل:')
        self.stdout.write('='*50)
        
        # إحصائيات المستخدمين
        super_managers = User.objects.filter(role_type=1).count()
        user_managers = User.objects.filter(role_type=2).count()
        sales_reps = User.objects.filter(role_type=3).count()
        
        self.stdout.write(f'المدراء العامين: {super_managers}')
        self.stdout.write(f'المستخدمين/المدراء: {user_managers}')
        self.stdout.write(f'مناديب المبيعات: {sales_reps}')
        
        # إحصائيات التصنيفات
        total_categories = Category.objects.filter(is_active=True).count()
        self.stdout.write(f'إجمالي التصنيفات: {total_categories}')
        
        # إحصائيات العلاقات الهرمية
        total_hierarchies = UserHierarchy.objects.filter(is_active=True).count()
        self.stdout.write(f'إجمالي العلاقات الهرمية: {total_hierarchies}')
        
        self.stdout.write('='*50)
