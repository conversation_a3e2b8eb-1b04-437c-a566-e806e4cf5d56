#Sat Jul 26 03:18:32 AST 2025
com.company.fieldsalestracker.app-main-56\:/anim/button_scale_down.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\anim_button_scale_down.xml.flat
com.company.fieldsalestracker.app-main-56\:/anim/button_scale_up.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\anim_button_scale_up.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/badge_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_badge_background.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/btn_camera_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_btn_camera_background.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/btn_submit_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_btn_submit_background.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/button_acknowledge.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_acknowledge.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/button_scanner.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_scanner.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/button_start.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_start.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/circle_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_alarm.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_alarm.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_arrow_back.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_arrow_back.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_arrow_forward.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_arrow_forward.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_assignment.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_assignment.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_business.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_business.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_camera.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_camera.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_camera_enhanced.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_camera_enhanced.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_check_circle.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_check_circle.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_enhanced_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_enhanced_task.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_event.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_event.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_group.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_group.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_help.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_help.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_launcher_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_background.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_launcher_foreground.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_foreground.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_launcher_simple.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_simple.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_list.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_list.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_location.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_location.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_location_on.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_location_on.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_lock.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_lock.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_logout.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_logout.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_multi_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_multi_task.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_person.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_play_arrow.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_play_arrow.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_priority_high.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_priority_high.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_priority_low.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_priority_low.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_priority_medium.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_priority_medium.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_qr_code_scanner.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_qr_code_scanner.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_refresh.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_refresh.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_repeat.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_repeat.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_schedule.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_schedule.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_send.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_send.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_send_enhanced.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_send_enhanced.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_subdirectory_arrow_right.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_subdirectory_arrow_right.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/ic_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_task.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/login_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_login_background.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/scanner_overlay.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_scanner_overlay.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/splash_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_splash_background.xml.flat
com.company.fieldsalestracker.app-main-56\:/drawable/status_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_status_background.xml.flat
com.company.fieldsalestracker.app-main-56\:/menu/main_menu.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\menu_main_menu.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-hdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-hdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-mdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-mdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-xhdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-xhdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-xxhdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-xxhdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-xxxhdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-56\:/mipmap-xxxhdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-56\:/xml/backup_rules.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.company.fieldsalestracker.app-main-56\:/xml/data_extraction_rules.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
com.company.fieldsalestracker.app-main-56\:/xml/file_paths.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\xml_file_paths.xml.flat
com.company.fieldsalestracker.app-main-56\:/xml/network_security_config.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\xml_network_security_config.xml.flat
com.company.fieldsalestracker.app-main-57\:/anim/button_scale_down.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\anim_button_scale_down.xml.flat
com.company.fieldsalestracker.app-main-57\:/anim/button_scale_up.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\anim_button_scale_up.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/badge_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_badge_background.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/btn_camera_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_btn_camera_background.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/btn_submit_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_btn_submit_background.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/button_acknowledge.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_acknowledge.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/button_scanner.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_scanner.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/button_start.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_start.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/circle_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_alarm.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_alarm.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_arrow_back.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_arrow_back.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_arrow_forward.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_arrow_forward.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_assignment.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_assignment.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_business.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_business.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_camera.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_camera.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_camera_enhanced.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_camera_enhanced.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_check_circle.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_check_circle.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_enhanced_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_enhanced_task.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_event.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_event.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_group.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_group.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_help.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_help.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_launcher_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_background.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_launcher_foreground.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_foreground.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_launcher_simple.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_simple.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_list.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_list.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_location.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_location.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_location_on.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_location_on.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_lock.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_lock.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_logout.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_logout.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_multi_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_multi_task.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_person.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_play_arrow.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_play_arrow.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_priority_high.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_priority_high.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_priority_low.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_priority_low.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_priority_medium.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_priority_medium.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_qr_code_scanner.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_qr_code_scanner.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_refresh.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_refresh.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_repeat.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_repeat.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_schedule.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_schedule.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_send.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_send.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_send_enhanced.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_send_enhanced.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_subdirectory_arrow_right.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_subdirectory_arrow_right.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/ic_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_task.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/login_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_login_background.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/scanner_overlay.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_scanner_overlay.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/splash_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_splash_background.xml.flat
com.company.fieldsalestracker.app-main-57\:/drawable/status_background.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\drawable_status_background.xml.flat
com.company.fieldsalestracker.app-main-57\:/menu/main_menu.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\menu_main_menu.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-hdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-hdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-mdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-mdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-xhdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-xhdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-xxhdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-xxhdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-xxxhdpi/ic_launcher.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher.xml.flat
com.company.fieldsalestracker.app-main-57\:/mipmap-xxxhdpi/ic_launcher_round.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher_round.xml.flat
com.company.fieldsalestracker.app-main-57\:/xml/backup_rules.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.company.fieldsalestracker.app-main-57\:/xml/data_extraction_rules.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
com.company.fieldsalestracker.app-main-57\:/xml/file_paths.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\xml_file_paths.xml.flat
com.company.fieldsalestracker.app-main-57\:/xml/network_security_config.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\xml_network_security_config.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/activity_barcode_scanner.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_barcode_scanner.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/activity_enhanced_tasks.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_enhanced_tasks.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/activity_login.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_login.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/activity_main.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/activity_multi_tasks.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_multi_tasks.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/activity_my_visits.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_my_visits.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/activity_splash.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_splash.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/activity_task_details.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_task_details.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/activity_tasks.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_tasks.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/activity_visit_details.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_visit_details.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/item_client.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_item_client.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/item_enhanced_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_item_enhanced_task.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/item_multi_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_item_multi_task.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/item_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_item_task.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-53\:/layout/item_visit.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_item_visit.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/activity_barcode_scanner.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_barcode_scanner.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/activity_enhanced_tasks.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_enhanced_tasks.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/activity_login.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_login.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/activity_main.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/activity_multi_tasks.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_multi_tasks.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/activity_my_visits.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_my_visits.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/activity_splash.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_splash.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/activity_task_details.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_task_details.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/activity_tasks.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_tasks.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/activity_visit_details.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_visit_details.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/item_client.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_item_client.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/item_enhanced_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_item_enhanced_task.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/item_multi_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_item_multi_task.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/item_task.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_item_task.xml.flat
com.company.fieldsalestracker.app-mergeDebugResources-54\:/layout/item_visit.xml=D\:\\django_project\\TrackCustomer\\android_app\\app\\build\\intermediates\\merged_res\\debug\\layout_item_visit.xml.flat
