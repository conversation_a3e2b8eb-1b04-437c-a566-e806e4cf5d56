# النظام الهرمي الجديد - نظام مراقبة المناديب

## نظرة عامة

تم تطوير نظام هرمي جديد ومرن لإدارة المستخدمين والصلاحيات في نظام مراقبة المناديب. يوفر هذا النظام مرونة أكبر في إدارة العلاقات الهرمية والتصنيفات.

## الميزات الرئيسية

### 1. نظام الأدوار الجديد
- **مدير عام (Super Manager)**: صلاحيات كاملة على النظام
- **مستخدم/مدير (User Manager)**: صلاحيات إدارية محدودة
- **مندوب مبيعات (Sales Rep)**: وصول للتطبيق المحمول فقط

### 2. نظام التصنيفات المرن
خمسة أنواع تصنيفات:
- **تصنيفات النظام (System Categories)**: للإعدادات الأساسية
- **تصنيفات المستخدمين (User Categories)**: لتصنيف المدراء
- **تصنيفات المناديب (Sales Rep Categories)**: لتصنيف مناديب المبيعات
- **تصنيفات العملاء (Client Categories)**: لتصنيف العملاء
- **تصنيفات المؤسسات (Organization Categories)**: للهيكل التنظيمي

### 3. العلاقات الهرمية المرنة
- **الإدارة المباشرة**: علاقة مباشرة بين مدير ومُدار
- **الإدارة عبر التصنيف**: إدارة مجموعة من المستخدمين عبر التصنيف
- **التكليف المؤقت**: تكليفات مؤقتة للمشاريع

### 4. نظام الصلاحيات المتقدم
- صلاحيات افتراضية حسب نوع الدور
- صلاحيات مخصصة قابلة للتعديل
- نظام وراثة الصلاحيات

## البنية التقنية

### النماذج الرئيسية

#### User Model (محدث)
```python
class User(AbstractUser):
    # الأدوار الجديدة
    ROLE_TYPES = [
        (1, 'مدير عام'),
        (2, 'مستخدم'),
        (3, 'مندوب مبيعات'),
    ]
    
    role_type = models.IntegerField(choices=ROLE_TYPES, default=3)
    categories = models.JSONField(default=list)
    direct_managers = models.JSONField(default=list)
    managed_users = models.JSONField(default=list)
    managed_categories = models.JSONField(default=list)
    permissions = models.JSONField(default=dict)
```

#### Category Model (جديد)
```python
class Category(models.Model):
    CATEGORY_TYPES = [
        (0, 'تصنيفات النظام'),
        (1, 'تصنيفات المستخدمين'),
        (2, 'تصنيفات المناديب'),
        (3, 'تصنيفات العملاء'),
        (4, 'تصنيفات المؤسسات'),
    ]
    
    name = models.CharField(max_length=100)
    category_type = models.IntegerField(choices=CATEGORY_TYPES)
    parent_category = models.ForeignKey('self', null=True, blank=True)
    # ... المزيد من الحقول
```

#### UserHierarchy Model (جديد)
```python
class UserHierarchy(models.Model):
    RELATIONSHIP_TYPES = [
        ('direct_management', 'إدارة مباشرة'),
        ('category_management', 'إدارة عبر التصنيف'),
        ('temporary_assignment', 'تكليف مؤقت'),
    ]
    
    manager = models.ForeignKey(User, related_name='hierarchy_as_manager')
    subordinate = models.ForeignKey(User, null=True, blank=True)
    category = models.ForeignKey(Category, null=True, blank=True)
    relationship_type = models.CharField(max_length=50, choices=RELATIONSHIP_TYPES)
```

## التصنيفات الافتراضية

### تصنيفات المستخدمين
- **مدراء المناطق (REGION_MGR)**: إدارة المناطق الجغرافية
- **مدراء الفروع (BRANCH_MGR)**: إدارة الفروع المحلية

### تصنيفات المناديب
- **مناديب كبار (SENIOR_REPS)**: مناديب ذوي خبرة عالية
- **مناديب عاديين (REGULAR_REPS)**: مناديب بخبرة متوسطة
- **مناديب جدد (NEW_REPS)**: مناديب جدد في التدريب

### تصنيفات العملاء
- **عملاء VIP (VIP_CLIENTS)**: عملاء مهمين
- **عملاء كبار (MAJOR_CLIENTS)**: عملاء بحجم تعامل كبير
- **عملاء عاديين (REGULAR_CLIENTS)**: عملاء عاديين

## واجهات الإدارة

### 1. لوحة تحكم التصنيفات
- **الرابط**: `/categories/`
- **الوصول**: المدير العام فقط
- **الميزات**:
  - عرض جميع التصنيفات حسب النوع
  - إنشاء وتعديل التصنيفات
  - العرض الشجري للتصنيفات الهرمية
  - إدارة المستخدمين المرتبطين بكل تصنيف

### 2. إدارة التسلسل الهرمي
- **الرابط**: `/users/hierarchy/`
- **الوصول**: المدير العام والمدراء
- **الميزات**:
  - عرض جميع المستخدمين وتصنيفاتهم
  - تعيين المستخدمين للتصنيفات
  - تعيين المدراء لإدارة التصنيفات
  - إنشاء علاقات إدارة مباشرة

### 3. لوحات التحكم المحدثة
- **المدير العام**: إحصائيات شاملة + إدارة النظام
- **المستخدم/المدير**: إحصائيات الفريق + إدارة المرؤوسين
- **مندوب المبيعات**: إحصائيات شخصية + تصنيفاته

## الصلاحيات الافتراضية

### المدير العام
- إدارة المستخدمين والتصنيفات
- رؤية جميع الزيارات والتقارير
- تأكيد الزيارات وإدارة العملاء
- تصدير البيانات وإدارة النظام

### المستخدم/المدير
- إدارة المستخدمين المرؤوسين
- رؤية زيارات الفريق
- تأكيد زيارات الفريق
- إدارة عملاء الفريق
- رؤية تقارير الفريق

### مندوب المبيعات
- الوصول للتطبيق المحمول فقط
- إنشاء الزيارات الشخصية
- رؤية الزيارات الشخصية فقط

## أوامر الإدارة

### إنشاء البيانات التجريبية
```bash
python manage.py create_new_system_data
```

### ترحيل النظام القديم
```bash
# تشغيل تجريبي
python manage.py migrate_to_new_system --dry-run

# تطبيق الترحيل
python manage.py migrate_to_new_system
```

## API Endpoints

### إدارة التصنيفات
- `GET /categories/` - لوحة تحكم التصنيفات
- `GET /categories/{type}/` - قائمة تصنيفات حسب النوع
- `POST /categories/{type}/create/` - إنشاء تصنيف جديد
- `PUT /categories/edit/{id}/` - تعديل تصنيف
- `DELETE /categories/delete/{id}/` - حذف تصنيف
- `GET /categories/api/tree/{type}/` - شجرة التصنيفات

### إدارة التسلسل الهرمي
- `GET /users/hierarchy/` - واجهة إدارة التسلسل الهرمي
- `POST /users/api/assign-user-category/` - تعيين مستخدم لتصنيف
- `POST /users/api/assign-manager-category/` - تعيين مدير لإدارة تصنيف
- `POST /users/api/assign-direct-management/` - إنشاء إدارة مباشرة
- `GET /users/api/hierarchy-data/` - جلب بيانات التسلسل الهرمي
- `POST /users/api/remove-hierarchy/` - إزالة علاقة هرمية

## الترحيل من النظام القديم

### خطوات الترحيل
1. **النسخ الاحتياطي**: إنشاء نسخة احتياطية من قاعدة البيانات
2. **تطبيق المigrations**: `python manage.py migrate`
3. **إنشاء التصنيفات**: `python manage.py create_new_system_data`
4. **ترحيل المستخدمين**: `python manage.py migrate_to_new_system`

### التوافق مع النظام القديم
- تم الحفاظ على جميع البيانات الموجودة
- تم تحويل الأدوار القديمة للأدوار الجديدة
- تم إنشاء علاقات هرمية أساسية

## الاختبار

### اختبار النظام
1. تسجيل الدخول كمدير عام
2. الوصول لإدارة التصنيفات: `/categories/`
3. الوصول لإدارة التسلسل الهرمي: `/users/hierarchy/`
4. اختبار تعيين المستخدمين للتصنيفات
5. اختبار إنشاء علاقات إدارية

### بيانات الاختبار
- **المدير العام**: `super_manager` / `admin123`
- **مدير منطقة**: `region_manager1` / `manager123`
- **مندوب مبيعات**: `sales_rep1` / `rep123`

## الدعم والصيانة

### ملفات السجلات
- سجلات Django العادية
- سجلات خاصة بالعلاقات الهرمية في `UserHierarchy`

### النسخ الاحتياطي
- نسخ احتياطي دوري لقاعدة البيانات
- تصدير إعدادات التصنيفات والعلاقات الهرمية

### التحديثات المستقبلية
- إضافة المزيد من أنواع التصنيفات
- تطوير واجهات إدارة أكثر تقدماً
- إضافة تقارير تحليلية للعلاقات الهرمية

---

**تاريخ الإنشاء**: 2025-01-20  
**الإصدار**: 2.0  
**المطور**: Augment Agent
