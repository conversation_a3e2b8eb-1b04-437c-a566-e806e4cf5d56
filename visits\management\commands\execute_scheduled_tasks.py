from django.core.management.base import BaseCommand
from django.utils import timezone
from visits.models import Visit
from django.db.models import Q


class Command(BaseCommand):
    help = 'تنفيذ المهام المجدولة المستحقة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='عرض المهام المستحقة بدون تنفيذها'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='تنفيذ جميع المهام حتى المتأخرة'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        # البحث عن المهام المتكررة المستحقة للتنفيذ
        query = Q(
            is_recurring_task=True,
            task_status__in=['assigned', 'in_progress']
        )
        
        if force:
            # تنفيذ جميع المهام النشطة
            query &= Q(next_execution__isnull=False)
        else:
            # تنفيذ المهام المستحقة فقط
            query &= Q(next_execution__lte=timezone.now())
        
        # التأكد من عدم انتهاء فترة التكرار
        query &= (Q(recurrence_end_date__isnull=True) | Q(recurrence_end_date__gt=timezone.now()))
        
        due_tasks = Visit.objects.filter(query)
        
        if not due_tasks.exists():
            self.stdout.write(
                self.style.SUCCESS('لا توجد مهام مستحقة للتنفيذ في الوقت الحالي')
            )
            return
        
        self.stdout.write(f'تم العثور على {due_tasks.count()} مهمة مستحقة للتنفيذ')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('--- وضع المعاينة ---'))
        
        executed_count = 0
        created_visits_count = 0
        
        for task in due_tasks:
            self.stdout.write(f'\n📋 معالجة المهمة: {task.task_title}')
            self.stdout.write(f'👤 المندوب: {task.sales_rep.get_full_name()}')
            self.stdout.write(f'⏰ موعد التنفيذ: {task.next_execution}')
            
            target_clients = task.get_target_clients()
            clients_to_visit = target_clients[:task.max_visits_per_execution]
            
            self.stdout.write(f'🎯 عدد العملاء المستهدفين: {len(target_clients)}')
            self.stdout.write(f'📊 الحد الأقصى للزيارات: {task.max_visits_per_execution}')
            self.stdout.write(f'✅ سيتم زيارة {len(clients_to_visit)} عميل')
            
            if dry_run:
                self.stdout.write('العملاء الذين سيتم زيارتهم:')
                for i, client in enumerate(clients_to_visit, 1):
                    self.stdout.write(f'  {i}. {client.name}')
                continue
            
            try:
                # تنفيذ المهمة وإنشاء الزيارات الفرعية
                created_visits = task.create_child_visits()
                
                if created_visits:
                    executed_count += 1
                    created_visits_count += len(created_visits)
                    
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ تم تنفيذ المهمة وإنشاء {len(created_visits)} زيارة')
                    )
                    
                    # عرض تفاصيل الزيارات المُنشأة
                    for i, visit in enumerate(created_visits, 1):
                        self.stdout.write(f'  {i}. زيارة {visit.client.name} (ID: {str(visit.id)[:8]}...)')
                    
                    # عرض موعد التنفيذ التالي
                    if task.next_execution:
                        self.stdout.write(f'⏭ التنفيذ التالي: {task.next_execution}')
                    else:
                        self.stdout.write('🏁 انتهت فترة تكرار المهمة')
                else:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️ لم يتم إنشاء زيارات للمهمة (ربما لا توجد عملاء مستهدفين)')
                    )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ خطأ في تنفيذ المهمة: {str(e)}')
                )
        
        # ملخص النتائج
        self.stdout.write('\n' + '='*50)
        if dry_run:
            self.stdout.write(
                self.style.WARNING('📋 وضع المعاينة فقط - لم يتم تنفيذ أي مهام')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('🎉 تم الانتهاء من تنفيذ المهام المجدولة')
            )
            self.stdout.write(f'📊 المهام المنفذة: {executed_count}')
            self.stdout.write(f'📋 الزيارات المُنشأة: {created_visits_count}')
        
        # إحصائيات عامة
        self._show_general_statistics()
    
    def _show_general_statistics(self):
        """عرض إحصائيات عامة للمهام"""
        self.stdout.write('\n📈 إحصائيات عامة:')
        
        total_recurring_tasks = Visit.objects.filter(is_recurring_task=True).count()
        active_recurring_tasks = Visit.objects.filter(
            is_recurring_task=True,
            task_status__in=['assigned', 'in_progress']
        ).count()
        
        self.stdout.write(f'📋 إجمالي المهام المتكررة: {total_recurring_tasks}')
        self.stdout.write(f'🟢 المهام المتكررة النشطة: {active_recurring_tasks}')
        
        # المهام القادمة
        upcoming_tasks = Visit.objects.filter(
            is_recurring_task=True,
            next_execution__gt=timezone.now(),
            task_status__in=['assigned', 'in_progress']
        ).order_by('next_execution')[:5]
        
        if upcoming_tasks.exists():
            self.stdout.write('\n⏰ المهام القادمة (أقرب 5):')
            for i, task in enumerate(upcoming_tasks, 1):
                days_until = (task.next_execution - timezone.now()).days
                self.stdout.write(f'  {i}. {task.task_title} (خلال {days_until} يوم)')
        
        # التحقق من المهام المتأخرة
        overdue_tasks = Visit.objects.filter(
            is_recurring_task=True,
            next_execution__lt=timezone.now() - timezone.timedelta(hours=24),
            task_status__in=['assigned', 'in_progress']
        )
        
        if overdue_tasks.exists():
            self.stdout.write(
                self.style.WARNING(f'\n⚠️ تحذير: يوجد {overdue_tasks.count()} مهمة متأخرة أكثر من 24 ساعة')
            )
            for task in overdue_tasks:
                hours_overdue = (timezone.now() - task.next_execution).total_seconds() / 3600
                self.stdout.write(f'  - {task.task_title} (متأخرة {hours_overdue:.1f} ساعة)')
        else:
            self.stdout.write(self.style.SUCCESS('\n✅ لا توجد مهام متأخرة'))
