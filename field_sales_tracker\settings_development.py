# إعدادات التطوير - لقبول جميع الاتصالات غير المشفرة
from .settings import *

# تعطيل جميع فحوصات الأمان
DEBUG = True
ALLOWED_HOSTS = ['*']

# إعدادات CORS مفتوحة بالكامل
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_HEADERS = True
CORS_ALLOW_ALL_METHODS = True

# تعطيل جميع headers الأمنية
SECURE_CROSS_ORIGIN_OPENER_POLICY = None
SECURE_CROSS_ORIGIN_EMBEDDER_POLICY = None
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = False
SECURE_BROWSER_XSS_FILTER = False
SECURE_REFERRER_POLICY = None

# إعدادات Session و CSRF مرنة
SESSION_COOKIE_SECURE = False
SESSION_COOKIE_HTTPONLY = False
SESSION_COOKIE_SAMESITE = None

CSRF_COOKIE_SECURE = False
CSRF_COOKIE_HTTPONLY = False
CSRF_COOKIE_SAMESITE = None

# قبول جميع المصادر
CSRF_TRUSTED_ORIGINS = [
    'http://*',
    'https://*',
]

# Middleware مبسط
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    # تم إزالة CSRF و Security middleware
]

# تعطيل X-Frame-Options
X_FRAME_OPTIONS = None

print("🔓 إعدادات التطوير: تم تعطيل جميع فحوصات الأمان")
print("⚠️  تحذير: هذه الإعدادات للتطوير فقط!")
