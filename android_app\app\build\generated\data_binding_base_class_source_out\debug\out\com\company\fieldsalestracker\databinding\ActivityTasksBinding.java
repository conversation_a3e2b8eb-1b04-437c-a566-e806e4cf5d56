// Generated by view binder compiler. Do not edit!
package com.company.fieldsalestracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.company.fieldsalestracker.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTasksBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final LinearLayout emptyState;

  @NonNull
  public final FloatingActionButton fabRefresh;

  @NonNull
  public final RecyclerView recyclerViewTasks;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final Toolbar toolbar;

  private ActivityTasksBinding(@NonNull CoordinatorLayout rootView,
      @NonNull LinearLayout emptyState, @NonNull FloatingActionButton fabRefresh,
      @NonNull RecyclerView recyclerViewTasks, @NonNull SwipeRefreshLayout swipeRefreshLayout,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.emptyState = emptyState;
    this.fabRefresh = fabRefresh;
    this.recyclerViewTasks = recyclerViewTasks;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTasksBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTasksBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_tasks, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTasksBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.emptyState;
      LinearLayout emptyState = ViewBindings.findChildViewById(rootView, id);
      if (emptyState == null) {
        break missingId;
      }

      id = R.id.fabRefresh;
      FloatingActionButton fabRefresh = ViewBindings.findChildViewById(rootView, id);
      if (fabRefresh == null) {
        break missingId;
      }

      id = R.id.recyclerViewTasks;
      RecyclerView recyclerViewTasks = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewTasks == null) {
        break missingId;
      }

      id = R.id.swipeRefreshLayout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityTasksBinding((CoordinatorLayout) rootView, emptyState, fabRefresh,
          recyclerViewTasks, swipeRefreshLayout, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
