<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" 
        android:maxSdkVersion="32" />

    <!-- Camera features -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <!-- Location features -->
    <uses-feature
        android:name="android.hardware.location"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.location.gps"
        android:required="false" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.FieldSalesTracker"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:debuggable="false"
        tools:ignore="HardcodedDebugMode"
        tools:targetApi="31">

        <!-- Splash Activity (Main Launcher) -->
        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.FieldSalesTracker.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.FieldSalesTracker" />

        <!-- Login Activity -->
        <activity
            android:name=".LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- My Visits Activity -->
        <activity
            android:name=".MyVisitsActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- Tasks Activity -->
        <activity
            android:name=".TasksActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- Task Details Activity -->
        <activity
            android:name=".TaskDetailsActivity"
            android:exported="false"
            android:parentActivityName=".TasksActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- Barcode Scanner Activity -->
        <activity
            android:name=".BarcodeScannerActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- Visit Details Activity -->
        <activity
            android:name=".VisitDetailsActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- Client List Activity -->
        <activity
            android:name=".ClientListActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity" />

        <!-- Enhanced Tasks Activities -->
        <activity
            android:name=".EnhancedTasksActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <activity
            android:name=".EnhancedTaskDetailsActivity"
            android:exported="false"
            android:parentActivityName=".EnhancedTasksActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <activity
            android:name=".MultiTasksActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <activity
            android:name=".MultiTaskDetailsActivity"
            android:exported="false"
            android:parentActivityName=".MultiTasksActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- File Provider for camera -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- ML Kit metadata -->
        <meta-data
            android:name="com.google.mlkit.vision.DEPENDENCIES"
            android:value="barcode" />

    </application>

</manifest>
