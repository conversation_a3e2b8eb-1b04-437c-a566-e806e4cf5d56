from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import random

from categories.models import Category
from clients.models import Client
from visits.models import Visit

User = get_user_model()

class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية شاملة للنظام'

    def handle(self, *args, **options):
        self.stdout.write("🚀 بدء إنشاء البيانات التجريبية...")
        self.stdout.write("=" * 60)
        
        # 1. إنشاء المستخدمين
        self.stdout.write("👥 إنشاء المستخدمين...")
        users = self.create_users()
        
        # 2. إنشاء التصنيفات
        self.stdout.write("📂 إنشاء التصنيفات...")
        categories = self.create_categories(users)
        
        # 3. إنشاء العملاء
        self.stdout.write("🏢 إنشاء العملاء...")
        clients = self.create_clients()
        
        # 4. إنشاء الزيارات
        self.stdout.write("📍 إنشاء الزيارات...")
        visits = self.create_visits(users, clients)
        
        self.stdout.write("=" * 60)
        self.stdout.write(self.style.SUCCESS("✅ تم إنشاء البيانات التجريبية بنجاح!"))
        self.print_summary(users, categories, clients, visits)

    def create_users(self):
        """إنشاء مستخدمين تجريبيين"""
        users = []
        
        # مدير عام
        if not User.objects.filter(username='admin').exists():
            admin = User.objects.create_user(
                username='admin',
                password='admin123',
                first_name='أحمد',
                last_name='المدير العام',
                email='<EMAIL>',
                phone_number='+966501234567',
                employee_id='EMP001',
                national_id='1234567890',
                role_type=1,  # مدير عام
                is_active=True,
                is_active_employee=True,
                is_staff=True,
                is_superuser=True
            )
            users.append(admin)
            self.stdout.write(f"   ✅ تم إنشاء المدير العام: {admin.get_full_name()}")
        
        # مديرو المستخدمين
        managers_data = [
            {
                'username': 'manager1',
                'first_name': 'محمد',
                'last_name': 'مدير الرياض',
                'email': '<EMAIL>',
                'phone_number': '+966502345678',
                'employee_id': 'MGR001',
                'national_id': '2345678901'
            },
            {
                'username': 'manager2', 
                'first_name': 'فاطمة',
                'last_name': 'مدير جدة',
                'email': '<EMAIL>',
                'phone_number': '+966503456789',
                'employee_id': 'MGR002',
                'national_id': '3456789012'
            }
        ]
        
        for data in managers_data:
            if not User.objects.filter(username=data['username']).exists():
                manager = User.objects.create_user(
                    password='manager123',
                    role_type=2,  # مدير مستخدمين
                    is_active=True,
                    is_active_employee=True,
                    **data
                )
                users.append(manager)
                self.stdout.write(f"   ✅ تم إنشاء المدير: {manager.get_full_name()}")
        
        # مندوبو المبيعات
        sales_reps_data = [
            {
                'username': 'sales1',
                'first_name': 'خالد',
                'last_name': 'مندوب الرياض الشمالي',
                'email': '<EMAIL>',
                'phone_number': '+966504567890',
                'employee_id': 'REP001',
                'national_id': '4567890123'
            },
            {
                'username': 'sales2',
                'first_name': 'عبدالله',
                'last_name': 'مندوب الرياض الجنوبي', 
                'email': '<EMAIL>',
                'phone_number': '+966505678901',
                'employee_id': 'REP002',
                'national_id': '5678901234'
            },
            {
                'username': 'sales3',
                'first_name': 'سارة',
                'last_name': 'مندوب جدة الشمالي',
                'email': '<EMAIL>',
                'phone_number': '+966506789012',
                'employee_id': 'REP003',
                'national_id': '6789012345'
            },
            {
                'username': 'sales4',
                'first_name': 'نورا',
                'last_name': 'مندوب جدة الجنوبي',
                'email': '<EMAIL>',
                'phone_number': '+966507890123',
                'employee_id': 'REP004',
                'national_id': '7890123456'
            },
            {
                'username': 'sales5',
                'first_name': 'عمر',
                'last_name': 'مندوب الدمام',
                'email': '<EMAIL>',
                'phone_number': '+966508901234',
                'employee_id': 'REP005',
                'national_id': '8901234567'
            }
        ]
        
        for data in sales_reps_data:
            if not User.objects.filter(username=data['username']).exists():
                rep = User.objects.create_user(
                    password='sales123',
                    role_type=3,  # مندوب مبيعات
                    is_active=True,
                    is_active_employee=True,
                    **data
                )
                users.append(rep)
                self.stdout.write(f"   ✅ تم إنشاء المندوب: {rep.get_full_name()}")
        
        return users

    def create_categories(self, users):
        """إنشاء تصنيفات تجريبية"""
        categories = []
        
        # تصنيفات المستخدمين
        user_categories_data = [
            {'name': 'مديرو المناطق', 'description': 'مديرو المناطق الجغرافية'},
            {'name': 'مديرو الأقسام', 'description': 'مديرو الأقسام المختلفة'},
            {'name': 'المشرفون', 'description': 'المشرفون على المناديب'},
        ]
        
        for data in user_categories_data:
            if not Category.objects.filter(name=data['name'], category_type=1).exists():
                category = Category.objects.create(
                    category_type=1,
                    is_active=True,
                    created_by=users[0] if users else None,
                    **data
                )
                categories.append(category)
                self.stdout.write(f"   ✅ تم إنشاء تصنيف المستخدمين: {category.name}")
        
        # تصنيفات المناديب
        rep_categories_data = [
            {'name': 'مناديب الرياض', 'description': 'مناديب منطقة الرياض'},
            {'name': 'مناديب جدة', 'description': 'مناديب منطقة جدة'},
            {'name': 'مناديب الدمام', 'description': 'مناديب المنطقة الشرقية'},
            {'name': 'مناديب المدن الصغيرة', 'description': 'مناديب المدن والقرى الصغيرة'},
        ]
        
        for data in rep_categories_data:
            if not Category.objects.filter(name=data['name'], category_type=2).exists():
                category = Category.objects.create(
                    category_type=2,
                    is_active=True,
                    created_by=users[0] if users else None,
                    **data
                )
                categories.append(category)
                self.stdout.write(f"   ✅ تم إنشاء تصنيف المناديب: {category.name}")
        
        # تصنيفات العملاء
        client_categories_data = [
            {'name': 'متاجر كبيرة', 'description': 'المتاجر والسوبر ماركت الكبيرة'},
            {'name': 'متاجر متوسطة', 'description': 'المتاجر متوسطة الحجم'},
            {'name': 'متاجر صغيرة', 'description': 'البقالات والمتاجر الصغيرة'},
            {'name': 'مطاعم وكافيهات', 'description': 'المطاعم والكافيهات'},
            {'name': 'مؤسسات حكومية', 'description': 'الجهات والمؤسسات الحكومية'},
            {'name': 'شركات خاصة', 'description': 'الشركات والمؤسسات الخاصة'},
        ]
        
        for data in client_categories_data:
            if not Category.objects.filter(name=data['name'], category_type=3).exists():
                category = Category.objects.create(
                    category_type=3,
                    is_active=True,
                    created_by=users[0] if users else None,
                    **data
                )
                categories.append(category)
                self.stdout.write(f"   ✅ تم إنشاء تصنيف العملاء: {category.name}")
        
        return categories

    def create_clients(self):
        """إنشاء عملاء تجريبيين"""
        clients = []
        
        clients_data = [
            # الرياض
            {'name': 'هايبر بندة الرياض', 'phone_number': '+966112345678', 'address': 'شارع الملك فهد، الرياض', 'latitude': 24.7136, 'longitude': 46.6753},
            {'name': 'كارفور الرياض مول', 'phone_number': '+966112345679', 'address': 'الرياض مول، الرياض', 'latitude': 24.7220, 'longitude': 46.6690},
            {'name': 'بقالة أبو أحمد', 'phone_number': '+966112345680', 'address': 'حي النخيل، الرياض', 'latitude': 24.7000, 'longitude': 46.6800},
            {'name': 'مطعم الأصالة', 'phone_number': '+966112345681', 'address': 'شارع العليا، الرياض', 'latitude': 24.7300, 'longitude': 46.6600},
            {'name': 'كافيه ستار بكس العليا', 'phone_number': '+966112345682', 'address': 'برج العليا، الرياض', 'latitude': 24.7250, 'longitude': 46.6650},

            # جدة
            {'name': 'هايبر بندة جدة', 'phone_number': '+966122345678', 'address': 'شارع الأمير سلطان، جدة', 'latitude': 21.5433, 'longitude': 39.1728},
            {'name': 'الدانوب جدة', 'phone_number': '+966122345679', 'address': 'حي الزهراء، جدة', 'latitude': 21.5500, 'longitude': 39.1800},
            {'name': 'بقالة الحرمين', 'phone_number': '+966122345680', 'address': 'حي البلد، جدة', 'latitude': 21.5200, 'longitude': 39.1600},
            {'name': 'مطعم البيك', 'phone_number': '+966122345681', 'address': 'شارع التحلية، جدة', 'latitude': 21.5600, 'longitude': 39.1900},

            # الدمام
            {'name': 'لولو هايبر الدمام', 'phone_number': '+966132345678', 'address': 'شارع الملك سعود، الدمام', 'latitude': 26.4207, 'longitude': 50.0888},
            {'name': 'بقالة الخليج', 'phone_number': '+966132345679', 'address': 'حي الفيصلية، الدمام', 'latitude': 26.4300, 'longitude': 50.1000},
            {'name': 'مطعم كودو', 'phone_number': '+966132345680', 'address': 'الواجهة البحرية، الدمام', 'latitude': 26.4100, 'longitude': 50.0700},

            # مدن أخرى
            {'name': 'بقالة النور - الطائف', 'phone_number': '+966122345690', 'address': 'وسط الطائف', 'latitude': 21.2703, 'longitude': 40.4158},
            {'name': 'سوق المدينة - المدينة المنورة', 'phone_number': '+966142345678', 'address': 'شارع قباء، المدينة المنورة', 'latitude': 24.4539, 'longitude': 39.6034},
            {'name': 'بقالة الواحة - أبها', 'phone_number': '+966172345678', 'address': 'وسط أبها', 'latitude': 18.2164, 'longitude': 42.5053},
        ]
        
        for i, data in enumerate(clients_data, 1):
            if not Client.objects.filter(name=data['name']).exists():
                # إنشاء باركود فريد
                barcode = f"CLT{i:06d}"
                
                client = Client.objects.create(
                    barcode=barcode,
                    **data
                )
                clients.append(client)
                self.stdout.write(f"   ✅ تم إنشاء العميل: {client.name}")
        
        return clients

    def create_visits(self, users, clients):
        """إنشاء زيارات تجريبية"""
        visits = []
        
        # الحصول على المناديب فقط
        sales_reps = [user for user in users if user.role_type == 3]
        
        if not sales_reps or not clients:
            self.stdout.write("   ⚠️ لا توجد مناديب أو عملاء لإنشاء زيارات")
            return visits
        
        # إنشاء زيارات للأسبوعين الماضيين
        start_date = timezone.now() - timedelta(days=14)
        
        visit_statuses = ['pending', 'verified', 'rejected']
        visit_types = ['regular', 'follow_up', 'complaint', 'delivery']
        
        for day in range(14):
            visit_date = start_date + timedelta(days=day)
            
            # إنشاء 3-8 زيارات يومياً
            daily_visits = random.randint(3, 8)
            
            for _ in range(daily_visits):
                rep = random.choice(sales_reps)
                client = random.choice(clients)
                
                # تجنب الزيارات المكررة لنفس العميل في نفس اليوم
                existing_visit = Visit.objects.filter(
                    sales_rep=rep,
                    client=client,
                    visit_date__date=visit_date.date()
                ).exists()
                
                if existing_visit:
                    continue
                
                visit = Visit.objects.create(
                    sales_rep=rep,
                    client=client,
                    visit_date=visit_date,
                    latitude=client.latitude + random.uniform(-0.001, 0.001),
                    longitude=client.longitude + random.uniform(-0.001, 0.001),
                    notes=f"زيارة {random.choice(['روتينية', 'متابعة', 'طلب خاص', 'شكوى'])} للعميل {client.name}",
                    status=random.choice(visit_statuses),
                    visit_type=random.choice(visit_types),
                    distance_from_client=random.uniform(5, 50),  # متر
                    created_at=visit_date,
                    updated_at=visit_date
                )
                visits.append(visit)
        
        self.stdout.write(f"   ✅ تم إنشاء {len(visits)} زيارة")
        return visits

    def print_summary(self, users, categories, clients, visits):
        """طباعة ملخص البيانات المنشأة"""
        self.stdout.write("\n📊 ملخص البيانات التجريبية:")
        self.stdout.write("-" * 40)
        self.stdout.write(f"👥 المستخدمين: {len(users)}")
        self.stdout.write(f"   - مدير عام: {len([u for u in users if u.role_type == 1])}")
        self.stdout.write(f"   - مدير مستخدمين: {len([u for u in users if u.role_type == 2])}")
        self.stdout.write(f"   - مندوب مبيعات: {len([u for u in users if u.role_type == 3])}")
        
        self.stdout.write(f"\n📂 التصنيفات: {len(categories)}")
        self.stdout.write(f"   - تصنيفات المستخدمين: {len([c for c in categories if c.category_type == 1])}")
        self.stdout.write(f"   - تصنيفات المناديب: {len([c for c in categories if c.category_type == 2])}")
        self.stdout.write(f"   - تصنيفات العملاء: {len([c for c in categories if c.category_type == 3])}")
        
        self.stdout.write(f"\n🏢 العملاء: {len(clients)}")
        # تجميع العملاء حسب المدينة من العنوان
        cities = {}
        for client in clients:
            # استخراج المدينة من العنوان
            if 'الرياض' in client.address:
                city = 'الرياض'
            elif 'جدة' in client.address:
                city = 'جدة'
            elif 'الدمام' in client.address:
                city = 'الدمام'
            elif 'الطائف' in client.address:
                city = 'الطائف'
            elif 'المدينة' in client.address:
                city = 'المدينة المنورة'
            elif 'أبها' in client.address:
                city = 'أبها'
            else:
                city = 'أخرى'
            cities[city] = cities.get(city, 0) + 1
        for city, count in cities.items():
            self.stdout.write(f"   - {city}: {count}")
        
        self.stdout.write(f"\n📍 الزيارات: {len(visits)}")
        if visits:
            statuses = {}
            for visit in visits:
                statuses[visit.status] = statuses.get(visit.status, 0) + 1
            for status, count in statuses.items():
                status_ar = {'pending': 'معلقة', 'verified': 'مؤكدة', 'rejected': 'مرفوضة'}.get(status, status)
                self.stdout.write(f"   - {status_ar}: {count}")
        
        self.stdout.write("\n🔐 بيانات تسجيل الدخول:")
        self.stdout.write("-" * 40)
        self.stdout.write("المدير العام:")
        self.stdout.write("  اسم المستخدم: admin")
        self.stdout.write("  كلمة المرور: admin123")
        self.stdout.write("\nالمديرين:")
        self.stdout.write("  اسم المستخدم: manager1 / manager2")
        self.stdout.write("  كلمة المرور: manager123")
        self.stdout.write("\nالمناديب:")
        self.stdout.write("  اسم المستخدم: sales1 / sales2 / sales3 / sales4 / sales5")
        self.stdout.write("  كلمة المرور: sales123")
