[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\layout_activity_barcode_scanner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_barcode_scanner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_help.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_help.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\layout_item_client.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\item_client.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_check_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_check_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_assignment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_assignment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_button_scanner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\button_scanner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-xxxhdpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-xxxhdpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\menu_main_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\menu\\main_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_camera.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_camera.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_btn_camera_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\btn_camera_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-xhdpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-xhdpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-mdpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-mdpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-xxhdpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-xxhdpi\\ic_launcher_round.xml"}, {"merged": "com.company.fieldsalestracker.app-merged_res-54:/drawable_ic_event.xml.flat", "source": "com.company.fieldsalestracker.app-main-56:/drawable/ic_event.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_logout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_logout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-xxxhdpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-xxxhdpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\layout_item_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\item_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\layout_activity_my_visits.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_my_visits.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-hdpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-hdpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\layout_activity_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_splash.xml"}, {"merged": "com.company.fieldsalestracker.app-merged_res-54:/layout_item_multi_task.xml.flat", "source": "com.company.fieldsalestracker.app-main-56:/layout/item_multi_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_login_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\login_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_group.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_group.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_business.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_business.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-xxhdpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-xxhdpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_button_start.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\button_start.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_multi_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_multi_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_location.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_location.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-xhdpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-xhdpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_alarm.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_alarm.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_enhanced_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_enhanced_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\xml\\data_extraction_rules.xml"}, {"merged": "com.company.fieldsalestracker.app-merged_res-54:/layout_activity_multi_tasks.xml.flat", "source": "com.company.fieldsalestracker.app-main-56:/layout/activity_multi_tasks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_refresh.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_refresh.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_scanner_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\scanner_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\layout_activity_tasks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_tasks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-mdpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-mdpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_launcher_simple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_launcher_simple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_subdirectory_arrow_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_subdirectory_arrow_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_badge_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\badge_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_play_arrow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_play_arrow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_repeat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_repeat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\xml\\file_paths.xml"}, {"merged": "com.company.fieldsalestracker.app-merged_res-54:/drawable_ic_priority_low.xml.flat", "source": "com.company.fieldsalestracker.app-main-56:/drawable/ic_priority_low.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_schedule.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_schedule.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\anim_button_scale_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\anim\\button_scale_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\mipmap-hdpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\mipmap-hdpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_person.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_person.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\layout_activity_task_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_task_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_location_on.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_location_on.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_splash_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\splash_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_arrow_forward.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_arrow_forward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_send_enhanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_send_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_button_acknowledge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\button_acknowledge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\anim_button_scale_down.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\anim\\button_scale_down.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_send.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_send.xml"}, {"merged": "com.company.fieldsalestracker.app-merged_res-54:/layout_item_enhanced_task.xml.flat", "source": "com.company.fieldsalestracker.app-main-56:/layout/item_enhanced_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\layout_item_visit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\item_visit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\status_background.xml"}, {"merged": "com.company.fieldsalestracker.app-merged_res-54:/layout_activity_enhanced_tasks.xml.flat", "source": "com.company.fieldsalestracker.app-main-56:/layout/activity_enhanced_tasks.xml"}, {"merged": "com.company.fieldsalestracker.app-merged_res-54:/drawable_ic_priority_medium.xml.flat", "source": "com.company.fieldsalestracker.app-main-56:/drawable/ic_priority_medium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_btn_submit_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\btn_submit_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_camera_enhanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_camera_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_lock.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_lock.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\layout_activity_visit_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\layout\\activity_visit_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_qr_code_scanner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_qr_code_scanner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-merged_res-54:\\drawable_ic_priority_high.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.company.fieldsalestracker.app-main-56:\\drawable\\ic_priority_high.xml"}]