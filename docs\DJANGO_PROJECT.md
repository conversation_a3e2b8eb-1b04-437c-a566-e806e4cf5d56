# 🌐 **مشروع Django - نظام تتبع العملاء الميداني**

## 🎯 **نظرة عامة**
نظام ويب متكامل مبني بـ Django لإدارة المبيعات الميدانية مع لوحة تحكم متقدمة ونظام مهام ذكي.

---

## 🏗️ **هيكل المشروع**

### **📁 التطبيقات الرئيسية:**
```
TrackCustomer/
├── field_sales_tracker/    # الإعدادات الرئيسية
├── users/                  # إدارة المستخدمين
├── clients/               # إدارة العملاء
├── visits/                # إدارة الزيارات والمهام
├── categories/            # إدارة التصنيفات
├── dashboard/             # لوحة التحكم
├── static/                # الملفات الثابتة
├── templates/             # القوالب
└── media/                 # ملفات الوسائط
```

---

## 🔧 **الإعدادات والتكوين**

### **settings.py الرئيسية:**
```python
# قاعدة البيانات
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# التطبيقات المثبتة
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    'users',
    'clients', 
    'visits',
    'categories',
    'dashboard',
]

# إعدادات REST Framework
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
}
```

### **URLs الرئيسية:**
```python
urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('dashboard.urls')),
    path('users/', include('users.urls')),
    path('clients/', include('clients.urls')),
    path('visits/', include('visits.urls')),
    path('categories/', include('categories.urls')),
    path('api/', include('visits.urls')),  # API endpoints
]
```

---

## 👤 **تطبيق المستخدمين (Users)**

### **النماذج:**
```python
class User(AbstractUser):
    ROLE_CHOICES = [
        (1, 'مدير عام'),
        (2, 'مدير مستخدمين'), 
        (3, 'مندوب مبيعات'),
    ]
    
    role_type = models.IntegerField(choices=ROLE_CHOICES, default=3)
    is_super_manager = models.BooleanField(default=False)
    is_user_manager = models.BooleanField(default=False)
    categories = models.JSONField(default=list, blank=True)
    phone_number = models.CharField(max_length=20, blank=True)
    auth_token = models.CharField(max_length=255, blank=True)
```

### **الوظائف الرئيسية:**
- ✅ **إدارة المستخدمين:** إضافة، تعديل، حذف
- ✅ **نظام الأدوار:** مدير عام، مدير مستخدمين، مندوب
- ✅ **التصنيفات:** ربط المستخدمين بالتصنيفات
- ✅ **المصادقة:** نظام Token للـ API
- ✅ **الصلاحيات:** تحكم دقيق في الوصول

### **الواجهات:**
- `/users/management/` - إدارة المستخدمين (DataTable)
- `/users/add/` - إضافة مستخدم جديد
- `/users/edit/<id>/` - تعديل مستخدم
- `/users/<id>/` - تفاصيل المستخدم

---

## 🏢 **تطبيق العملاء (Clients)**

### **النماذج:**
```python
class Client(models.Model):
    name = models.CharField(max_length=255)
    address = models.TextField()
    phone = models.CharField(max_length=20)
    email = models.EmailField(blank=True)
    latitude = models.DecimalField(max_digits=10, decimal_places=8)
    longitude = models.DecimalField(max_digits=11, decimal_places=8)
    barcode = models.CharField(max_length=100, unique=True)
    qr_code_image = models.ImageField(upload_to='qr_codes/')
    category = models.ForeignKey(Category, on_delete=models.SET_NULL)
    is_active = models.BooleanField(default=True)
```

### **الوظائف الرئيسية:**
- ✅ **إدارة العملاء:** CRUD كامل مع DataTable متقدم
- ✅ **الباركود:** توليد تلقائي فريد
- ✅ **رموز QR:** إنشاء وحفظ تلقائي
- ✅ **الخرائط:** تكامل مع Google Maps
- ✅ **البحث المتقدم:** فلترة حسب التصنيف والموقع
- ✅ **التصدير:** Excel, PDF, طباعة

### **الواجهات:**
- `/clients/` - قائمة العملاء (DataTable متقدم)
- `/clients/add/` - إضافة عميل جديد
- `/clients/edit/<id>/` - تعديل عميل
- `/clients/<id>/` - تفاصيل العميل
- `/clients/api/` - API للتطبيق المحمول

---

## 📋 **تطبيق الزيارات والمهام (Visits)**

### **النماذج الرئيسية:**
```python
class Visit(models.Model):
    # الحقول الأساسية
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    sales_rep = models.ForeignKey(User, on_delete=models.CASCADE)
    visit_datetime = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    
    # حقول المهام المتقدمة
    task_type = models.CharField(max_length=20, choices=TASK_TYPE_CHOICES)
    task_title = models.CharField(max_length=255, blank=True)
    task_description = models.TextField(blank=True)
    task_status = models.CharField(max_length=20, choices=TASK_STATUS_CHOICES)
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES)
    
    # حقول المهام المتكررة
    is_recurring_task = models.BooleanField(default=False)
    recurrence_type = models.CharField(max_length=10, choices=RECURRENCE_CHOICES)
    recurrence_interval = models.IntegerField(default=1)
    next_execution = models.DateTimeField(null=True, blank=True)
    
    # حقول المهام المتعددة
    is_parent_task = models.BooleanField(default=False)
    is_child_visit = models.BooleanField(default=False)
    parent_task = models.ForeignKey('self', on_delete=models.CASCADE)
```

### **الوظائف المتقدمة:**

#### **🔧 إدارة الزيارات المحسنة:**
- ✅ **فلاتر شاملة:** بحث، حالة، مندوب، عميل، تاريخ
- ✅ **عرضين:** بطاقات وجدول
- ✅ **إحصائيات مباشرة:** مؤكدة، معلقة، مرفوضة
- ✅ **تكامل الخرائط:** عرض مواقع الزيارات
- ✅ **DataTable متقدم:** ترتيب، بحث، تصدير

#### **👥 المهام متعددة الزيارات:**
- ✅ **مهام متكررة:** يومي، أسبوعي، شهري
- ✅ **التنفيذ التلقائي:** للمهام المستحقة
- ✅ **مؤشرات التقدم:** نسب الإنجاز
- ✅ **إحصائيات متقدمة:** معدلات النجاح
- ✅ **إدارة الزيارات الفرعية:** تتبع شامل

#### **📊 نظام المهام الذكي:**
- ✅ **أنواع المهام:** عادية، متعددة، متكررة
- ✅ **الأولويات:** منخفض، متوسط، عالي، عاجل
- ✅ **حالات المهام:** مكلفة، مقرة، قيد التنفيذ، مكتملة
- ✅ **الجدولة التلقائية:** تنفيذ المهام المتكررة

### **الواجهات:**
- `/visits/` - قائمة الزيارات الأساسية
- `/visits/enhanced-management/` - إدارة الزيارات المحسنة
- `/visits/tasks-management/` - إدارة المهام
- `/visits/multi-tasks/` - المهام متعددة الزيارات
- `/visits/create-task/` - إنشاء مهمة موحدة
- `/visits/statistics/` - إحصائيات الزيارات

---

## 🏷️ **تطبيق التصنيفات (Categories)**

### **النماذج:**
```python
class Category(models.Model):
    CATEGORY_TYPES = [
        ('client', 'تصنيف عملاء'),
        ('user', 'تصنيف مستخدمين'),
        ('region', 'تصنيف مناطق'),
    ]
    
    name = models.CharField(max_length=255)
    category_type = models.CharField(max_length=20, choices=CATEGORY_TYPES)
    parent = models.ForeignKey('self', on_delete=models.CASCADE)
    is_system = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
```

### **الوظائف:**
- ✅ **تصنيفات هرمية:** دعم التصنيفات الفرعية
- ✅ **أنواع متعددة:** عملاء، مستخدمين، مناطق
- ✅ **إدارة شاملة:** DataTable محسن
- ✅ **ربط المستخدمين:** تخصيص التصنيفات للمستخدمين

---

## 🏠 **لوحة التحكم (Dashboard)**

### **الميزات الرئيسية:**
- 📊 **إحصائيات شاملة:** زيارات، مهام، مستخدمين، عملاء
- 📈 **مؤشرات الأداء:** معدلات النجاح والإنجاز
- ⚡ **إجراءات سريعة:** وصول مباشر لجميع الوظائف
- 🏆 **أفضل المناديب:** ترتيب حسب الأداء
- ⭐ **أهم العملاء:** الأكثر زيارة
- 📝 **أحدث الأنشطة:** متابعة مباشرة

### **التحسينات:**
- ✅ **تصميم متجاوب:** جميع الأجهزة
- ✅ **تحديث مباشر:** بيانات فورية
- ✅ **واجهة احترافية:** تصميم عصري
- ✅ **إلغاء welcome-section:** تركيز على المحتوى

---

## 🔌 **API Endpoints**

### **المصادقة:**
```python
# تسجيل الدخول
POST /api/login/
{
    "username": "user",
    "password": "pass"
}

# الحصول على التوكن
Response: {
    "success": true,
    "token": "abc123...",
    "user": {...}
}
```

### **الزيارات:**
```python
# قائمة الزيارات
GET /api/visits/

# إرسال زيارة
POST /api/visits/submit/

# الزيارات المحسنة
GET /api/visits/enhanced-management/

# المهام المتعددة
GET /api/visits/multi-tasks/

# تنفيذ مهمة
POST /api/visits/{id}/execute/
```

### **المهام:**
```python
# مهام المستخدم
GET /api/visits/my-tasks/

# إقرار مهمة
POST /api/visits/{id}/acknowledge/

# بدء مهمة
POST /api/visits/{id}/start/

# إكمال مهمة
POST /api/visits/{id}/complete/
```

---

## 🔐 **الأمان والصلاحيات**

### **مستويات الوصول:**
```python
# المدير العام
if user.is_super_manager:
    # وصول كامل لجميع البيانات
    
# مدير المستخدمين
elif user.is_user_manager:
    # وصول للمناديب التابعين فقط
    managed_users = get_managed_users(user)
    
# المندوب
else:
    # وصول للبيانات الشخصية فقط
```

### **حماية API:**
```python
# Token Authentication
@csrf_exempt
@require_http_methods(["GET"])
def api_view(request):
    auth_header = request.META.get('HTTP_AUTHORIZATION')
    if not auth_header or not auth_header.startswith('Bearer '):
        return JsonResponse({'error': 'Authentication required'}, status=401)
```

---

## ⚡ **الأداء والتحسينات**

### **قاعدة البيانات:**
- 🗂️ **فهرسة محسنة:** استعلامات سريعة
- 🔄 **علاقات محسنة:** parent-child للمهام
- 📊 **إحصائيات مخزنة:** حسابات سريعة

### **واجهة المستخدم:**
- 📱 **تصميم متجاوب:** جميع الأجهزة
- ⚡ **تحميل سريع:** lazy loading
- 🎨 **تأثيرات سلسة:** animations
- 📊 **DataTables محسنة:** أداء عالي

---

## 🚀 **الجدولة التلقائية**

### **Command للتنفيذ:**
```bash
# معاينة المهام المستحقة
python manage.py execute_scheduled_tasks --dry-run

# تنفيذ المهام
python manage.py execute_scheduled_tasks
```

### **أنواع التكرار:**
- 📅 **يومي:** كل X أيام
- 📆 **أسبوعي:** كل X أسابيع  
- 🗓️ **شهري:** كل X أشهر

---

## 📊 **التقارير والإحصائيات**

### **مؤشرات الأداء:**
- 📈 **معدل نجاح الزيارات**
- 📊 **متوسط المسافة المقطوعة**
- 📝 **عدد الزيارات اليومية/الأسبوعية/الشهرية**
- 🎯 **معدل إنجاز المهام**
- 📋 **توزيع الزيارات حسب التصنيفات**

### **تقارير تلقائية:**
- 📄 **تقرير الأداء اليومي**
- ⏰ **تقرير المهام المتأخرة**
- 🏆 **تقرير أفضل المناديب**
- ⭐ **تقرير العملاء الأكثر زيارة**

---

**🌐 نظام Django متكامل وقابل للتوسع مع أداء عالي وأمان متقدم**
