from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
import uuid


class Category(models.Model):
    """
    نموذج التصنيفات مع دعم الهيكل الهرمي
    """

    # أنواع التصنيفات
    CATEGORY_TYPES = [
        (0, 'تصنيف عام للنظام'),           # System Categories
        (1, 'تصنيف المدير العام'),         # Super Manager Categories
        (2, 'تصنيف المستخدمين'),          # User Categories
        (3, 'تصنيف المناديب'),            # Sales Rep Categories
        (4, 'تصنيف العملاء'),             # Client Categories
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, verbose_name='اسم التصنيف')
    description = models.TextField(blank=True, null=True, verbose_name='وصف التصنيف')

    # نوع التصنيف
    category_type = models.IntegerField(
        choices=CATEGORY_TYPES,
        verbose_name='نوع التصنيف',
        help_text='تحديد نوع التصنيف حسب الاستخدام'
    )

    # التصنيف الأب (للهيكل الهرمي)
    parent_category = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='subcategories',
        verbose_name='التصنيف الأب'
    )

    # معلومات إضافية
    code = models.CharField(
        max_length=50,
        unique=True,
        blank=True,
        null=True,
        verbose_name='رمز التصنيف',
        help_text='رمز فريد للتصنيف (اختياري)'
    )

    color = models.CharField(
        max_length=7,
        default='#007bff',
        verbose_name='لون التصنيف',
        help_text='لون التصنيف في الواجهة (hex color)'
    )

    icon = models.CharField(
        max_length=50,
        default='fas fa-folder',
        verbose_name='أيقونة التصنيف',
        help_text='أيقونة FontAwesome للتصنيف'
    )

    # ترتيب العرض
    sort_order = models.IntegerField(
        default=0,
        verbose_name='ترتيب العرض',
        help_text='ترتيب التصنيف في القوائم'
    )

    # حالة التصنيف
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    is_system = models.BooleanField(
        default=False,
        verbose_name='تصنيف نظام',
        help_text='تصنيف نظام لا يمكن حذفه'
    )

    # معلومات الإنشاء والتحديث
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_categories',
        verbose_name='أنشأ بواسطة'
    )

    class Meta:
        db_table = 'categories'
        verbose_name = 'تصنيف'
        verbose_name_plural = 'التصنيفات'
        ordering = ['category_type', 'sort_order', 'name']
        unique_together = [['name', 'category_type', 'parent_category']]

    def __str__(self):
        if self.parent_category:
            return f"{self.parent_category.name} > {self.name}"
        return self.name

    def clean(self):
        """التحقق من صحة البيانات"""
        super().clean()

        # التحقق من عدم تعيين التصنيف كأب لنفسه
        if self.parent_category == self:
            raise ValidationError("لا يمكن للتصنيف أن يكون أباً لنفسه")

        # التحقق من عدم وجود حلقة مفرغة في الهيكل الهرمي
        if self.parent_category:
            parent = self.parent_category
            while parent:
                if parent == self:
                    raise ValidationError("لا يمكن إنشاء حلقة مفرغة في الهيكل الهرمي")
                parent = parent.parent_category

        # التحقق من أن التصنيف الأب من نفس النوع
        if self.parent_category and self.parent_category.category_type != self.category_type:
            raise ValidationError("يجب أن يكون التصنيف الأب من نفس النوع")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def get_full_path(self):
        """الحصول على المسار الكامل للتصنيف"""
        path = [self.name]
        parent = self.parent_category
        while parent:
            path.insert(0, parent.name)
            parent = parent.parent_category
        return ' > '.join(path)

    def get_children(self):
        """الحصول على التصنيفات الفرعية"""
        return self.subcategories.filter(is_active=True).order_by('sort_order', 'name')

    def get_all_descendants(self):
        """الحصول على جميع التصنيفات الفرعية (بما في ذلك الأحفاد)"""
        descendants = []
        for child in self.get_children():
            descendants.append(child)
            descendants.extend(child.get_all_descendants())
        return descendants

    def get_ancestors(self):
        """الحصول على جميع التصنيفات الأب"""
        ancestors = []
        parent = self.parent_category
        while parent:
            ancestors.append(parent)
            parent = parent.parent_category
        return ancestors

    def get_level(self):
        """الحصول على مستوى التصنيف في الهيكل الهرمي"""
        level = 0
        parent = self.parent_category
        while parent:
            level += 1
            parent = parent.parent_category
        return level

    def is_descendant_of(self, category):
        """التحقق من كون التصنيف فرعي لتصنيف آخر"""
        parent = self.parent_category
        while parent:
            if parent == category:
                return True
            parent = parent.parent_category
        return False

    def get_category_type_display_ar(self):
        """الحصول على اسم نوع التصنيف بالعربية"""
        type_names = {
            0: 'تصنيف عام للنظام',
            1: 'تصنيف المدير العام',
            2: 'تصنيف المستخدمين',
            3: 'تصنيف المناديب',
            4: 'تصنيف العملاء',
        }
        return type_names.get(self.category_type, 'غير محدد')

    @classmethod
    def get_root_categories(cls, category_type=None):
        """الحصول على التصنيفات الجذر (بدون أب)"""
        queryset = cls.objects.filter(parent_category__isnull=True, is_active=True)
        if category_type is not None:
            queryset = queryset.filter(category_type=category_type)
        return queryset.order_by('sort_order', 'name')

    @classmethod
    def get_categories_by_type(cls, category_type):
        """الحصول على التصنيفات حسب النوع"""
        return cls.objects.filter(
            category_type=category_type,
            is_active=True
        ).order_by('sort_order', 'name')

    @classmethod
    def create_default_categories(cls):
        """إنشاء التصنيفات الافتراضية للنظام"""
        default_categories = [
            # تصنيفات النظام (0)
            {'name': 'تصنيفات النظام', 'type': 0, 'code': 'SYSTEM', 'is_system': True},

            # تصنيفات المدير العام (1)
            {'name': 'إدارة عليا', 'type': 1, 'code': 'TOP_MGMT', 'is_system': True},
            {'name': 'إدارة تنفيذية', 'type': 1, 'code': 'EXEC_MGMT', 'is_system': True},

            # تصنيفات المستخدمين (2)
            {'name': 'مدراء المناطق', 'type': 2, 'code': 'REGION_MGR', 'is_system': True},
            {'name': 'مدراء الفروع', 'type': 2, 'code': 'BRANCH_MGR', 'is_system': True},
            {'name': 'مشرفين', 'type': 2, 'code': 'SUPERVISORS', 'is_system': True},

            # تصنيفات المناديب (3)
            {'name': 'مناديب كبار', 'type': 3, 'code': 'SENIOR_REPS', 'is_system': True},
            {'name': 'مناديب عاديين', 'type': 3, 'code': 'REGULAR_REPS', 'is_system': True},
            {'name': 'مناديب جدد', 'type': 3, 'code': 'NEW_REPS', 'is_system': True},

            # تصنيفات العملاء (4)
            {'name': 'عملاء VIP', 'type': 4, 'code': 'VIP_CLIENTS', 'is_system': True},
            {'name': 'عملاء كبار', 'type': 4, 'code': 'MAJOR_CLIENTS', 'is_system': True},
            {'name': 'عملاء عاديين', 'type': 4, 'code': 'REGULAR_CLIENTS', 'is_system': True},
        ]

        created_categories = []
        for cat_data in default_categories:
            category, created = cls.objects.get_or_create(
                code=cat_data['code'],
                defaults={
                    'name': cat_data['name'],
                    'category_type': cat_data['type'],
                    'is_system': cat_data.get('is_system', False),
                    'is_active': True,
                }
            )
            if created:
                created_categories.append(category)

        return created_categories
