{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/management-pages.css' %}">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

.metric-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    text-align: center;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
}

.metric-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-change {
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.metric-change.positive {
    color: #28a745;
}

.metric-change.negative {
    color: #dc3545;
}

.metric-primary { color: #007bff; }
.metric-success { color: #28a745; }
.metric-warning { color: #ffc107; }
.metric-danger { color: #dc3545; }
.metric-info { color: #17a2b8; }
.metric-secondary { color: #6c757d; }

.top-clients-list {
    max-height: 400px;
    overflow-y: auto;
}

.client-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.client-item:hover {
    background-color: #f8f9fa;
}

.client-info {
    flex: 1;
}

.client-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.client-stats {
    font-size: 0.85rem;
    color: #6c757d;
}

.client-badge {
    margin-left: 0.5rem;
}

.cities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.city-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
}

.city-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.city-count {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-bar me-2"></i>
                {{ title }}
            </h1>
            <p class="text-muted mb-0">تحليل شامل لبيانات العملاء والزيارات</p>
        </div>
        <div>
            <button class="btn btn-success" onclick="exportStatistics()">
                <i class="fas fa-download me-2"></i>
                تصدير التقرير
            </button>
        </div>
    </div>

    <!-- Main Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <div class="metric-number metric-primary">{{ total_clients }}</div>
                <div class="metric-label">إجمالي العملاء</div>
                {% if clients_this_month > clients_last_month %}
                <div class="metric-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +{{ clients_this_month }} هذا الشهر
                </div>
                {% elif clients_this_month < clients_last_month %}
                <div class="metric-change negative">
                    <i class="fas fa-arrow-down"></i>
                    {{ clients_this_month }} هذا الشهر
                </div>
                {% endif %}
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <div class="metric-number metric-success">{{ total_visits }}</div>
                <div class="metric-label">إجمالي الزيارات</div>
                {% if visits_this_month > visits_last_month %}
                <div class="metric-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +{{ visits_this_month }} هذا الشهر
                </div>
                {% elif visits_this_month < visits_last_month %}
                <div class="metric-change negative">
                    <i class="fas fa-arrow-down"></i>
                    {{ visits_this_month }} هذا الشهر
                </div>
                {% endif %}
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <div class="metric-number metric-info">{{ success_rate }}%</div>
                <div class="metric-label">معدل نجاح الزيارات</div>
                <div class="metric-change">
                    {{ verified_visits }} من {{ total_visits }} زيارة
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <div class="metric-number metric-warning">{{ avg_visits_per_client }}</div>
                <div class="metric-label">متوسط الزيارات لكل عميل</div>
                <div class="metric-change">
                    {{ clients_no_visits }} عميل بدون زيارات
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-pie-chart me-2"></i>
                        توزيع حالات الزيارات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="visitsStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bar-chart me-2"></i>
                        العملاء والزيارات الشهرية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Clients and Cities -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        أفضل العملاء (الأكثر زيارة)
                    </h5>
                </div>
                <div class="card-body">
                    {% if top_clients %}
                    <div class="top-clients-list">
                        {% for item in top_clients %}
                        <div class="client-item">
                            <div class="client-info">
                                <div class="client-name">{{ item.client.name }}</div>
                                <div class="client-stats">
                                    {{ item.visits_count }} زيارة • 
                                    معدل النجاح: {{ item.success_rate }}% •
                                    {% if item.last_visit %}
                                    آخر زيارة: {{ item.last_visit.visit_datetime|date:"Y-m-d" }}
                                    {% else %}
                                    لا توجد زيارات
                                    {% endif %}
                                </div>
                            </div>
                            <div>
                                <span class="badge bg-primary client-badge">{{ item.visits_count }}</span>
                                {% if item.success_rate >= 80 %}
                                <span class="badge bg-success client-badge">ممتاز</span>
                                {% elif item.success_rate >= 60 %}
                                <span class="badge bg-warning client-badge">جيد</span>
                                {% else %}
                                <span class="badge bg-danger client-badge">ضعيف</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد بيانات</h5>
                        <p class="text-muted">لم يتم العثور على عملاء بزيارات</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        توزيع العملاء حسب المدن
                    </h5>
                </div>
                <div class="card-body">
                    {% if cities_stats %}
                    <div class="cities-grid">
                        {% for city, count in cities_stats.items %}
                        <div class="city-item">
                            <div class="city-name">{{ city }}</div>
                            <div class="city-count">{{ count }}</div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-map fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد بيانات</h5>
                        <p class="text-muted">لم يتم العثور على مدن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="row">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <div class="metric-number metric-success">{{ verified_visits }}</div>
                <div class="metric-label">زيارات مؤكدة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <div class="metric-number metric-warning">{{ pending_visits }}</div>
                <div class="metric-label">زيارات معلقة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <div class="metric-number metric-danger">{{ rejected_visits }}</div>
                <div class="metric-label">زيارات مرفوضة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <div class="metric-number metric-secondary">{{ inactive_clients }}</div>
                <div class="metric-label">عملاء غير نشطين</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Visits Status Chart
const visitsCtx = document.getElementById('visitsStatusChart').getContext('2d');
new Chart(visitsCtx, {
    type: 'doughnut',
    data: {
        labels: ['مؤكدة', 'معلقة', 'مرفوضة'],
        datasets: [{
            data: [{{ verified_visits }}, {{ pending_visits }}, {{ rejected_visits }}],
            backgroundColor: [
                '#28a745',
                '#ffc107', 
                '#dc3545'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Monthly Chart
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
new Chart(monthlyCtx, {
    type: 'bar',
    data: {
        labels: ['الشهر الماضي', 'هذا الشهر'],
        datasets: [{
            label: 'العملاء',
            data: [{{ clients_last_month }}, {{ clients_this_month }}],
            backgroundColor: '#007bff',
            borderRadius: 5
        }, {
            label: 'الزيارات',
            data: [{{ visits_last_month }}, {{ visits_this_month }}],
            backgroundColor: '#28a745',
            borderRadius: 5
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

function exportStatistics() {
    // سيتم تطبيق وظيفة التصدير لاحقاً
    alert('وظيفة تصدير التقرير ستكون متاحة قريباً');
}
</script>
{% endblock %}
