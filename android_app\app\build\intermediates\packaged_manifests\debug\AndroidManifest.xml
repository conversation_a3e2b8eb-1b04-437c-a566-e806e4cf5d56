<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.company.fieldsalestracker"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="33" />

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <!-- Camera features -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <!-- Location features -->
    <uses-feature
        android:name="android.hardware.location"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.location.gps"
        android:required="false" />

    <queries>
        <intent>
            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
        </intent>
        <!-- Needs to be explicitly declared on Android R+ -->
        <package android:name="com.google.android.apps.maps" />
    </queries>

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <permission
        android:name="com.company.fieldsalestracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.company.fieldsalestracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.FieldSalesTracker"
        android:usesCleartextTraffic="true" >

        <!-- Splash Activity (Main Launcher) -->
        <activity
            android:name="com.company.fieldsalestracker.SplashActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.FieldSalesTracker.Splash" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Main Activity -->
        <activity
            android:name="com.company.fieldsalestracker.MainActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.FieldSalesTracker" />

        <!-- Login Activity -->
        <activity
            android:name="com.company.fieldsalestracker.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- My Visits Activity -->
        <activity
            android:name="com.company.fieldsalestracker.MyVisitsActivity"
            android:exported="false"
            android:parentActivityName="com.company.fieldsalestracker.MainActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- Tasks Activity -->
        <activity
            android:name="com.company.fieldsalestracker.TasksActivity"
            android:exported="false"
            android:parentActivityName="com.company.fieldsalestracker.MainActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- Task Details Activity -->
        <activity
            android:name="com.company.fieldsalestracker.TaskDetailsActivity"
            android:exported="false"
            android:parentActivityName="com.company.fieldsalestracker.TasksActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- Barcode Scanner Activity -->
        <activity
            android:name="com.company.fieldsalestracker.BarcodeScannerActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- Visit Details Activity -->
        <activity
            android:name="com.company.fieldsalestracker.VisitDetailsActivity"
            android:exported="false"
            android:parentActivityName="com.company.fieldsalestracker.MainActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- Client List Activity -->
        <activity
            android:name="com.company.fieldsalestracker.ClientListActivity"
            android:exported="false"
            android:parentActivityName="com.company.fieldsalestracker.MainActivity" />

        <!-- Enhanced Tasks Activities -->
        <activity
            android:name="com.company.fieldsalestracker.EnhancedTasksActivity"
            android:exported="false"
            android:parentActivityName="com.company.fieldsalestracker.MainActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
        <activity
            android:name="com.company.fieldsalestracker.EnhancedTaskDetailsActivity"
            android:exported="false"
            android:parentActivityName="com.company.fieldsalestracker.EnhancedTasksActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
        <activity
            android:name="com.company.fieldsalestracker.MultiTasksActivity"
            android:exported="false"
            android:parentActivityName="com.company.fieldsalestracker.MainActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />
        <activity
            android:name="com.company.fieldsalestracker.MultiTaskDetailsActivity"
            android:exported="false"
            android:parentActivityName="com.company.fieldsalestracker.MultiTasksActivity"
            android:theme="@style/Theme.FieldSalesTracker.NoActionBar" />

        <!-- File Provider for camera -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.company.fieldsalestracker.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- ML Kit metadata -->
        <meta-data
            android:name="com.google.mlkit.vision.DEPENDENCIES"
            android:value="barcode" />

        <activity
            android:name="pub.devrel.easypermissions.AppSettingsDialogHolderActivity"
            android:exported="false"
            android:label=""
            android:theme="@style/EasyPermissions.Transparent" />

        <uses-library
            android:name="androidx.camera.extensions.impl"
            android:required="false" />

        <service
            android:name="androidx.camera.core.impl.MetadataHolderService"
            android:enabled="false"
            android:exported="false" >
            <meta-data
                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
        </service>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.company.fieldsalestracker.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider> <!-- Needs to be explicitly declared on P+ -->
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <service
            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <provider
            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
            android:authorities="com.company.fieldsalestracker.mlkitinitprovider"
            android:exported="false"
            android:initOrder="99" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />
    </application>

</manifest>