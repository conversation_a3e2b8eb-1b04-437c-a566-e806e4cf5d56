"""
URL configuration for field_sales_tracker project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView
from rest_framework.routers import DefaultRouter
from users.views import UserViewSet
from users import views as user_views
from visits.views import VisitViewSet, my_tasks_api, acknowledge_task_api, start_task_api, clients_api
from visits import admin_views

# Create main router for API only
router = DefaultRouter()
router.register(r'api-users', UserViewSet)  # تغيير لتجنب التضارب
router.register(r'api-visits', VisitViewSet, basename='visit')

urlpatterns = [
    # Favicon
    path('favicon.ico', RedirectView.as_view(url=settings.STATIC_URL + 'favicon.ico', permanent=True)),

    # Admin reports (must be before admin/ to avoid conflicts)
    path('admin/reports/dashboard/',
         admin_views.visits_dashboard,
         name='visits_dashboard'),
    path('admin/reports/daily/',
         admin_views.daily_report,
         name='daily_report'),
    path('admin/reports/export/',
         admin_views.export_visits_csv,
         name='export_visits_csv'),
    path('admin/api/analytics/',
         admin_views.visits_analytics_api,
         name='visits_analytics_api'),

    # Web applications
    path('', include('dashboard.urls')),  # الصفحة الرئيسية
    path('visits/', include('visits.urls')),
    path('clients/', include('clients.urls')),
    path('users/', include('users.urls')),
    path('categories/', include('categories.urls')),

    # Django admin
    path('admin/', admin.site.urls),

    # Direct API endpoints for mobile app
    path('api/users/login/', user_views.mobile_login, name='api_mobile_login'),

    # Missing API endpoints for Android app (direct paths)
    path('api/visits/my-tasks/', my_tasks_api, name='api_my_tasks'),
    path('api/visits/<str:task_id>/acknowledge/', acknowledge_task_api, name='api_acknowledge_task'),
    path('api/visits/<str:task_id>/start/', start_task_api, name='api_start_task'),
    path('api/clients/', clients_api, name='api_clients'),

    # API endpoints
    path('api/', include(router.urls)),
    path('api-auth/', include('rest_framework.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
