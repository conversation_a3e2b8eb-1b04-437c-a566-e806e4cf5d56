package com.company.fieldsalestracker;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class MultiTaskAdapter extends RecyclerView.Adapter<MultiTaskAdapter.MultiTaskViewHolder> {
    
    private List<ApiService.MultiTask> tasks;
    private OnTaskClickListener clickListener;
    private OnExecuteTaskListener executeListener;
    
    public interface OnTaskClickListener {
        void onTaskClick(ApiService.MultiTask task);
    }
    
    public interface OnExecuteTaskListener {
        void onExecuteTask(ApiService.MultiTask task);
    }
    
    public MultiTaskAdapter(List<ApiService.MultiTask> tasks, 
                           OnTaskClickListener clickListener,
                           OnExecuteTaskListener executeListener) {
        this.tasks = tasks;
        this.clickListener = clickListener;
        this.executeListener = executeListener;
    }
    
    @NonNull
    @Override
    public MultiTaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_multi_task, parent, false);
        return new MultiTaskViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull MultiTaskViewHolder holder, int position) {
        ApiService.MultiTask task = tasks.get(position);
        holder.bind(task, clickListener, executeListener);
    }
    
    @Override
    public int getItemCount() {
        return tasks.size();
    }
    
    static class MultiTaskViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private TextView titleText;
        private TextView salesRepText;
        private TextView taskTypeText;
        private TextView statusText;
        private TextView progressText;
        private TextView nextExecutionText;
        private TextView executionsText;
        private ProgressBar progressBar;
        private ImageView taskTypeIcon;
        private ImageView statusIcon;
        private Button executeButton;
        private Button detailsButton;
        
        public MultiTaskViewHolder(@NonNull View itemView) {
            super(itemView);
            
            cardView = itemView.findViewById(R.id.cardView);
            titleText = itemView.findViewById(R.id.titleText);
            salesRepText = itemView.findViewById(R.id.salesRepText);
            taskTypeText = itemView.findViewById(R.id.taskTypeText);
            statusText = itemView.findViewById(R.id.statusText);
            progressText = itemView.findViewById(R.id.progressText);
            nextExecutionText = itemView.findViewById(R.id.nextExecutionText);
            executionsText = itemView.findViewById(R.id.executionsText);
            progressBar = itemView.findViewById(R.id.progressBar);
            taskTypeIcon = itemView.findViewById(R.id.taskTypeIcon);
            statusIcon = itemView.findViewById(R.id.statusIcon);
            executeButton = itemView.findViewById(R.id.executeButton);
            detailsButton = itemView.findViewById(R.id.detailsButton);
        }
        
        public void bind(ApiService.MultiTask task, 
                        OnTaskClickListener clickListener,
                        OnExecuteTaskListener executeListener) {
            
            // عنوان المهمة
            titleText.setText(task.task_title != null ? task.task_title : "مهمة متعددة");
            
            // المندوب
            if (task.sales_rep != null) {
                String repName = task.sales_rep.first_name != null && task.sales_rep.last_name != null
                    ? task.sales_rep.first_name + " " + task.sales_rep.last_name
                    : task.sales_rep.username;
                salesRepText.setText("المندوب: " + repName);
                salesRepText.setVisibility(View.VISIBLE);
            } else {
                salesRepText.setVisibility(View.GONE);
            }
            
            // نوع المهمة
            setupTaskType(task);
            
            // حالة المهمة
            setupTaskStatus(task);
            
            // التقدم
            setupProgress(task);
            
            // التنفيذ التالي
            setupNextExecution(task);
            
            // عدد التنفيذات
            setupExecutions(task);
            
            // أزرار الإجراءات
            setupActionButtons(task, clickListener, executeListener);
            
            // تلوين البطاقة
            setupCardColor(task);
        }
        
        private void setupTaskType(ApiService.MultiTask task) {
            if (task.is_recurring_task) {
                String recurrenceText = "مهمة متكررة";
                if (task.recurrence_type_display != null) {
                    recurrenceText += " (" + task.recurrence_type_display;
                    if (task.recurrence_interval > 1) {
                        recurrenceText += " كل " + task.recurrence_interval;
                    }
                    recurrenceText += ")";
                }
                taskTypeText.setText(recurrenceText);
                taskTypeIcon.setImageResource(R.drawable.ic_repeat);
            } else {
                taskTypeText.setText("مهمة لمرة واحدة");
                taskTypeIcon.setImageResource(R.drawable.ic_event);
            }
        }
        
        private void setupTaskStatus(ApiService.MultiTask task) {
            if (task.status_display != null) {
                statusText.setText(task.status_display);
            } else {
                statusText.setText("غير محدد");
            }
            
            // تلوين حسب الحالة
            switch (task.status != null ? task.status : "") {
                case "assigned":
                    statusText.setTextColor(Color.parseColor("#FFA500"));
                    statusIcon.setImageResource(R.drawable.ic_assignment);
                    break;
                case "in_progress":
                    statusText.setTextColor(Color.parseColor("#2196F3"));
                    statusIcon.setImageResource(R.drawable.ic_play_arrow);
                    break;
                case "completed":
                    statusText.setTextColor(Color.parseColor("#4CAF50"));
                    statusIcon.setImageResource(R.drawable.ic_check_circle);
                    break;
                default:
                    statusText.setTextColor(Color.parseColor("#757575"));
                    statusIcon.setImageResource(R.drawable.ic_help);
                    break;
            }
        }
        
        private void setupProgress(ApiService.MultiTask task) {
            if (task.child_visits_count > 0) {
                int progress = (int) task.success_rate;
                progressBar.setProgress(progress);
                
                String progressText = String.format("%d/%d زيارات مكتملة (%.1f%%)",
                    task.completed_child_visits_count,
                    task.child_visits_count,
                    task.success_rate);
                this.progressText.setText(progressText);
                
                progressBar.setVisibility(View.VISIBLE);
                this.progressText.setVisibility(View.VISIBLE);
            } else {
                progressBar.setVisibility(View.GONE);
                this.progressText.setVisibility(View.GONE);
            }
        }
        
        private void setupNextExecution(ApiService.MultiTask task) {
            if (task.next_execution != null && task.is_recurring_task) {
                try {
                    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault());
                    SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
                    
                    Date date = inputFormat.parse(task.next_execution);
                    if (date != null) {
                        String executionText = "التنفيذ التالي: " + outputFormat.format(date);
                        
                        // تحقق من الاستحقاق
                        if (task.is_due_for_execution) {
                            executionText += " (مستحقة الآن)";
                            nextExecutionText.setTextColor(Color.parseColor("#F44336")); // أحمر
                        } else if (date.before(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000))) {
                            executionText += " (قريباً)";
                            nextExecutionText.setTextColor(Color.parseColor("#FF9800")); // برتقالي
                        } else {
                            nextExecutionText.setTextColor(Color.parseColor("#757575")); // رمادي
                        }
                        
                        nextExecutionText.setText(executionText);
                        nextExecutionText.setVisibility(View.VISIBLE);
                    }
                } catch (ParseException e) {
                    nextExecutionText.setVisibility(View.GONE);
                }
            } else {
                nextExecutionText.setVisibility(View.GONE);
            }
        }
        
        private void setupExecutions(ApiService.MultiTask task) {
            if (task.is_recurring_task && task.total_executions > 0) {
                String executionsText = String.format("تم التنفيذ %d مرة | نجح %d",
                    task.total_executions, task.successful_executions);
                this.executionsText.setText(executionsText);
                this.executionsText.setVisibility(View.VISIBLE);
            } else {
                this.executionsText.setVisibility(View.GONE);
            }
        }
        
        private void setupActionButtons(ApiService.MultiTask task,
                                      OnTaskClickListener clickListener,
                                      OnExecuteTaskListener executeListener) {
            
            // زر التفاصيل
            detailsButton.setOnClickListener(v -> {
                if (clickListener != null) {
                    clickListener.onTaskClick(task);
                }
            });
            
            // زر التنفيذ (للمهام المتكررة المستحقة فقط)
            if (task.is_recurring_task && task.is_due_for_execution) {
                executeButton.setVisibility(View.VISIBLE);
                executeButton.setText("تنفيذ الآن");
                executeButton.setBackgroundColor(Color.parseColor("#4CAF50")); // أخضر
                executeButton.setOnClickListener(v -> {
                    if (executeListener != null) {
                        executeListener.onExecuteTask(task);
                    }
                });
            } else if (task.is_recurring_task) {
                executeButton.setVisibility(View.VISIBLE);
                executeButton.setText("غير مستحقة");
                executeButton.setBackgroundColor(Color.parseColor("#757575")); // رمادي
                executeButton.setEnabled(false);
            } else {
                executeButton.setVisibility(View.GONE);
            }
        }
        
        private void setupCardColor(ApiService.MultiTask task) {
            int borderColor = Color.parseColor("#E0E0E0"); // رمادي افتراضي
            
            if (task.is_recurring_task) {
                if (task.is_due_for_execution) {
                    borderColor = Color.parseColor("#F44336"); // أحمر للمستحقة
                } else {
                    borderColor = Color.parseColor("#4CAF50"); // أخضر للمتكررة
                }
            } else {
                borderColor = Color.parseColor("#2196F3"); // أزرق للمهام العادية
            }
            
            // يمكن إضافة تأثير الحدود هنا إذا كان التصميم يدعم ذلك
        }
    }
}
