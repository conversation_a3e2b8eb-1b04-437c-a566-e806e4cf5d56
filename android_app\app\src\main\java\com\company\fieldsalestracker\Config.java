package com.company.fieldsalestracker;

/**
 * ملف التكوين المركزي للتطبيق
 * يحتوي على جميع الإعدادات والعناوين المهمة
 * 
 * لتغيير عنوان الخادم، عدل فقط SERVER_IP أدناه
 */
public class Config {
    
    // ========================================
    // إعدادات الخادم - عدل هنا فقط!
    // ========================================
    
    /**
     * عنوان IP الخاص بالخادم
     * غير هذا العنوان فقط عند تغيير الخادم
     */
    public static final String SERVER_IP = "************";
//    public static final String SERVER_IP = "************";
//    public static final String SERVER_IP = "************";
//    public static final String SERVER_IP = "************";

    /**
     * منفذ الخادم (عادة 8000 لـ Django)
     */
//    public static final String SERVER_PORT = "8070";
    public static final String SERVER_PORT = "9000";

    /**
     * العنوان الأساسي الكامل للخادم
     * يتم بناؤه تلقائياً من SERVER_IP و SERVER_PORT
     */
    public static final String BASE_URL = "http://" + SERVER_IP + ":" + SERVER_PORT + "/";
    
    // ========================================
    // عناوين API المختلفة
    // ========================================
    
    // عناوين العملاء
    public static final String CLIENTS_API_URL = BASE_URL + "api/clients/";
    
    // عناوين الزيارات
    public static final String VISITS_API_URL = BASE_URL + "visits/api/mobile/";
    public static final String SCAN_BARCODE_URL = VISITS_API_URL + "scan-barcode/";
    public static final String SUBMIT_VISIT_URL = VISITS_API_URL + "submit-visit/";
    public static final String GET_VISITS_URL = VISITS_API_URL + "get-visits/";
    
    // عناوين المصادقة
    public static final String AUTH_API_URL = BASE_URL + "api/auth/";
    public static final String LOGIN_URL = AUTH_API_URL + "login/";
    public static final String LOGOUT_URL = AUTH_API_URL + "logout/";
    
    // ========================================
    // إعدادات التطبيق
    // ========================================
    
    // مهلة الاتصال (بالثواني)
    public static final int CONNECTION_TIMEOUT = 30;
    public static final int READ_TIMEOUT = 30;
    public static final int WRITE_TIMEOUT = 30;
    
    // إعدادات الموقع
    public static final double DEFAULT_RADIUS_KM = 10.0;
    public static final int LOCATION_UPDATE_INTERVAL = 5000; // 5 ثوان
    
    // إعدادات الصور
    public static final int MAX_IMAGE_SIZE_MB = 5;
    public static final int IMAGE_QUALITY = 85;
    
    // ========================================
    // رسائل التطبيق
    // ========================================
    
    public static final String NETWORK_ERROR_MESSAGE = "خطأ في الشبكة. تحقق من الاتصال بالإنترنت.";
    public static final String SERVER_ERROR_MESSAGE = "خطأ في الخادم. حاول مرة أخرى لاحقاً.";
    public static final String AUTH_ERROR_MESSAGE = "خطأ في المصادقة. يجب تسجيل الدخول مرة أخرى.";
    
    // ========================================
    // معلومات التطبيق
    // ========================================
    
    public static final String APP_NAME = "Field Sales Tracker";
    public static final String APP_VERSION = "1.0.0";
    public static final String USER_AGENT = APP_NAME + "/" + APP_VERSION;
    
    // ========================================
    // دوال مساعدة
    // ========================================
    
    /**
     * الحصول على عنوان API كامل
     * @param endpoint نقطة النهاية (مثل "clients/")
     * @return العنوان الكامل
     */
    public static String getApiUrl(String endpoint) {
        return BASE_URL + "api/" + endpoint;
    }
    
    /**
     * الحصول على عنوان زيارات API
     * @param endpoint نقطة النهاية (مثل "scan-barcode/")
     * @return العنوان الكامل
     */
    public static String getVisitsApiUrl(String endpoint) {
        return VISITS_API_URL + endpoint;
    }
    
    /**
     * طباعة معلومات التكوين للتشخيص
     */
    public static void printConfig() {
        System.out.println("=== تكوين التطبيق ===");
        System.out.println("عنوان الخادم: " + SERVER_IP);
        System.out.println("منفذ الخادم: " + SERVER_PORT);
        System.out.println("العنوان الأساسي: " + BASE_URL);
        System.out.println("إصدار التطبيق: " + APP_VERSION);
        System.out.println("==================");
    }
}
