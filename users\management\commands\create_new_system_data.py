from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import User, UserHierarchy
from categories.models import Category
from clients.models import Client
import uuid


class Command(BaseCommand):
    help = 'إنشاء البيانات التجريبية للنظام الجديد'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('بدء إنشاء البيانات التجريبية...'))
        
        with transaction.atomic():
            # إنشاء التصنيفات الافتراضية
            self.create_default_categories()
            
            # إنشاء المستخدمين
            self.create_users()
            
            # إنشاء العلاقات الهرمية
            self.create_hierarchies()
            
            # تحديث العملاء بالتصنيفات
            self.update_clients_with_categories()
        
        self.stdout.write(self.style.SUCCESS('تم إنشاء البيانات التجريبية بنجاح!'))

    def create_default_categories(self):
        """إنشاء التصنيفات الافتراضية"""
        self.stdout.write('إنشاء التصنيفات الافتراضية...')
        
        # إنشاء التصنيفات الافتراضية
        categories = Category.create_default_categories()
        
        self.stdout.write(f'تم إنشاء {len(categories)} تصنيف افتراضي')

    def create_users(self):
        """إنشاء المستخدمين التجريبيين"""
        self.stdout.write('إنشاء المستخدمين...')
        
        # حذف المستخدمين الموجودين (عدا superuser)
        User.objects.filter(is_superuser=False).delete()
        
        # 1. إنشاء مدير عام
        super_manager = User.create_super_manager(
            username='super_manager',
            email='<EMAIL>',
            password='admin123',
            first_name='أحمد',
            last_name='المدير العام',
            phone_number='+966501234567',
            employee_id='SM001'
        )
        
        # 2. إنشاء مدراء المناطق
        region_manager1 = User.create_user_manager(
            username='region_manager1',
            email='<EMAIL>',
            password='manager123',
            first_name='محمد',
            last_name='مدير الرياض',
            phone_number='+966501234568',
            employee_id='RM001'
        )
        
        region_manager2 = User.create_user_manager(
            username='region_manager2',
            email='<EMAIL>',
            password='manager123',
            first_name='سعد',
            last_name='مدير جدة',
            phone_number='+966501234569',
            employee_id='RM002'
        )
        
        # 3. إنشاء مدراء الفروع
        branch_manager1 = User.create_user_manager(
            username='branch_manager1',
            email='<EMAIL>',
            password='manager123',
            first_name='خالد',
            last_name='مدير فرع الرياض الشمالي',
            phone_number='+966501234570',
            employee_id='BM001'
        )
        
        branch_manager2 = User.create_user_manager(
            username='branch_manager2',
            email='<EMAIL>',
            password='manager123',
            first_name='عبدالله',
            last_name='مدير فرع الرياض الجنوبي',
            phone_number='+966501234571',
            employee_id='BM002'
        )
        
        # 4. إنشاء مناديب مبيعات
        sales_reps = []
        for i in range(1, 11):  # 10 مناديب
            rep = User.create_sales_rep(
                username=f'sales_rep{i}',
                email=f'rep{i}@company.com',
                password='rep123',
                first_name=f'مندوب{i}',
                last_name='المبيعات',
                phone_number=f'+96650123457{i}',
                employee_id=f'SR{i:03d}'
            )
            sales_reps.append(rep)
        
        # حفظ المستخدمين للاستخدام في الهرمية
        self.super_manager = super_manager
        self.region_managers = [region_manager1, region_manager2]
        self.branch_managers = [branch_manager1, branch_manager2]
        self.sales_reps = sales_reps
        
        self.stdout.write(f'تم إنشاء {1 + 2 + 2 + 10} مستخدم')

    def create_hierarchies(self):
        """إنشاء العلاقات الهرمية"""
        self.stdout.write('إنشاء العلاقات الهرمية...')
        
        # الحصول على التصنيفات
        region_mgr_category = Category.objects.get(code='REGION_MGR')
        branch_mgr_category = Category.objects.get(code='BRANCH_MGR')
        senior_reps_category = Category.objects.get(code='SENIOR_REPS')
        regular_reps_category = Category.objects.get(code='REGULAR_REPS')
        new_reps_category = Category.objects.get(code='NEW_REPS')
        
        # 1. المدير العام يدير تصنيف مدراء المناطق
        UserHierarchy.create_category_management(
            manager=self.super_manager,
            category=region_mgr_category
        )
        
        # 2. تعيين مدراء المناطق في تصنيفهم
        for manager in self.region_managers:
            manager.add_category(region_mgr_category.id)
        
        # 3. مدراء المناطق يديرون تصنيف مدراء الفروع
        for manager in self.region_managers:
            UserHierarchy.create_category_management(
                manager=manager,
                category=branch_mgr_category
            )
        
        # 4. تعيين مدراء الفروع في تصنيفهم
        for manager in self.branch_managers:
            manager.add_category(branch_mgr_category.id)
        
        # 5. مدراء الفروع يديرون تصنيفات المناديب
        for manager in self.branch_managers:
            UserHierarchy.create_category_management(
                manager=manager,
                category=senior_reps_category
            )
            UserHierarchy.create_category_management(
                manager=manager,
                category=regular_reps_category
            )
            UserHierarchy.create_category_management(
                manager=manager,
                category=new_reps_category
            )
        
        # 6. توزيع المناديب على التصنيفات
        # مناديب كبار (3)
        for rep in self.sales_reps[:3]:
            rep.add_category(senior_reps_category.id)
        
        # مناديب عاديين (4)
        for rep in self.sales_reps[3:7]:
            rep.add_category(regular_reps_category.id)
        
        # مناديب جدد (3)
        for rep in self.sales_reps[7:]:
            rep.add_category(new_reps_category.id)
        
        # 7. إنشاء بعض العلاقات المباشرة
        # المدير العام يدير مدير المنطقة الأول مباشرة
        self.super_manager.add_managed_user(self.region_managers[0].id)
        
        # مدير المنطقة الأول يدير مدير الفرع الأول مباشرة
        self.region_managers[0].add_managed_user(self.branch_managers[0].id)
        
        self.stdout.write('تم إنشاء العلاقات الهرمية')

    def update_clients_with_categories(self):
        """تحديث العملاء بالتصنيفات"""
        self.stdout.write('تحديث العملاء بالتصنيفات...')
        
        # الحصول على تصنيفات العملاء
        vip_category = Category.objects.get(code='VIP_CLIENTS')
        major_category = Category.objects.get(code='MAJOR_CLIENTS')
        regular_category = Category.objects.get(code='REGULAR_CLIENTS')
        
        # تحديث العملاء الموجودين
        clients = Client.objects.all()
        for i, client in enumerate(clients):
            if i % 3 == 0:
                # عميل VIP
                if not hasattr(client, 'category_id'):
                    client.category_id = str(vip_category.id)
            elif i % 3 == 1:
                # عميل كبير
                if not hasattr(client, 'category_id'):
                    client.category_id = str(major_category.id)
            else:
                # عميل عادي
                if not hasattr(client, 'category_id'):
                    client.category_id = str(regular_category.id)
            
            client.save()
        
        self.stdout.write(f'تم تحديث {clients.count()} عميل بالتصنيفات')
