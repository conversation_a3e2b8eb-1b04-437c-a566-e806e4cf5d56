// Generated by view binder compiler. Do not edit!
package com.company.fieldsalestracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.company.fieldsalestracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemEnhancedTaskBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView cardView;

  @NonNull
  public final TextView clientText;

  @NonNull
  public final TextView descriptionText;

  @NonNull
  public final TextView dueDateText;

  @NonNull
  public final ImageView priorityIcon;

  @NonNull
  public final TextView priorityText;

  @NonNull
  public final ImageView statusIcon;

  @NonNull
  public final TextView statusText;

  @NonNull
  public final ImageView taskTypeIcon;

  @NonNull
  public final TextView taskTypeText;

  @NonNull
  public final TextView titleText;

  private ItemEnhancedTaskBinding(@NonNull CardView rootView, @NonNull CardView cardView,
      @NonNull TextView clientText, @NonNull TextView descriptionText,
      @NonNull TextView dueDateText, @NonNull ImageView priorityIcon,
      @NonNull TextView priorityText, @NonNull ImageView statusIcon, @NonNull TextView statusText,
      @NonNull ImageView taskTypeIcon, @NonNull TextView taskTypeText,
      @NonNull TextView titleText) {
    this.rootView = rootView;
    this.cardView = cardView;
    this.clientText = clientText;
    this.descriptionText = descriptionText;
    this.dueDateText = dueDateText;
    this.priorityIcon = priorityIcon;
    this.priorityText = priorityText;
    this.statusIcon = statusIcon;
    this.statusText = statusText;
    this.taskTypeIcon = taskTypeIcon;
    this.taskTypeText = taskTypeText;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemEnhancedTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemEnhancedTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_enhanced_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemEnhancedTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      CardView cardView = (CardView) rootView;

      id = R.id.clientText;
      TextView clientText = ViewBindings.findChildViewById(rootView, id);
      if (clientText == null) {
        break missingId;
      }

      id = R.id.descriptionText;
      TextView descriptionText = ViewBindings.findChildViewById(rootView, id);
      if (descriptionText == null) {
        break missingId;
      }

      id = R.id.dueDateText;
      TextView dueDateText = ViewBindings.findChildViewById(rootView, id);
      if (dueDateText == null) {
        break missingId;
      }

      id = R.id.priorityIcon;
      ImageView priorityIcon = ViewBindings.findChildViewById(rootView, id);
      if (priorityIcon == null) {
        break missingId;
      }

      id = R.id.priorityText;
      TextView priorityText = ViewBindings.findChildViewById(rootView, id);
      if (priorityText == null) {
        break missingId;
      }

      id = R.id.statusIcon;
      ImageView statusIcon = ViewBindings.findChildViewById(rootView, id);
      if (statusIcon == null) {
        break missingId;
      }

      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      id = R.id.taskTypeIcon;
      ImageView taskTypeIcon = ViewBindings.findChildViewById(rootView, id);
      if (taskTypeIcon == null) {
        break missingId;
      }

      id = R.id.taskTypeText;
      TextView taskTypeText = ViewBindings.findChildViewById(rootView, id);
      if (taskTypeText == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new ItemEnhancedTaskBinding((CardView) rootView, cardView, clientText, descriptionText,
          dueDateText, priorityIcon, priorityText, statusIcon, statusText, taskTypeIcon,
          taskTypeText, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
