<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <!-- Task Type Icon -->
            <ImageView
                android:id="@+id/taskTypeIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_task"
                android:layout_marginEnd="8dp" />

            <!-- Title -->
            <TextView
                android:id="@+id/titleText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="عنوان المهمة"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black" />

            <!-- Status Icon -->
            <ImageView
                android:id="@+id/statusIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_help"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- Client -->
        <TextView
            android:id="@+id/clientText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="العميل"
            android:textSize="14sp"
            android:textColor="@color/colorPrimary"
            android:layout_marginBottom="4dp"
            android:drawableStart="@drawable/ic_business"
            android:drawablePadding="8dp" />

        <!-- Task Type -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp">

            <TextView
                android:id="@+id/taskTypeText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="نوع المهمة"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray" />

            <!-- Priority Icon -->
            <ImageView
                android:id="@+id/priorityIcon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_priority_medium"
                android:layout_marginEnd="4dp" />

            <TextView
                android:id="@+id/priorityText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="متوسط"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- Status -->
        <TextView
            android:id="@+id/statusText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="الحالة"
            android:textSize="14sp"
            android:layout_marginBottom="4dp" />

        <!-- Due Date -->
        <TextView
            android:id="@+id/dueDateText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="تاريخ الاستحقاق"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginBottom="8dp"
            android:drawableStart="@drawable/ic_schedule"
            android:drawablePadding="8dp" />

        <!-- Description -->
        <TextView
            android:id="@+id/descriptionText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="وصف المهمة"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginBottom="8dp"
            android:maxLines="2"
            android:ellipsize="end" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
