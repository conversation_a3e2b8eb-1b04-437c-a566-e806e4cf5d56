from django.urls import path, include
from django.shortcuts import render
from rest_framework.routers import Default<PERSON>outer
from .views import (
    VisitViewSet,
    visits_list,
    visits_search,
    pending_visits,
    my_visits,
    add_visit,
    mobile_scan_barcode,
    mobile_submit_visit,
    mobile_get_visits,
    mobile_my_visits,
    get_visit_details,
    # Task management views
    task_management,
    create_task,
    get_subordinates,
    task_details,
    update_task_status,
    tasks_api,
    # Enhanced task management views
    tasks_management,
    visits_statistics,
    enhanced_visits_management,
    create_unified_task,
    multi_tasks_management,

    # Enhanced API views for Android
    enhanced_visits_api,
    multi_tasks_api,
    child_visits_api,
    execute_task_api,
    complete_task_api,

    # Missing API views for Android app
    my_tasks_api,
    acknowledge_task_api,
    start_task_api,
    clients_api,
    visit_details_api
)

app_name = 'visits'

router = DefaultRouter()
router.register(r'visits', VisitViewSet, basename='visit')

urlpatterns = [
    # Web views
    path('', visits_list, name='visits_list'),
    path('search/', visits_search, name='visits_search'),  # للبحث المباشر
    path('pending/', pending_visits, name='pending_visits'),
    path('my-visits/', my_visits, name='my_visits'),
    path('add/', add_visit, name='add_visit'),

    # Task management
    path('tasks/', task_management, name='task_management'),
    path('tasks/create/', create_task, name='create_task'),
    path('tasks/<uuid:task_id>/', task_details, name='task_details'),
    path('tasks/<uuid:task_id>/update-status/', update_task_status, name='update_task_status'),
    path('api/subordinates/', get_subordinates, name='get_subordinates'),
    path('api/tasks/', tasks_api, name='tasks_api'),

    # Enhanced task management
    path('tasks-management/', tasks_management, name='tasks_management'),
    path('create-task/', create_unified_task, name='create_unified_task'),
    path('statistics/', visits_statistics, name='visits_statistics'),
    path('enhanced-management/', enhanced_visits_management, name='enhanced_visits_management'),
    path('multi-tasks/', multi_tasks_management, name='multi_tasks_management'),

    # Mobile API endpoints
    path('api/mobile/scan-barcode/', mobile_scan_barcode, name='mobile_scan_barcode'),
    path('api/mobile/submit-visit/', mobile_submit_visit, name='mobile_submit_visit'),
    path('api/mobile/get-visits/', mobile_get_visits, name='mobile_get_visits'),
    path('api/mobile/my-visits/', mobile_my_visits, name='mobile_my_visits'),
    path('test-mobile-visits/', lambda request: render(request, 'test_mobile_visits.html'), name='test_mobile_visits'),

    # Visit details API
    path('api/visit/<int:visit_id>/', get_visit_details, name='get_visit_details'),
    path('api/visit-details/<str:visit_id>/', visit_details_api, name='visit_details_api'),

    # Enhanced API endpoints for Android
    path('api/visits/enhanced-management/', enhanced_visits_api, name='enhanced_visits_api'),
    path('api/visits/multi-tasks/', multi_tasks_api, name='multi_tasks_api'),
    path('api/visits/<str:task_id>/child-visits/', child_visits_api, name='child_visits_api'),
    path('api/visits/<str:task_id>/execute/', execute_task_api, name='execute_task_api'),
    path('api/visits/<str:task_id>/complete/', complete_task_api, name='complete_task_api'),

    # Missing API endpoints for Android app
    path('api/visits/my-tasks/', my_tasks_api, name='my_tasks_api'),
    path('api/visits/<str:task_id>/acknowledge/', acknowledge_task_api, name='acknowledge_task_api'),
    path('api/visits/<str:task_id>/start/', start_task_api, name='start_task_api'),
    path('api/clients/', clients_api, name='clients_api'),

    # API endpoints
    path('api/', include(router.urls)),
]
